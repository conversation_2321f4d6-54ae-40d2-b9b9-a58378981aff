import { MeetingReqModel } from '../common/model'
import { Participant } from '../models'

@CustomDialog
export struct ParticipantSetDialog {
  dialogController: CustomDialogController
  //点击的对象信息
  @Prop participant: Participant
  //用户自己的信息
  @Prop userInfo: Participant
  onclick: (index: number) => void = () => {
  }

  build() {
    Column() {

      Row() {
        Text(this.participant.nickname)
        Image('')
      }
      .width('100%')
      .padding(15)
      .justifyContent(FlexAlign.SpaceBetween)

      Column() {
        // Text('被点击' + this.participant.uid?.toString())
        // Text('我自己' + this.userInfo.uid?.toString())
        if (this.participant.uid === this.userInfo.uid) {
          this.optionItemBuilder(0, this.participant.audioOpen ? '静音' : '打开音频', '')
          this.optionItemBuilder(1, this.participant.videoOpen ? '关闭视频' : '打开视频', '')
        } else if (this.userInfo.role === 1) {
          this.optionItemBuilder(0, this.participant.audioOpen ? '静音' : '打开音频', '')
          this.optionItemBuilder(1, this.participant.videoOpen ? '关闭视频' : '打开视频', '')
          this.optionItemBuilder(2, '设为主持人', '')
          this.optionItemBuilder(3, '私聊', '')
        }
      }
      .width('100%')
      .backgroundColor(Color.White)
      .borderRadius(10)

      Text()
        .width(1)
        .height(10)

      if (this.userInfo.role === 1 && this.participant.uid !== this.userInfo.uid) {
        Row() {
          Text('移出会议')
            .fontColor(Color.Red)
          Image('')
        }
        .width('100%')
        .padding(15)
        .borderRadius(10)
        .justifyContent(FlexAlign.SpaceBetween)
        .backgroundColor(Color.White)
        .onClick(() => {
          this.onclick(4)
          this.dialogController.close()
        })
      }

    }
    .width('100%')
    .padding(15)
    .backgroundColor('#fff5f5f5')

  }

  @Builder
  optionItemBuilder(index: number, text: string, icon: ResourceStr) {
    Row() {
      Text(text)
      Image(icon)
    }
    .width('100%')
    .padding(15)
    .justifyContent(FlexAlign.SpaceBetween)
    .onClick(() => {
      this.onclick(index)
      this.dialogController.close()
    })
  }
}


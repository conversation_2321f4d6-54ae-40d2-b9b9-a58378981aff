import { RouterModule, RouterNameConstants, BuilderNameConstants, builderRouterModel } from 'routermoudel';
import { MainHome } from '../views/MainHome';
import { loginApi } from 'basic';
import { emitter } from '@kit.BasicServicesKit';

// 定义路由参数接口
interface RouterParams {
  origin: string;
}

// import { MainPage } from 'library'
@Entry
@Component
struct Index {
  @State
  pageInfo: NavPathStack = new NavPathStack()
  @State
  showMainHome: boolean = false

  aboutToAppear() {
    if(!this.pageInfo){
      this.pageInfo = new NavPathStack()
    }
    RouterModule.craterRouter(RouterNameConstants.HomeIndex,this.pageInfo)

    // 注册登录状态变化事件监听
    this.registerLoginStatusListener();

    // 检查登录状态
    this.checkLoginStatus()
  }

  aboutToDisappear() {
    // 取消事件监听
    emitter.off('loginStatusChanged');
  }

  // 注册登录状态变化事件监听
  private registerLoginStatusListener(): void {
    emitter.on('loginStatusChanged', () => {
      console.info('收到登录状态变化事件，重新检查登录状态');
      this.checkLoginStatus();
    });
  }

  // 检查用户登录状态
  private async checkLoginStatus(): Promise<void> {
    try {
      // 确保用户信息已初始化
      const currentUser = loginApi.getCurrentUser();
      await currentUser.ensureInitialized();

      console.info('检查用户登录状态:', {
        userId: currentUser.userId,
        userName: currentUser.userName,
        hasToken: currentUser.token !== '',
        isValidUser: currentUser.isValidUser(),
        isLoggedIn: currentUser.isLoggedIn(),
        isAnonymousUser: currentUser.isAnonymousUser()
      });

      // 检查是否所有字段都是默认值（判定为未登录用户）
      const isDefaultUser = (
        currentUser.userId === 0 &&
        currentUser.userName === '' &&
        currentUser.realName === '' &&
        currentUser.nickName === '' &&
        currentUser.roles === '' &&
        currentUser.token === '' &&
        currentUser.email === ''
      );

      if (isDefaultUser) {
        console.info('用户信息为默认值，跳转到登录页面');
        this.navigateToLogin();
        return;
      }

      // 如果用户已登录（包括普通用户和匿名用户），显示主页
      if (currentUser.isValidUser()) {
        console.info('用户已登录，显示主页');
        this.showMainHome = true;
      } else {
        console.info('用户未登录，跳转到登录页面');
        this.navigateToLogin();
      }
    } catch (error) {
      console.error('检查用户登录状态时发生错误:', error);
      // 发生错误时跳转到登录页面
      this.navigateToLogin();
    }
  }

  // 跳转到登录页面
  private navigateToLogin(): void {
    console.info('导航到登录页面');
    // 延迟一下确保路由初始化完成
    setTimeout(() => {
      const routeParams: RouterParams = {
        origin: 'startup'
      };
      builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.LoginPage, routeParams);
    }, 100);
  }

  @Builder
  routerMap(builderName:string,param:object){
    RouterModule.getBuilder(builderName).builder(param)
  }

  build() {
    Navigation(this.pageInfo){
      if (this.showMainHome) {
        MainHome()
      }
    }
    .hideToolBar(true)
    .hideTitleBar(true)
    .hideBackButton(true)
    .navDestination(this.routerMap)
    .mode(NavigationMode.Stack)
  }
}
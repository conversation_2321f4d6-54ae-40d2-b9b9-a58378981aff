import testNapi from 'libhstinterface.so';
import { callbackManager, CallBackManager } from '../callback/CallBackManager';
import { GetSelfID } from '../conference';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { BusinessType } from '../../Constant';
import player from 'libplayer.so'

export type VideoDeviceUpdateCallbackFn = (nUserId: number, nChannelId: number, bRemved: boolean,
  strName: string, nCodeType: number, userId?: number) => void

export type VideoCloseCallbackFn = (nChannelId: number, bReleaseDecoder: boolean) => void

export type VideoOnNewVideoCallbackFn = (nUserId: number, nChannelId: number) => void

export let g_nChannelId: number = 0;


type audioOpenCallbackFun = (bOpen: boolean) => void

function OpenVideo(bOpen: boolean) {
  console.log('iiii OpenVideo', bOpen)
  let handlers = callbackManager.trigger(BusinessType.VIDEO_ON_OPEN_VIDEO)
  if (handlers) {
    handlers(bOpen)
  }
}

function handleOnDeviceUpdate(nUserId: number, nChannelId: number, bRemove: boolean, strName: string,
  nCodeType: number) {

  let n: number = GetSelfID()
  let handlers = callbackManager.trigger(BusinessType.VIDEO_ON_DEVICE_UPDATE)
  let channelIdArr = callbackManager.getChannelId()

  if (channelIdArr.findIndex(i => i === nChannelId) === -1) {
    callbackManager.setChannelId(nChannelId)
    console.log('所有人的channelId', callbackManager.getChannelId())
  }

  if (n === nUserId) {
    if (handlers) {
      handlers(nChannelId, nChannelId, bRemove, strName, nCodeType,
        n)
    }
  } else {
    if (handlers) {
      handlers(nChannelId, nChannelId, bRemove, strName,
        nCodeType)
    }
  }
}

//type:true 为开启 false为关闭
export function changeOpenVideo(channelId: number, type: boolean) {
  console.log('channelId++', channelId, type)
  testNapi.video_OpenVideo(channelId);
  testNapi.video_SetVideoSync(channelId, type);
}

function video_onNewVideo(nUserId: number, nChannelId: number): void {
  console.log('video video_onNewVideo', nUserId, nChannelId)
  let handlers = callbackManager.trigger(BusinessType.VIDEO_ON_NEW_VIDEO)
  g_nChannelId = GetSelfID()
  // if (g_nChannelId == nChannelId) {
  //   testNapi.video_OpenVideo(nChannelId);
  // }
  testNapi.video_OpenVideo(nChannelId);

  if (handlers) {
    handlers(nUserId, nChannelId)
  }
}

function video_onVideoClose(nChannelId: number, bReleaseDecoder: boolean) {
  console.log('video video_onVideoClose', nChannelId, bReleaseDecoder)
  let handlers = callbackManager.trigger(BusinessType.VIDEO_ON_VIDEO_CLOSE)
  if (handlers) {
    handlers(nChannelId, bReleaseDecoder)
  }
  testNapi.video_CloseVideo(nChannelId)
}

export const addVideoDeviceUpdateCallback = (callback: VideoDeviceUpdateCallbackFn) => {
  callbackManager.register(BusinessType.VIDEO_ON_DEVICE_UPDATE, callback)
}

export const addVideoClose = (callback: VideoCloseCallbackFn) => {
  callbackManager.register(BusinessType.VIDEO_ON_VIDEO_CLOSE, callback)
}

export const addVideoOnNewVideo = (callback: VideoOnNewVideoCallbackFn) => {
  callbackManager.register(BusinessType.VIDEO_ON_NEW_VIDEO, callback)
}

export function addOpenVideoCallback(callback: audioOpenCallbackFun) {
  callbackManager.register(BusinessType.VIDEO_ON_OPEN_VIDEO, callback)
}

export function removeOpenVideoCallback() {
  callbackManager.unregister(BusinessType.VIDEO_ON_OPEN_VIDEO)
}

export const removeVideoDeviceUpdateCallback = (callback: VideoDeviceUpdateCallbackFn) => {
  callbackManager.unregister(BusinessType.VIDEO_ON_DEVICE_UPDATE)
}

export const removeVideoClose = (callback: VideoCloseCallbackFn) => {
  callbackManager.unregister(BusinessType.VIDEO_ON_VIDEO_CLOSE)
}

export const removeVideoOnNewVideo = (callback: VideoOnNewVideoCallbackFn) => {
  callbackManager.unregister(BusinessType.VIDEO_ON_NEW_VIDEO)
}


function InitializeMyVideo() {

}

function SendMyVideoData() {

}

function CloseMyVideo() {

}

function OpenMyVideo() {

}

function SetH323SupportFlag() {

}

function SetH323View() {

}


export function video_registerCallback() {
  testNapi.registerCallback("video_onNewVideo", video_onNewVideo);
  testNapi.registerCallback("video_onDeviceUpdate", handleOnDeviceUpdate);
  testNapi.registerCallback("video_onVideoClose", video_onVideoClose);
  testNapi.registerCallback("video_onVideoClose", video_onVideoClose);
  testNapi.registerCallback('video_onOpenVideo', OpenVideo)

  callbackManager.initIsFailed = true
}

export function video_unregisterCallback() {
  testNapi.unregisterCallback("video_onDeviceUpdate", handleOnDeviceUpdate)
  testNapi.unregisterCallback("video_onNewVideo", video_onNewVideo);
  testNapi.unregisterCallback("video_onVideoClose", video_onVideoClose);
  testNapi.unregisterCallback('video_onOpenVideo', OpenVideo)
}


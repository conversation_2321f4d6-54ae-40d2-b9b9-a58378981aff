// 会议列表项接口
export interface MeetingItem {
  meetingId: string;      // 会议ID
  title: string;          // 会议标题
  startTime: string;      // 开始时间
  endTime: string;        // 结束时间
  host: string;           // 主持人
  password?: string;      // 会议密码（可选）
  status?: number;        // 会议状态
}

// // 会议列表响应接口
export interface MeetingListResponse {
  code: number;           // 响应代码
  message: string;        // 响应消息
  data: MeetingListData
}

export interface MeetingListData {
  list: MeetingItem[];  // 会议列表
  total: number;        // 总数
}

// 会议列表请求参数接口
export interface MeetingListParams {
  siteName: string;     // 站点名称
  siteId: string;       // 站点ID
  userId: number;       // 用户ID
  userName: string;     // 用户名
  os: string;           // 操作系统标识
  pageNum: number;      // 页码
  pageSize: number;     // 每页显示数量
}
{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"basic@../../commons/basic": "basic@../../commons/basic", "routermoudel@../../commons/RouterMoudel": "routermoudel@../../commons/RouterMoudel"}, "packages": {"basic@../../commons/basic": {"name": "basic", "version": "1.0.0", "resolved": "../../commons/basic", "registryType": "local", "packageType": "InterfaceHar"}, "routermoudel@../../commons/RouterMoudel": {"name": "routermoudel", "version": "1.0.0", "resolved": "../../commons/RouterMoudel", "registryType": "local"}}}
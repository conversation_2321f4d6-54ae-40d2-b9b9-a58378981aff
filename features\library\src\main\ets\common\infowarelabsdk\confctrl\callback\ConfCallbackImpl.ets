import { ConfCallback } from './ConfCallBack';

export class ConfCallbackImpl implements ConfCallback {
  onJoinConference: (result: number) => number;
  onConferenceLeave: (result: number) => number;
  onRosterUpdate: (action: number, uid: number, name: string, role: number) => void;
  onUserRole: (uid: number, newRole: number) => void;
  onUpdateUserPriviledge: (type: number, state: boolean) => void;
  onRemoveUser: (action: number, uid: number) => void;
  onBeginRecordFailed: (isBegin: number) => void;
  onRecordNotify: (isBegin: boolean) => void;
  onInvitePhoneConfirm: (phoneNum: string, isSuccess: boolean) => void;
  onCallAtt: (response: boolean, timeout: number, id: number) => void;
  onTransparentRecvData: (data: number[], length: number) => void;
  onRecordStateResponse: (state: number, isRecording: boolean) => void;
  onRecordStopRequest: (srcuserid: number, dstuserid: number) => void;
  onSubtitles: (bShow: boolean, strText: string) => void;
  onFirstJoin: (bFirst: boolean) => void;
  onInviteH323Response: (arg1: number, arg2: string, arg3: string, arg4: string) => void;
  onConferenceSupportH323: (arg1: boolean, arg2: number, agr3: number, arg4: number, agr5: number,
    arg6: number) => void;
  onConferenceSupportH3231: (arg1: boolean, arg2: number) => void;
  onInviteH3231Response: (arg2: number) => void;
  onUserViewState: (type: number) => void;
  OnLiveState: (type: number) => void;
  onAsPriviledge: (bAs: number) => void;
  onVideoAdaptive: (bVideoAdaptive: boolean) => void;
  onConfAvOpenConfirm: (bAvOpenConfirm: boolean) => void;
  onConfMode: (bFreeMode: boolean) => void;
  OnConfFlag: (flag: number) => void;
  onNetworkSpeed: (nSpeed: number, nGoodSpeed: number, nBadSpeed: number) => void;
  onHandUp: (userid: number, handUp: boolean) => void;
  onRequest: (type: number, userId: number, channelId: number, data1: number, data2: number, strData: string) => void;
  onResponse: (type: number, userId: number, channelId: number, data1: number, data2: number, strData: string) => void;

  constructor() {
    this.onJoinConference = (result:number) => {
      return result
    }

    this.onConferenceLeave = (result:number) =>{
      return result
    }

    this.onRosterUpdate = (action: number, uid: number, name: string, role: number) =>{

    }

    this.onUserRole = (uid: number, newRole: number) =>{

    }

    this.onUpdateUserPriviledge = (type: number, state: boolean) =>{

    }

    this.onRemoveUser = (actio: number, uid: number) => {}

    this.onBeginRecordFailed = (isBegin: number) => {}

    this.onRecordNotify = (isBegin: boolean) => {}

    this.onInvitePhoneConfirm = (phoneNum: string, isSuccess: boolean) => {}

    this.onCallAtt = (response: boolean,timeout: number, id: number) => {}

    this.onTransparentRecvData = (data: number[], length: number) => {}

    this.onRecordStateResponse = (state: number, isRecording : boolean) => {}

    this.onRecordStopRequest = (srcuserid: number, dstuserid: number) => {}

    this.onSubtitles =  (bShow: boolean, strText: string) => {};

    this.onFirstJoin = (bFirst: boolean) => {};

    this.onInviteH323Response = (arg1: number, arg2: string, arg3: string, arg4: string) => {}

    this.onConferenceSupportH323 = (arg1: boolean, arg2: number, agr3: number, arg4: number, agr5: number,
      arg6: number) => {};

    this.onConferenceSupportH3231 = (arg1: boolean, arg2: number) => {}

    this.onInviteH3231Response = (arg2: number) => {};

    this.onUserViewState =  (type: number) => {};

    this.OnLiveState = (type: number) => {};

    this.onAsPriviledge =  (bAs: number) => {};

    this.onVideoAdaptive = (bVideoAdaptive: boolean) => {};

    this.onConfAvOpenConfirm = (bAvOpenConfirm: boolean) => {};

    this.onConfMode = (bFreeMode: boolean) => {};

    this.OnConfFlag = (flag: number) => {};

    this.onNetworkSpeed = (nSpeed: number, nGoodSpeed: number, nBadSpeed: number) => {};

    this.onHandUp = (userid: number, handUp: boolean) => {};

    this.onRequest = (type: number, userId: number, channelId: number, data1: number, data2: number, strData: string) => {};

    this.onResponse = (type: number, userId: number, channelId: number, data1: number, data2: number, strData: string) => {};

  }
}
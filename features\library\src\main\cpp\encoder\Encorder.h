#ifndef VIDEO_CODEC_SAMPLE_RECODER_H
#define VIDEO_CODEC_SAMPLE_RECODER_H

#include <thread>
#include <mutex>
#include <memory>
#include <atomic>
#include <condition_variable>

#include "capbilities/include/VideoEncoder.h"
#include "common/SampleInfo.h"

class Encoder {
public:
    Encoder(){};
    ~Encoder();

    static Encoder &GetInstance() {
        static Encoder recorder;
        return recorder;
    }

    int32_t Init(SampleInfo &sampleInfo);
    int32_t Start();
    int32_t Stop();

private:
    void EncOutputThread();
    void StartRelease();
    void Release();
    
    std::unique_ptr<VideoEncoder> videoEncoder_ = nullptr;
    std::mutex mutex_;
    std::atomic<bool> isStarted_{false};
    std::unique_ptr<std::thread> encOutputThread_ = nullptr;
    std::unique_ptr<std::thread> releaseThread_ = nullptr;
    std::condition_variable doneCond_;
    SampleInfo sampleInfo_;
    CodecUserData *encContext_ = nullptr;
};

#endif // VIDEO_CODEC_SAMPLE_RECODER_H
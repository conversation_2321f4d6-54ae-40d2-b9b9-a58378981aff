import testNapi from 'libhstinterface.so'
import { ANTINFO } from '../../../models';
import { BusinessType } from '../../Constant';
import { callbackManager } from '../callback/CallBackManager';
import capture from 'libcapture.so'

type NewAntCallbackFun = (ant: ANTINFO) => void
type ShareAsAllCallbackFun = (nDocId: number, strDocTitle: string) => void
type closeAsCallbackFun = (nDocID: number) => void
type AsOnCallbackFun = (nDocId: number, nPageId: number, data: ArrayBuffer, nPageWidth: number, nPageHeight: number,
  bNDF: boolean) => void
type RemoveAntCallbackFun = (nDocId: number, nPageId: number, nAntId: number) => void
type RemoveAllAntCallbackFun = (nDocId: number, nPageId: number) => void

function ds_onNewAnt(ant: ANTINFO) {
  console.log('ds_onNewAnt', JSON.stringify(ant))
  let handlers = callbackManager.trigger(BusinessType.DS_ON_NEW_ANT)
  if (handlers) {
    handlers(ant)
  }
}

//查看所有白板
function ds_onShareDoc(nDocId: number, nDocType: number, strDocTitle: string, nDocPages: number) {
  console.log('画布id', nDocId, nDocType, strDocTitle, nDocPages)
  let handlers = callbackManager.trigger(BusinessType.DS_ON_SHARE_DOC)
  if (handlers) {
    handlers(nDocId, strDocTitle)
  }
}

function ds_onSwitchDoc() {

}

//关闭画布
function ds_onCloseDoc(nDocId: number) {
  console.log('关闭画布', nDocId)
  let handlers = callbackManager.trigger(BusinessType.DS_ON_CLOSE_DOC)
  if (handlers) {
    handlers(nDocId)
  }
}

function ds_onNewPage(nDocId: number, nPageId: number, nPageWidth: number, nPageHeight: number) {
  //console.log('新画布',nDocId,nPageId,nPageWidth,nPageHeight)

}

function ds_onNewPageStep(nDocId: number, nPageId: number, nPageWidth: number, nPageHeight: number,
  nPageStepID: number) {
  //console.log('画布切换',nDocId,nPageId,nPageWidth,nPageHeight,nPageId)
}

function ds_onSwitchPage() {

}

//查看当前切换白板
function ds_onPageData(nDocId: number, nPageId: number, data: ArrayBuffer, nPageWidth: number, nPageHeight: number,
  bNDF: boolean) {
  console.log('画布数据', nDocId, nPageId, JSON.stringify(data), nPageWidth, nPageHeight, bNDF)
  let handlers = callbackManager.trigger(BusinessType.DS_ON_PAGE_DATA)
  if (handlers) {
    handlers(nDocId, nPageId, data, nPageWidth, nPageHeight, bNDF)
  }
}

function ds_onPageStepData() {

}

function ds_onSwitchPageStep() {

}

function ds_onMovePage() {

}

function ds_onRemoveAnt(nDocId: number, nPageId: number, nAntId: number) {
  let handlers = callbackManager.trigger(BusinessType.DS_ON_REMOVE_ANT)
  if (handlers) {
    handlers(nDocId, nPageId, nAntId)
  }
}

function ds_onRemoveAllAnt(nDocId: number, nPageId: number) {
  let handlers = callbackManager.trigger(BusinessType.DS_ON_REMOVE_ALL_ANT)
  if (handlers) {
    handlers(nDocId, nPageId)
  }
}

export function addNewAntCallback(callback: NewAntCallbackFun) {
  callbackManager.register(BusinessType.DS_ON_NEW_ANT, callback)
}

export function addAllShareAsCallback(callback: ShareAsAllCallbackFun) {
  callbackManager.register(BusinessType.DS_ON_SHARE_DOC, callback)
}

export function addCloseShareAsCallback(callback: closeAsCallbackFun) {
  callbackManager.register(BusinessType.DS_ON_CLOSE_DOC, callback)
}

export function addShareOnASCallback(callback: AsOnCallbackFun) {
  callbackManager.register(BusinessType.DS_ON_PAGE_DATA, callback)
}

//DS_ON_REMOVE_ANT
export function addRemoveAntCallback(callback: RemoveAntCallbackFun) {
  callbackManager.register(BusinessType.DS_ON_REMOVE_ANT, callback)
}

export function addRemoveAllAntCallback(callback: RemoveAllAntCallbackFun) {
  callbackManager.register(BusinessType.DS_ON_REMOVE_ALL_ANT, callback)
}


export function removeNewAntCallback() {
  callbackManager.unregister(BusinessType.DS_ON_NEW_ANT)
}

export function removeAllShareAsCallback() {
  callbackManager.unregister(BusinessType.DS_ON_SHARE_DOC)
}

export function removeCloseShareAsCallback() {
  callbackManager.unregister(BusinessType.DS_ON_CLOSE_DOC)
}

export function removeShareOnASCallback() {
  callbackManager.unregister(BusinessType.DS_ON_PAGE_DATA)
}

export function removeRemoveAntCallback() {
  callbackManager.unregister(BusinessType.DS_ON_REMOVE_ANT)
}

export function removeRemoveAllAntCallback() {
  callbackManager.unregister(BusinessType.DS_ON_REMOVE_ALL_ANT)
}

export function ds_registerCallback() {
  testNapi.registerCallback("ds_onNewAnt", ds_onNewAnt);
  testNapi.registerCallback("ds_onShareDoc", ds_onShareDoc);
  testNapi.registerCallback("ds_onSwitchDoc", ds_onSwitchDoc);
  testNapi.registerCallback("ds_onCloseDoc", ds_onCloseDoc);
  testNapi.registerCallback("ds_onNewPage", ds_onNewPage);
  testNapi.registerCallback("ds_onNewPageStep", ds_onNewPageStep);
  testNapi.registerCallback("ds_onSwitchPage", ds_onSwitchPage);
  testNapi.registerCallback("ds_onPageData", ds_onPageData);
  testNapi.registerCallback("ds_onPageStepData", ds_onPageStepData);
  testNapi.registerCallback("ds_onSwitchPageStep", ds_onSwitchPageStep);
  testNapi.registerCallback("ds_onMovePage", ds_onMovePage);
  testNapi.registerCallback("ds_onRemoveAnt", ds_onRemoveAnt);
  // testNapi.registerCallback("ds_onRemoveMyAnt", (nDocId: number, nPageId: number, nUserId: number) => {
  //   console.log('iiii 移出我的线条222  ', nDocId, nPageId, nUserId)
  // });
  testNapi.registerCallback("ds_onRemoveAllAnt", ds_onRemoveAllAnt);
  callbackManager.initIsFailed = true
}

export function ds_unregisterCallback() {

}
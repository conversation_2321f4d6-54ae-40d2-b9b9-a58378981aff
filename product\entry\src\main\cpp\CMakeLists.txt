# the minimum version of CMake.
cmake_minimum_required(VERSION 3.5.0)
project(HSMeeting)

set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

if(DEFINED PACKAGE_FIND_FILE)
    include(${<PERSON><PERSON>KAGE_FIND_FILE})
endif()

include_directories(${NATIVERENDER_ROOT_PATH}
                    ${NATIVERENDER_ROOT_PATH}/include)

#add_library(entry SHARED napi_init.cpp)
#target_link_libraries(entry PUBLIC libace_napi.z.so)
#
#add_library(lib_hstinterface SHARED IMPORTED)
#set_target_properties(lib_hstinterface PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}/libhstinterface.so)
#target_link_libraries(entry PUBLIC lib_hstinterface)
#
#add_library(lib_hstsdk SHARED IMPORTED)
#set_target_properties(lib_hstsdk PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}/libhstsdk.so)
#target_link_libraries(entry PUBLIC lib_hstsdk)
#
#add_library(lib_hstwebrtc SHARED IMPORTED)
#set_target_properties(lib_hstwebrtc PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}/libhstwebrtc.so)
#target_link_libraries(entry PUBLIC lib_hstwebrtc)









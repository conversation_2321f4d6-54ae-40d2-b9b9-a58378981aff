import { ChatMessage } from ".";

// 参会者信息接口
export interface Participant {
  /** 用户操作*/
  action?: number
  /** 用户所在的频道 ID */
  channelId?: number;

  /** 用户是否被选中 */
  isSelected?: boolean;

  /** 用户是否聚焦 */
  isFocus?: boolean;

  /** !用户 ID */
  uid?: number;

  /** 用户登录名 */
  username?: string;

  /** 用户登录密码 */
  password?: string;

  /** !用户昵称 */
  nickname?: string;

  /** 用户真实姓名 */
  realname?: string;

  /** 用户的消息列表 */
  messages?: ChatMessage[];

  /** !用户角色，0 代表其他人；1 代表主持人；2 代表演讲者；4 代表助手；8 代表参会者；16 代表 Android 设备用户 */
  role?: number;

  /** !用户设备类型，0 代表其他设备；1 << 8 代表移动设备；1 << 9 代表 PC 设备等（具体见注释中的定义） */
  device?: number;

  /** !用户音频是否打开 */
  audioOpen?: boolean;

  /** !用户是否有麦克风 */
  isHaveMic?: boolean;

  /** !用户视频是否打开 */
  videoOpen?: boolean;

  /** 用户是否已读消息 */
  isReadedMsg?: boolean;

  /** 用户是否正在共享视频 */
  isShareVideo?: boolean;

  /** !用户是否有视频设备 */
  isHaveVideo?: boolean;

  /** 用户在创建会议时的角色描述 */
  createConfRole?: string;

  /** 指针颜色，-1 可能表示默认或未设置 */
  pointerColor?: number;

  /** !用户是否举手 */
  handUp?: boolean;

  /** 用户所属的多个频道 ID 列表 */
  channelIds?: number[];

}

// 参会者信息接口
@Observed
export class ParticipantClass {
  /** 用户操作*/
  action?: number
  /** 用户所在的频道 ID */
  channelId?: number;
  /** 用户是否被选中 */
  isSelected?: boolean;
  /** 用户是否聚焦 */
  isFocus?: boolean;
  /** !用户 ID */
  uid?: number;
  /** 用户登录名 */
  username?: string;
  /** 用户登录密码 */
  password?: string;
  /** !用户昵称 */
  nickname?: string;
  /** 用户真实姓名 */
  realname?: string;
  /** 用户的消息列表 */
  messages?: ChatMessage[];
  /** !用户角色，0 代表其他人；1 代表主持人；2 代表演讲者；4 代表助手；8 代表参会者；16 代表 Android 设备用户 */
  role?: number;
  /** !用户设备类型，0 代表其他设备；1 << 8 代表移动设备；1 << 9 代表 PC 设备等（具体见注释中的定义） */
  device?: number;
  /** !用户音频是否打开 */
  audioOpen?: boolean;
  /** !用户是否有麦克风 */
  isHaveMic?: boolean;
  /** !用户视频是否打开 */
  videoOpen?: boolean;
  /** 用户是否已读消息 */
  isReadedMsg?: boolean;
  /** 用户是否正在共享视频 */
  isShareVideo?: boolean;
  /** !用户是否有视频设备 */
  isHaveVideo?: boolean;
  /** 用户在创建会议时的角色描述 */
  createConfRole?: string;
  /** 指针颜色，-1 可能表示默认或未设置 */
  pointerColor?: number;
  /** !用户是否举手 */
  handUp?: boolean;
  /** 用户所属的多个频道 ID 列表 */
  channelIds?: number[];

  constructor(participant?: Participant) {
    // 每个数据都进行一遍赋值
    this.action = participant?.action
    this.channelId = participant?.channelId
    this.isSelected = participant?.isSelected
    this.isFocus = participant?.isFocus
    this.uid = participant?.uid
    this.username = participant?.username
    this.password = participant?.password
    this.nickname = participant?.nickname
    this.realname = participant?.realname
    this.messages = participant?.messages
    this.role = participant?.role
    this.device = participant?.device
    this.audioOpen = participant?.audioOpen
    this.isHaveMic = participant?.isHaveMic
    this.videoOpen = participant?.videoOpen
    this.isReadedMsg = participant?.isReadedMsg
    this.isShareVideo = participant?.isShareVideo
    this.isHaveVideo = participant?.isHaveVideo
    this.createConfRole = participant?.createConfRole
    this.pointerColor = participant?.pointerColor
    this.handUp = participant?.handUp
    this.channelIds = participant?.channelIds
  }
}

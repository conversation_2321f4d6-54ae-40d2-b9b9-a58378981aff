import { RouterModel } from '../model/RouterModel'

export class RouterModule{
  //WrappedBuilder 封装全局的@builder  使用Map的数组 其中以键值对的形式表示
  static builderMap:Map<string,WrappedBuilder<[object]>> =new Map<string,WrappedBuilder<[object]>>()
  //NavPathStack 是navigation中的接口 对路由进行操作的
  static routerMap:Map<string,NavPathStack> = new Map<string,NavPathStack>()

  //注册全局builder
  public static registerBuilder(builderName:string,builder:WrappedBuilder<[object]>):void{
    RouterModule.builderMap.set(builderName,builder)
  }
  //根据builder名返回相应的组件
  public static getBuilder(builderName:string):WrappedBuilder<[object]>{
    const builder = RouterModule.builderMap.get(builderName)
    if(!builder){
      console.log('not found builder' + builderName)
    }
    return builder as WrappedBuilder<[object]>
  }
  //清除路由 根据路由名
  public static craterRouter(routerName:string,router:NavPathStack){
    RouterModule.routerMap.set(routerName,router)
  }
  //根据路由名拿到相应的路由
  public static getRouter(routerName:string){
    return RouterModule.routerMap.get(routerName) as NavPathStack
  }
  //把路由放入路由栈
  public static async push(router:RouterModel){
    const harName = router.builderName.split("_")[0];
    console.log('www',harName)
    await import(harName).then((ns:ESObject)=> {
      ns.harInit(router.builderName)
      RouterModule.getRouter(router.routerName).pushPath({name:router.builderName,param:router.params})
    }).catch((err:string)=>{
      console.log('wwww',err)
    })
  }
  //删除相应名称的路由
  public static pop(routerName:string){
    RouterModule.getRouter(routerName).pop()
  }
  //清除路由
  public static clear(routerName:string){
    RouterModule.getRouter(routerName).clear()
  }
  //根据路由截取相应的路由
  public static popToName(routerName:string,builderName:string){
    RouterModule.getRouter(routerName).popToName(builderName)
  }

}
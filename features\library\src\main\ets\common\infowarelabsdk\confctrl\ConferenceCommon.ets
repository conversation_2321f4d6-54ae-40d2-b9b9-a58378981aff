import { ConferenceBean } from "./domain/ConferenceBean";

class  ConferenceContans {
  // 常量定义
  readonly RT_CONF_UI_TYPE_LEFT_TOP_VIDEO: number = 0;
  readonly RT_CONF_UI_TYPE_RIGHT_VIDEO: number = 1;
  readonly RT_CONF_UI_TYPE_TOP_VIDEO: number = 2;
  readonly RT_CONF_UI_TYPE_LIST_VIDEO: number = 3;
  readonly RT_CONF_UI_TYPE_FULLSCREEN_NORMAL: number = 4;
  readonly RT_CONF_UI_TYPE_FULLSCREEN_VIDEO: number = 5;

  readonly COMPONENT_TYPE_AS: number = 2;
  readonly COMPONENT_TYPE_DS: number = 3;
  readonly COMPONENT_TYPE_VIDEO: number = 8;

  readonly HTTP_ERROR: string = "100";
  readonly SPELL_ERROR: string = "101";
  readonly HOST_ERROR: string = "102";

  readonly RESULT_SUCCESS: number = 0;
  readonly RESULT_FAILURE: number = 1;

  readonly LEAVE: number = 3005;
  readonly BEGINRECORDSUC: number = 3004;
  readonly BEGINRECORDFAILED: number = 3003;
  readonly ENDRECORDSUC: number = 3002;
  readonly ENDRECORDFAILED: number = 3001;
  readonly INVITEPHONE: number = 3000;
  readonly CALLATT: number = 3007;
  readonly TRANSCHANNEL: number = 3008;
  readonly CLOUDRECORD: number = 3009;
  readonly RECONNECTING: number = 3006;

  readonly FORCELEAVE: number = 40500;
  readonly BEYOUNGMAXCOUNT: number = 40400;
  readonly HOSTCLOSECONF: number = 40300;
  readonly BEYOUNGJIAMI: number = 40200;
  readonly LICENSE_ERR: number = 40203;

  readonly FINISH_LIST_AVCIVITY: number = 2;

  readonly JOINED: number = 0;
  readonly NOTJOIN: number = 1;
  readonly JOINING: number = 2;
  readonly ISSHARE: number = 60522;

  // 音视频资源状态
  readonly RT_STATE_RESOURCE_VIDEO: number = 1 << 31;
  readonly RT_STATE_RESOURCE_AUDIO: number = 1 << 30;
  readonly RT_STATE_NEW_VIDEO: number = 1 << 29;
  readonly RT_STATE_NEW_VIDEO_RQ: number = 1 << 28;
  readonly RT_STATE_NEW_AUDIO: number = 1 << 27;
  readonly RT_STATE_NEW_AUDIO_RQ: number = 1 << 26;
  readonly RT_STATE_NEW_AUDIO_MUTE: number = 1 << 25;
  readonly RT_STATE_NEW_VIDEO_SYNC: number = 1 << 24;
  readonly RT_STATE_NEW_VIDEO_LOOP: number = 1 << 23;
  readonly RT_STATE_NEW_HAND: number = 1 << 22;
  readonly RT_STATE_NEW_SUB: number = 1 << 21;

  readonly RT_PRIVILEDGE_AV_AUDIO: number = 1 << 31;
  readonly RT_PRIVILEDGE_AV_VIDEO: number = 1 << 30;
}


/**
 * 会议组件,处理会议相关操作
 */
export interface ConferenceCommon {


  /**
   * 被动退出会议(回调方法)
   * @param result 退出状态
   */
  onLeaveConference(result: number): void;

  /**
   * 加入会议
   * @param param 会议参数
   */
  joinConference(param: string): void;

  /**
   * 修改界面
   * @param args1 参数1
   * @param args2 参数2
   */
  setLayout(args1: number, args2: number): void;

  /**
   * 主动退出会议
   */
  leaveConference(): void;

  /**
   * 主动退出会议但不销毁SDK
   */
  leaveConferenceNotDestroySDK(): void;

  /**
   * 断线重连
   * @param result 重连结果
   */
  onReconnectServer(result: number): void;

  /**
   * 加入会议结果回调
   * @param result 加入结果
   */
  onJoinConference(result: number): void;

  /**
   * 初始化SDK
   */
  initSDK(): void;

  /**
   * 返回初始化SDK结果
   * @param result 返回结果(0:成功  1:失败)
   */
  onInitSDK(result: number): void;

  /**
   * 关闭会议（当客户端为主持人时）
   */
  closeConference(): void;

  /**
   * 登出
   */
  logout(): void;

  /**
   * 打开录播
   * @param isBegin 表示是否打开，true表示打开录播，false表示关闭录播
   * @return 毫无意义
   */
  beginRecord(isBegin: boolean): number;

  /**
   * 打开录播操作成功
   */
  onBeginRecordSuc(): void;

  /**
   * 打开录播操作失败
   */
  onBeginRecordFailed(): void;

  /**
   * 关闭录播操作成功
   */
  onEndRecordSuc(): void;

  /**
   * 关闭录播操作失败
   */
  onEndRecordFailed(): void;

  /**
   * 取消呼叫手机
   * @param phoneNum 手机号码(utf-8)
   * @param length 手机号码长度
   */
  cancelInvitePhone(phoneNum: string, length: number): void;

  /**
   * 呼叫手机
   * @param phoneNum 手机号码(utf-8)
   * @param length 手机号码长度
   * @param name 手机用户昵称(unicode)
   * @param nameLength 手机用户昵称的长度
   */
  invitePhones(phoneNum: string, length: number, name: string, nameLength: number): void;

  /**
   * 呼叫手机结果回调
   * @param phoneNum 手机号码(utf-8)
   * @param isSuccess 是否呼叫成功
   */
  onInvitePhoneConfirm(phoneNum: string, isSuccess: boolean): void;

  /**
   * 呼叫原因
   * @param timeout 超时时间
   * @param id 呼叫ID
   */
  callReson(timeout: number, id: number): void;

  /**
   * 呼叫结果回调
   * @param response 是否响应
   * @param timeout 超时时间
   * @param id 呼叫ID
   */
  onCallAtt(response: boolean, timeout: number, id: number): void;

  /**
   * 用户角色变为主持人
   * @param uid 用户ID
   */
  userRole2Host(uid: number): void;

  /**
   * 举手
   * @param uid 用户ID
   * @param handUp 是否举手
   */
  handUp(uid: number, handUp: boolean): void;

  /**
   * 用户角色变为参会者
   */
  userRole2Attendee(): void;

  /**
   * 初始化配置
   * @param xml 配置XML
   * @param conferenceBean 会议信息
   */
  initConfig(xml: string, conferenceBean: ConferenceBean): void;

  /**
   * 是否支持SVC
   * @return 是否支持
   */
  isSupportSvc(): boolean;

  /**
   * 透明传输用户数据
   * @param msg 消息
   */
  transparentSendUserData(msg: string): void;

  /**
   * 透明接收数据
   * @param length 数据长度
   * @param data 数据
   */
  onTransparentRecvData(length: number, data: Uint8Array): void;

  /**
   * 是否支持云录制
   * @return 是否支持
   */
  isSupportCloudRecord(): boolean;

  /**
   * 开始云录制
   */
  beginCloudRecord(): void;

  /**
   * 开始云录制
   * @param quality 质量
   * @param videoBackground 视频背景
   * @param transparency 透明度
   * @param layoutID 布局ID
   */
  beginCloudRecord(quality: number, videoBackground: number, transparency: number, layoutID: number): void;

  /**
   * 停止云录制
   */
  stopCloudRecord(): void;

  /**
   * 更新直播状态
   * @param state 状态
   * @return 更新结果
   */
  updateLiveState(state: number): number;

  /**
   * 获取直播状态
   * @return 直播状态
   */
  getLiveState(): number;

  /**
   * 设置会议盒子
   */
  setMeetingBox(): void;

  /**
   * 设置混流视频
   */
  setMixVideo(): void;

  /**
   * 是否正在云录制
   * @return 是否正在录制
   */
  isCloudRecording(): boolean;

  /**
   * 录制状态响应
   * @param state 状态
   * @param isRecroding 是否正在录制
   */
  onRecordStateResponse(state: number, isRecroding: boolean): void;

  /**
   * 录制停止请求
   * @param srcuserid 源用户ID
   * @param dstuserid 目标用户ID
   */
  onRecordStopRequest(srcuserid: number, dstuserid: number): void;

  /**
   * 字幕
   * @param bShow 是否显示
   * @param strText 字幕内容
   */
  onSubtitles(bShow: boolean, strText: string): void;

  /**
   * 首次加入
   * @param bFirst 是否首次
   */
  onFirstJoin(bFirst: boolean): void;

  /**
   * 获取用户视图状态
   * @param uid 用户ID
   */
  getUserViewState(uid: number): void;

  /**
   * 直播状态
   * @param type 类型
   */
  OnLiveState(type: number): void;

  /**
   * 用户布局
   * @param type 类型
   */
  onUserLayout(type: number): void;

  /**
   * 权限问题
   * @param bAS 是否有权限
   */
  onAsPriviledge(bAS: boolean): void;

  /**
   * 视频自适应
   * @param videoAdaptive 是否自适应
   */
  onVideoAdaptive(videoAdaptive: boolean): void;

  /**
   * 会议模式
   * @param bFreeMode 是否自由模式
   */
  onConfMode(bFreeMode: boolean): void;

  /**
   * 会议音视频打开确认
   * @param bAvOpenConfirm 是否确认
   */
  onConfAvOpenConfirm(bAvOpenConfirm: boolean): void;

  /**
   * 会议标志
   * @param nFlag 标志
   */
  OnConfFlag(nFlag: number): void;

  /**
   * 用户视图状态
   * @param type 类型
   */
  onUserViewState(type: number): void;

  /**
   * 设置设备角色
   * @param role 角色
   */
  initRole(role: number): void;

  /**
   * 设置会议控制模式
   * @param key 键
   */
  setConfControlMode(key: string): void;

  /**
   * 设置字幕
   * @param bRemove 是否移除
   * @param strText 字幕内容
   * @param textLength 字幕长度
   * @param strFontName 字体名称
   * @param fontLength 字体长度
   * @param fontColor 字体颜色
   * @param bigFontSize 是否大字体
   */
  setSubtitles(bRemove: boolean, strText: Uint8Array, textLength: number, strFontName: Uint8Array, fontLength: number, fontColor: number, bigFontSize: boolean): void;

  /**
   * 获取网络速度
   */
  getNetSpeed(): void;

  /**
   * 设置默认权限
   * @param priviledge 权限
   * @param freePriviledge 自由权限
   */
  setDefaultPriviledge(priviledge: number, freePriviledge: number): void;

  /**
   * 请求
   * @param type 类型
   * @param userId 用户ID
   * @param channelId 通道ID
   * @param data1 数据1
   * @param data2 数据2
   * @param strData 字符串数据
   */
  onRequest(type: number, userId: number, channelId: number, data1: number, data2: number, strData: string): void;

  /**
   * 响应
   * @param type 类型
   * @param userId 用户ID
   * @param channelId 通道ID
   * @param data1 数据1
   * @param data2 数据2
   * @param strData 字符串数据
   */
  onResponse(type: number, userId: number, channelId: number, data1: number, data2: number, strData: string): void;

  /**
   * 设置设备状态
   * @param existMicrophone 是否存在麦克风
   * @param existCamera 是否存在摄像头
   */
  setDeviceStatus(existMicrophone: boolean, existCamera: boolean): void;
}

export class ConferenceImp extends ConferenceContans implements ConferenceCommon{
  onLeaveConference(result: number): void {
    throw new Error("Method not implemented.");
  }

  joinConference(param: string): void {
    throw new Error("Method not implemented.");
  }

  setLayout(args1: number, args2: number): void {
    throw new Error("Method not implemented.");
  }

  leaveConference(): void {
    throw new Error("Method not implemented.");
  }

  leaveConferenceNotDestroySDK(): void {
    throw new Error("Method not implemented.");
  }

  onReconnectServer(result: number): void {
    throw new Error("Method not implemented.");
  }

  onJoinConference(result: number): void {
    throw new Error("Method not implemented.");
  }

  initSDK(): void {
    throw new Error("Method not implemented.");
  }

  onInitSDK(result: number): void {
    throw new Error("Method not implemented.");
  }

  closeConference(): void {
    throw new Error("Method not implemented.");
  }

  logout(): void {
    throw new Error("Method not implemented.");
  }

  beginRecord(isBegin: boolean): number {
    throw new Error("Method not implemented.");
  }

  onBeginRecordSuc(): void {
    throw new Error("Method not implemented.");
  }

  onBeginRecordFailed(): void {
    throw new Error("Method not implemented.");
  }

  onEndRecordSuc(): void {
    throw new Error("Method not implemented.");
  }

  onEndRecordFailed(): void {
    throw new Error("Method not implemented.");
  }

  cancelInvitePhone(phoneNum: string, length: number): void {
    throw new Error("Method not implemented.");
  }

  invitePhones(phoneNum: string, length: number, name: string, nameLength: number): void {
    throw new Error("Method not implemented.");
  }

  onInvitePhoneConfirm(phoneNum: string, isSuccess: boolean): void {
    throw new Error("Method not implemented.");
  }

  callReson(timeout: number, id: number): void {
    throw new Error("Method not implemented.");
  }

  onCallAtt(response: boolean, timeout: number, id: number): void {
    throw new Error("Method not implemented.");
  }

  userRole2Host(uid: number): void {
    throw new Error("Method not implemented.");
  }

  handUp(uid: number, handUp: boolean): void {
    throw new Error("Method not implemented.");
  }

  userRole2Attendee(): void {
    throw new Error("Method not implemented.");
  }

  initConfig(xml: string, conferenceBean: ConferenceBean): void {
    throw new Error("Method not implemented.");
  }

  isSupportSvc(): boolean {
    throw new Error("Method not implemented.");
  }

  transparentSendUserData(msg: string): void {
    throw new Error("Method not implemented.");
  }

  onTransparentRecvData(length: number, data: Uint8Array): void {
    throw new Error("Method not implemented.");
  }

  isSupportCloudRecord(): boolean {
    throw new Error("Method not implemented.");
  }

  beginCloudRecord(): void;

  beginCloudRecord(quality: number, videoBackground: number, transparency: number, layoutID: number): void;

  beginCloudRecord(quality?: number, videoBackground?: number, transparency?: number, layoutID?: number): void {
    throw new Error("Method not implemented.");
  }

  stopCloudRecord(): void {
    throw new Error("Method not implemented.");
  }

  updateLiveState(state: number): number {
    throw new Error("Method not implemented.");
  }

  getLiveState(): number {
    throw new Error("Method not implemented.");
  }

  setMeetingBox(): void {
    throw new Error("Method not implemented.");
  }

  setMixVideo(): void {
    throw new Error("Method not implemented.");
  }

  isCloudRecording(): boolean {
    throw new Error("Method not implemented.");
  }

  onRecordStateResponse(state: number, isRecroding: boolean): void {
    throw new Error("Method not implemented.");
  }

  onRecordStopRequest(srcuserid: number, dstuserid: number): void {
    throw new Error("Method not implemented.");
  }

  onSubtitles(bShow: boolean, strText: string): void {
    throw new Error("Method not implemented.");
  }

  onFirstJoin(bFirst: boolean): void {
    throw new Error("Method not implemented.");
  }

  getUserViewState(uid: number): void {
    throw new Error("Method not implemented.");
  }

  OnLiveState(type: number): void {
    throw new Error("Method not implemented.");
  }

  onUserLayout(type: number): void {
    throw new Error("Method not implemented.");
  }

  onAsPriviledge(bAS: boolean): void {
    throw new Error("Method not implemented.");
  }

  onVideoAdaptive(videoAdaptive: boolean): void {
    throw new Error("Method not implemented.");
  }

  onConfMode(bFreeMode: boolean): void {
    throw new Error("Method not implemented.");
  }

  onConfAvOpenConfirm(bAvOpenConfirm: boolean): void {
    throw new Error("Method not implemented.");
  }

  OnConfFlag(nFlag: number): void {
    throw new Error("Method not implemented.");
  }

  onUserViewState(type: number): void {
    throw new Error("Method not implemented.");
  }

  initRole(role: number): void {
    throw new Error("Method not implemented.");
  }

  setConfControlMode(key: string): void {
    throw new Error("Method not implemented.");
  }

  setSubtitles(bRemove: boolean, strText: Uint8Array, textLength: number, strFontName: Uint8Array, fontLength: number,
    fontColor: number, bigFontSize: boolean): void {
    throw new Error("Method not implemented.");
  }

  getNetSpeed(): void {
    throw new Error("Method not implemented.");
  }

  setDefaultPriviledge(priviledge: number, freePriviledge: number): void {
    throw new Error("Method not implemented.");
  }

  onRequest(type: number, userId: number, channelId: number, data1: number, data2: number, strData: string): void {
    throw new Error("Method not implemented.");
  }

  onResponse(type: number, userId: number, channelId: number, data1: number, data2: number, strData: string): void {
    throw new Error("Method not implemented.");
  }

  setDeviceStatus(existMicrophone: boolean, existCamera: boolean): void {
    throw new Error("Method not implemented.");
  }

}
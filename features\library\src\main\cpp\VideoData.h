#ifndef HARMONYSDK_VIDEODATA_H
#define HARMONYSDK_VIDEODATA_H

#include "SdkDefine.h"

#ifdef __cplusplus
extern "C" { 
#endif 

class IVideoDataSink
{
public:
    virtual void OnVideoData(void* pData, int nDataLength, int nChannelId) = 0;
};

#ifdef __cplusplus
} 

extern "C" { 
#endif 

class IDsDataSink
{
public:
    virtual void OnAsData(void* pData,int nDataLength, SendDataType type, int nWidth, int nHeight) = 0;    
};

void registerVideoDataSink(IVideoDataSink *pSink);
void registerDsDataSink(IDsDataSink *pSink);
int sendVideoData(void* pData, int nDataLength,  bool bKeyFrame, int nWidth, int nHeight, bool bHardEncode);
int sendAsData(void *pData, int nDataLength, bool bKey<PERSON>rame, int nWidth, int nHeight, bool bHardEncode, int nBitsPerPix);

#ifdef __cplusplus
} 
#endif 

#endif //HARMONYSDK_VIDEODATA_H

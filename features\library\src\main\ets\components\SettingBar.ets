import { Participant } from '../models'
import { SettingBarItem } from './'

@Component
export struct SettingBar {
  @State isShowMore: boolean = false
  @Prop userData: Participant
  @Prop currentPageIndex: number
  @Prop isShowBar: boolean = true
  audioClick: () => void = () => {
  }
  videoClick: () => void = () => {
  }
  shareClick: () => void = () => {
  }
  participantClick: () => void = () => {
  }
  infoClick: () => void = () => {
  }
  settingClick: () => void = () => {
  }
  chatClick: () => void = () => {
  }
  handUpClick: () => void = () => {
  }
  takeBackChairClick: () => void = () => {
  }
  recordClick: () => void = () => {
  }

  build() {
    // 底部工具栏 - 在聊天页面时隐藏
    Row() {

      SettingBarItem({
        text: '语音',
        fontColor: '#007DFF',
        icon: this.userData.audioOpen ? $r('app.media.ic_index_1_enable_on') : $r('app.media.ic_index_1_enable_off'),
        onclick: () => {
          //开启-关闭语音
          this.audioClick()

        }
      })
      SettingBarItem({
        text: '视频',
        fontColor: this.currentPageIndex === 0 ? '#007DFF' : '#FFFFFF',
        icon: this.currentPageIndex === 0 ?
          (this.userData.videoOpen ? $r('app.media.ic_index_2_sel') : $r('app.media.ic_index_2_sel_off')) :
          (this.userData.videoOpen ? $r('app.media.ic_index_2_nor') : $r('app.media.ic_index_2_nor_off')),
        onclick: () => {
          // 开启-关闭视频
          this.videoClick()
        }
      })
      SettingBarItem({
        text: '分享',
        fontColor: this.currentPageIndex === 1 ? '#007DFF' : '#FFFFFF',
        icon: this.currentPageIndex === 1 ? $r('app.media.ic_index_3_sel') : $r('app.media.ic_index_3_nor'),
        onclick: () => {
          this.shareClick()
        }
      })
      SettingBarItem({
        text: '参加者',
        fontColor: this.currentPageIndex === 2 ? '#007DFF' : '#FFFFFF',
        icon: this.currentPageIndex === 2 ? $r('app.media.ic_index_4_sel') : $r('app.media.ic_index_4_nor'),
        onclick: () => {
          this.participantClick()
        }
      })
      Column() {
        SettingBarItem({
          text: '更多',
          fontColor: '#FFFFFF',
          icon: this.currentPageIndex === 3 || this.currentPageIndex === 4 ? $r('app.media.ic_index_5_sel') :
          $r('app.media.ic_index_5_nor'),
          onclick: () => {
            this.isShowMore = true
          }
        })
      }
      .bindContextMenu(this.isShowMore, this.MenuBuilder(), {
        placement: Placement.TopLeft,
        borderRadius: 5,
        backgroundBlurStyle: BlurStyle.NONE,
        backgroundColor: Color.Black,
        offset: { x: 0, y: -10 },
        onDisappear: () => {
          console.log('run onDisappear')
          this.isShowMore = false
        }
      })

    }
    .width('100%')
    .height(64)
    .backgroundColor(this.currentPageIndex === 0 ? '#4d1a1b1f' : '#1A1B1F')
    .justifyContent(FlexAlign.SpaceAround)
    .alignItems(VerticalAlign.Center)
    .alignRules(
      this.isShowBar ?
        {
          bottom: { anchor: "__container__", align: VerticalAlign.Bottom },
          left: { anchor: "__container__", align: HorizontalAlign.Start }
        } :
        {
          bottom: { anchor: "__container__", align: VerticalAlign.Bottom },
          left: { anchor: "__container__", align: HorizontalAlign.End }
        }
    )
  }

  @Builder
  MenuBuilder() {
    GridRow({ columns: 3, gutter: 15 }) {
      GridCol() {
        bindMenuItem({
          text: '信息',
          icon: this.currentPageIndex === 3 ? $r('app.media.ic_pop_index5_info_sel') :
          $r('app.media.ic_pop_index5_info_nor'),
          click: () => {
            this.infoClick()
          }
        })
      }

      GridCol() {
        bindMenuItem({
          text: '设置',
          icon: this.currentPageIndex === 4 ? $r('app.media.ic_pop_index5_set_sel') :
          $r('app.media.ic_pop_index5_set_nor'),
          click: () => {
            this.settingClick()
          }
        })
      }

      GridCol() {
        bindMenuItem({
          text: '聊天',
          icon: $r('app.media.ic_vs_chat'),
          click: () => {
            this.chatClick()
          }
        })
      }

      if (this.userData.role === 1) {
        GridCol() {
          bindMenuItem({
            text: '录制',
            icon: $r('app.media.ic_pop_index5_rec_nor'),
            click: () => {
              // console.log('录制功能暂未开放')
              this.recordClick()
            }
          })
        }
      } else {
        GridCol() {
          bindMenuItem({
            text: '举手',
            icon: $r('app.media.hst_action_handup'),
            click: () => {
              this.handUpClick()
            }
          })
        }

        //
        // GridCol() {
        //   bindMenuItem({
        //     text: '收回主持',
        //     icon: $r('app.media.hst_action_set_as_host'),
        //     click: () => {
        //       this.takeBackChairClick()
        //     }
        //   })
        // }
      }


    }
    .width(190)
    .padding(10)
    .height(130)
    .borderRadius(5)
    .backgroundColor(Color.Black)

  }
}


@Component
struct bindMenuItem {
  @Prop icon: ResourceStr
  @Prop text: string
  click: () => void = () => {
  }

  build() {
    Column() {
      Image(this.icon)
        .width(18)
        .fillColor(Color.White)
      Text(this.text)
        .fontSize(10)
        .fontColor(Color.White)
    }
    .justifyContent(FlexAlign.SpaceEvenly)
    .width(50)
    .aspectRatio(1)
    .backgroundColor('#ff161616')
    .onClick(() => {
      this.click()
    })
  }
}

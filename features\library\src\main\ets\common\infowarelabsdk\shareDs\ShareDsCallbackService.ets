import testNapi from 'libhstinterface.so'
import { ANTINFO } from '../../../models';

//开始分享白板
export function StartShareDoc(title: string) {
  let g_nDocId: number = testNapi.ds_NewBoard(title)
  testNapi.ds_NewBoardPage(g_nDocId, 3840, 2160);
  return g_nDocId
}

//停止分享白板
export function StopShareDoc(g_nDocId: number, nextDocId: number): number {
  return testNapi.ds_CloseDoc(g_nDocId, nextDocId);
}

export function sendAS_Data(data: ANTINFO): number {
  return data.id = testNapi.ds_NewAnt(data);
}

export function switchDoc(docId: number): number {
  return testNapi.ds_SwitchDoc(docId)
}



export function removeAS_Data(data: ANTINFO): number {
  console.log('iiii del ant is:', data.docId, data.pageId, data.id)
  return testNapi.ds_RemoveAnt(data.docId, data.pageId, data.id)
}

// function ds_onNewAnt(ant:object): void {
//
//   const antInfo = ant as ANTINFO;
//
//   if(ds_ant_type.ANT_OBJECT_POINTER == antInfo.type) {
//
//   }
//
// }
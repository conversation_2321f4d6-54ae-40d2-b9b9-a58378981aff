#include "Decoder.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "common/dfx/log/AVCodecSampleLog.h"
#include <queue>
#include "render/include/PluginManager.h"  // 包含PluginManager的头文件
#undef LOG_TAG
#define LOG_TAG "Decoder"

namespace {
constexpr int BALANCE_VALUE = 5;
using namespace std::chrono_literals;
} // namespace

Decoder::~Decoder() { Decoder::StartRelease(); }

#include <iostream>
#include <string>
#include <native_image/native_image.h>
#include <native_window/external_window.h>
#include <native_buffer/native_buffer.h>
#include <multimedia/player_framework/native_avcodec_videodecoder.h>

int32_t Decoder::CreateVideoDecoder() {
    AVCODEC_SAMPLE_LOGW("video mime:%{public}s", sampleInfo_.videoCodecMime.c_str());
    int32_t ret = videoDecoder_->Create(sampleInfo_.videoCodecMime);
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGW("Create video decoder failed, mime:%{public}s", sampleInfo_.videoCodecMime.c_str());
    } else {
        videoDecContext_ = new CodecUserData;
        sampleInfo_.window = NativeXComponentSample::PluginManager::GetInstance()->pluginWindow_;
//        OH_NativeImage* image = OH_ConsumerSurface_Create();
//        sampleInfo_.window  = OH_NativeImage_AcquireNativeWindow(image);
        ret = videoDecoder_->Config(sampleInfo_, videoDecContext_);
        CHECK_AND_RETURN_RET_LOG(ret == AVCODEC_SAMPLE_ERR_OK, ret, "Video Decoder config failed");
    }
    
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Decoder::Init(SampleInfo &sampleInfo) {
    std::unique_lock<std::mutex> lock(mutex_);
    CHECK_AND_RETURN_RET_LOG(!isStarted_, AVCODEC_SAMPLE_ERR_ERROR, "Already started.");

    sampleInfo_ = sampleInfo;
    videoDecoder_ = std::make_unique<VideoDecoder>();
    isReleased_ = false;
    
    int32_t ret = CreateVideoDecoder();
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGE("Create video decoder failed");
        doneCond_.notify_all();
        lock.unlock();
        StartRelease();
        return AVCODEC_SAMPLE_ERR_ERROR;
    }
    
    isInitialized_ = true;
    AVCODEC_SAMPLE_LOGI("Init Succeed");
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Decoder::Start() {
    std::unique_lock<std::mutex> lock(mutex_);
    int32_t ret;
    CHECK_AND_RETURN_RET_LOG(!isStarted_, AVCODEC_SAMPLE_ERR_ERROR, "Already started.");
    if (videoDecContext_) {
        ret = videoDecoder_->Start();
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Video Decoder start failed");
            lock.unlock();
            StartRelease();
            return AVCODEC_SAMPLE_ERR_ERROR;
        }
        isStarted_ = true;
        videoDecInputThread_ = std::make_unique<std::thread>(&Decoder::VideoDecInputThread, this);
        videoDecOutputThread_ = std::make_unique<std::thread>(&Decoder::VideoDecOutputThread, this);

        if (videoDecInputThread_ == nullptr || videoDecOutputThread_ == nullptr) {
            AVCODEC_SAMPLE_LOGE("Create thread failed");
            lock.unlock();
            StartRelease();
            return AVCODEC_SAMPLE_ERR_ERROR;
        }
    }
//    if (audioDecContext_) {
//        ret = audioDecoder_->Start();
//        if (ret != AVCODEC_SAMPLE_ERR_OK) {
//            AVCODEC_SAMPLE_LOGE("Audio Decoder start failed");
//            lock.unlock();
//            StartRelease();
//            return AVCODEC_SAMPLE_ERR_ERROR;
//        }
//        isStarted_ = true;
//        audioDecInputThread_ = std::make_unique<std::thread>(&Decoder::AudioDecInputThread, this);
//        audioDecOutputThread_ = std::make_unique<std::thread>(&Decoder::AudioDecOutputThread, this);
//        if (audioDecInputThread_ == nullptr || audioDecOutputThread_ == nullptr) {
//            AVCODEC_SAMPLE_LOGE("Create thread failed");
//            lock.unlock();
//            StartRelease();
//            return AVCODEC_SAMPLE_ERR_ERROR;
//        }
//    }
    // Clear the queue
    while (audioDecContext_ && !audioDecContext_->renderQueue.empty()) {
        audioDecContext_->renderQueue.pop();
    }
    if (audioRenderer_) {
        OH_AudioRenderer_Start(audioRenderer_);
    }
    AVCODEC_SAMPLE_LOGI("Succeed");
    doneCond_.notify_all();
    return AVCODEC_SAMPLE_ERR_OK;
}

void Decoder::StartRelease() {
    isInitialized_ = false;
    isScreenShareInitialized_ = false;
    AVCODEC_SAMPLE_LOGI("start release");
//    if (audioRenderer_) {
//        OH_AudioRenderer_Stop(audioRenderer_);
//    }
    if (!isReleased_) {
        isReleased_ = true;
        Release();
    }
}

void Decoder::ReleaseThread() {
    // Release video threads
    if (videoDecInputThread_ && videoDecInputThread_->joinable()) {
        videoDecInputThread_->detach();
        videoDecInputThread_.reset();
    }
    if (videoDecOutputThread_ && videoDecOutputThread_->joinable()) {
        videoDecOutputThread_->detach();
        videoDecOutputThread_.reset();
    }
    
    // Release screen share threads
    if (screenShareDecInputThread_ && screenShareDecInputThread_->joinable()) {
        screenShareDecInputThread_->detach();
        screenShareDecInputThread_.reset();
    }
    if (screenShareDecOutputThread_ && screenShareDecOutputThread_->joinable()) {
        screenShareDecOutputThread_->detach();
        screenShareDecOutputThread_.reset();
    }
    
    // Release audio threads
    if (audioDecInputThread_ && audioDecInputThread_->joinable()) {
        audioDecInputThread_->detach();
        audioDecInputThread_.reset();
    }
    if (audioDecOutputThread_ && audioDecOutputThread_->joinable()) {
        audioDecOutputThread_->detach();
        audioDecOutputThread_.reset();
    }
}

void Decoder::Release() {
    std::lock_guard<std::mutex> lock(mutex_);
    isStarted_ = false;

    // Clear audio queue
    if (audioDecContext_) {
        while (!audioDecContext_->renderQueue.empty()) {
            audioDecContext_->renderQueue.pop();
        }
    }
    
    if (audioRenderer_ != nullptr) {
        OH_AudioRenderer_Release(audioRenderer_);
        audioRenderer_ = nullptr;
    }
    
    ReleaseThread();

    // Release video decoder and context
    if (videoDecoder_ != nullptr) {
        videoDecoder_->Release();
        videoDecoder_.reset();
    }
    
    if (videoDecContext_ != nullptr) {
        while (!videoDecContext_->inputBufferInfoQueue.empty()) {
            videoDecContext_->inputBufferInfoQueue.pop();
        }
        while (!videoDecContext_->outputBufferInfoQueue.empty()) {
            videoDecContext_->outputBufferInfoQueue.pop();
        }
        delete videoDecContext_;
        videoDecContext_ = nullptr;
    }

    // Release screen share decoder and context
    if (screenShareDecoder_ != nullptr) {
        screenShareDecoder_->Release();
        screenShareDecoder_.reset();
    }
    
    if (screenShareDecContext_ != nullptr) {
        while (!screenShareDecContext_->inputBufferInfoQueue.empty()) {
            screenShareDecContext_->inputBufferInfoQueue.pop();
        }
        while (!screenShareDecContext_->outputBufferInfoQueue.empty()) {
            screenShareDecContext_->outputBufferInfoQueue.pop();
        }
        delete screenShareDecContext_;
        screenShareDecContext_ = nullptr;
    }

    // Release audio context
    if (audioDecContext_ != nullptr) {
        delete audioDecContext_;
        audioDecContext_ = nullptr;
    }
    
    if (builder_ != nullptr) {
        OH_AudioStreamBuilder_Destroy(builder_);
        builder_ = nullptr;
    }

    doneCond_.notify_all();
    
    // Handle callbacks
    if (screenShareSampleInfo_.playDoneCallback && screenShareSampleInfo_.playDoneCallbackData) {
        screenShareSampleInfo_.playDoneCallback(screenShareSampleInfo_.playDoneCallbackData);
    }
    if (sampleInfo_.playDoneCallback && sampleInfo_.playDoneCallbackData) {
        sampleInfo_.playDoneCallback(sampleInfo_.playDoneCallbackData);
    }
    
    AVCODEC_SAMPLE_LOGI("Release Succeed");
}

#include <chrono>
#include <iostream>

int64_t GetMicroseconds() {
    // 获取当前时间点 ‌:ml-citation{ref="1,3" data="citationList"}
    auto now = std::chrono::high_resolution_clock::now();
    // 转换为自 Epoch 以来的微秒数 ‌:ml-citation{ref="4,5" data="citationList"}
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

void Decoder::VideoDecInputThread() {
    
    while (true) {
        CHECK_AND_BREAK_LOG(isStarted_, "Decoder input thread out");
        
        if (!videoDecContext_) {
            AVCODEC_SAMPLE_LOGE("Invalid video decoder context");
            break;
        }
        
        {
            std::unique_lock<std::mutex> dataLock(videoDecContext_->inputDataMutex);
            bool dataCondRet = videoDecContext_->inputDataCond.wait_for(
                dataLock, 5s, [this]() { 
                    return !isStarted_ || 
                           (videoDecContext_ && !videoDecContext_->inputDataBufferInfoQueue.empty()); 
                });
        }        
       
        CHECK_AND_BREAK_LOG(isStarted_, "Work done, thread out");
        
        if (!videoDecContext_ || videoDecContext_->inputDataBufferInfoQueue.empty()) {
            continue;
        }    
        
        std::unique_lock<std::mutex> lock(videoDecContext_->inputMutex);
        bool condRet = videoDecContext_->inputCond.wait_for(
            lock, 5s, [this]() { 
                return !isStarted_ || 
                       (videoDecContext_ && !videoDecContext_->inputBufferInfoQueue.empty()); 
            });
            
        CHECK_AND_BREAK_LOG(isStarted_, "Work done, thread out");
        
        if (!videoDecContext_ || videoDecContext_->inputBufferInfoQueue.empty()) {
            
            std::unique_lock<std::mutex> dataLock(videoDecContext_->inputDataMutex);
            while(videoDecContext_->inputDataBufferInfoQueue.size() > 10) {
                CodecBufferInfo dataBufferInfo = videoDecContext_->inputDataBufferInfoQueue.front();
                videoDecContext_->inputDataBufferInfoQueue.pop();
             
                if(dataBufferInfo.bufferAddr) {
                    delete dataBufferInfo.bufferAddr;
                    dataBufferInfo.bufferAddr = nullptr;
                }            
            }            
            
            continue;
        }
        
        CodecBufferInfo bufferInfo = videoDecContext_->inputBufferInfoQueue.front();
        videoDecContext_->inputBufferInfoQueue.pop();
        videoDecContext_->inputFrameCount++;
        lock.unlock();

        if (!videoDecoder_) {
            AVCODEC_SAMPLE_LOGE("Invalid video decoder");
            break;
        }
        
        std::unique_lock<std::mutex> dataLock(videoDecContext_->inputDataMutex);
        CodecBufferInfo dataBufferInfo = videoDecContext_->inputDataBufferInfoQueue.front();
        videoDecContext_->inputDataBufferInfoQueue.pop();
        dataLock.unlock();
        
        uint8_t* addr = OH_AVBuffer_GetAddr((OH_AVBuffer *)bufferInfo.buffer) + bufferInfo.attr.offset;
        
        if(addr && dataBufferInfo.bufferAddr) {
            memcpy(addr, dataBufferInfo.bufferAddr, dataBufferInfo.attr.size);
            delete dataBufferInfo.bufferAddr;
            dataBufferInfo.bufferAddr = nullptr;
            bufferInfo.attr.size = dataBufferInfo.attr.size;
            OH_AVBuffer_SetBufferAttr((OH_AVBuffer *)bufferInfo.buffer, &bufferInfo.attr);
        }
        
        int32_t ret = videoDecoder_->PushInputBuffer(bufferInfo);
        CHECK_AND_BREAK_LOG(ret == AVCODEC_SAMPLE_ERR_OK, "Push data failed, thread out");

        CHECK_AND_BREAK_LOG(!(bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_EOS), "Catch EOS, thread out");
    }
}

void Decoder::VideoDecOutputThread() {
    if (!videoDecContext_) {
        AVCODEC_SAMPLE_LOGE("Invalid video decoder context");
        return;
    }

    sampleInfo_.frameInterval = MICROSECOND / sampleInfo_.frameRate;
    while (true) {
        thread_local auto lastPushTime = std::chrono::system_clock::now();
        CHECK_AND_BREAK_LOG(isStarted_, "Decoder output thread out");
        
        if (!videoDecContext_) {
            AVCODEC_SAMPLE_LOGE("Invalid video decoder context");
            break;
        }

        std::unique_lock<std::mutex> lock(videoDecContext_->outputMutex);
        bool condRet = videoDecContext_->outputCond.wait_for(
            lock, 5s, [this]() { 
                return !isStarted_ || 
                       (videoDecContext_ && !videoDecContext_->outputBufferInfoQueue.empty()); 
            });
            
        CHECK_AND_BREAK_LOG(isStarted_, "Decoder output thread out");
        
        if (!videoDecContext_ || videoDecContext_->outputBufferInfoQueue.empty()) {
            continue;
        }

        CodecBufferInfo bufferInfo = videoDecContext_->outputBufferInfoQueue.front();
        videoDecContext_->outputBufferInfoQueue.pop();
        
        if (bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_EOS) {
            AVCODEC_SAMPLE_LOGI("Catch EOS, thread out");
            break;
        }
        
        videoDecContext_->outputFrameCount++;
        
        if (!videoDecoder_) {
            AVCODEC_SAMPLE_LOGE("Invalid video decoder");
            break;
        }

        int32_t ret = videoDecoder_->FreeOutputBuffer(bufferInfo.bufferIndex, true);
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Free output buffer failed");
            break;
        }

        std::this_thread::sleep_until(lastPushTime + std::chrono::microseconds(sampleInfo_.frameInterval));
        lastPushTime = std::chrono::system_clock::now();
    }
    StartRelease();
}

int32_t Decoder::InitScreenShare(SampleInfo &sampleInfo) {
    std::unique_lock<std::mutex> lock(mutex_);
    CHECK_AND_RETURN_RET_LOG(!isScreenShareInitialized_, AVCODEC_SAMPLE_ERR_ERROR, "Screen share already initialized.");

    screenShareSampleInfo_ = sampleInfo;
    screenShareDecoder_ = std::make_unique<VideoDecoder>(VideoDecoder::Type::SCREEN_SHARE);
    isReleased_ = false;
    
    int32_t ret = CreateScreenShareDecoder();
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGE("Create screen share decoder failed");
        doneCond_.notify_all();
        lock.unlock();
        StartRelease();
        return AVCODEC_SAMPLE_ERR_ERROR;
    }
    
    isScreenShareInitialized_ = true;
    AVCODEC_SAMPLE_LOGI("Screen share init succeed");
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Decoder::CreateScreenShareDecoder() {
    AVCODEC_SAMPLE_LOGW("screen share video mime:%{public}s", screenShareSampleInfo_.videoCodecMime.c_str());
    int32_t ret = screenShareDecoder_->Create(screenShareSampleInfo_.videoCodecMime);
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGW("Create screen share decoder failed, mime:%{public}s", 
            screenShareSampleInfo_.videoCodecMime.c_str());
    } else {
        screenShareDecContext_ = new CodecUserData;
        screenShareSampleInfo_.window = NativeXComponentSample::PluginManager::GetInstance()->pluginWindow_;
        ret = screenShareDecoder_->Config(screenShareSampleInfo_, screenShareDecContext_);
        CHECK_AND_RETURN_RET_LOG(ret == AVCODEC_SAMPLE_ERR_OK, ret, "Screen share decoder config failed");
    }
    
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Decoder::StartScreenShare() {
    std::unique_lock<std::mutex> lock(mutex_);
    int32_t ret;
    CHECK_AND_RETURN_RET_LOG(!isStarted_, AVCODEC_SAMPLE_ERR_ERROR, "Already started.");

    if (screenShareDecContext_) {
        ret = screenShareDecoder_->Start();
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Screen share decoder start failed");
            lock.unlock();
            StartRelease();
            return AVCODEC_SAMPLE_ERR_ERROR;
        }
        isStarted_ = true;
        screenShareDecInputThread_ = std::make_unique<std::thread>(&Decoder::ScreenShareDecInputThread, this);
        screenShareDecOutputThread_ = std::make_unique<std::thread>(&Decoder::ScreenShareDecOutputThread, this);

        if (screenShareDecInputThread_ == nullptr || screenShareDecOutputThread_ == nullptr) {
            AVCODEC_SAMPLE_LOGE("Create screen share thread failed");
            lock.unlock();
            StartRelease();
            return AVCODEC_SAMPLE_ERR_ERROR;
        }
    }

    AVCODEC_SAMPLE_LOGI("Screen share start succeed");
    doneCond_.notify_all();
    return AVCODEC_SAMPLE_ERR_OK;
}

void Decoder::ScreenShareDecInputThread() {
    while (true) {
        CHECK_AND_BREAK_LOG(isStarted_, "Screen share decoder input thread out");
        
        if (!screenShareDecContext_) {
            AVCODEC_SAMPLE_LOGE("Invalid screen share decoder context");
            break;
        }
        
        {
            std::unique_lock<std::mutex> dataLock(screenShareDecContext_->inputDataMutex);
            bool dataCondRet = screenShareDecContext_->inputDataCond.wait_for(
                dataLock, 5s, [this]() { 
                    return !isStarted_ || 
                           (screenShareDecContext_ && !screenShareDecContext_->inputDataBufferInfoQueue.empty()); 
                });
        }        
       
        CHECK_AND_BREAK_LOG(isStarted_, "Work done, thread out");
        
        if (!screenShareDecContext_ || screenShareDecContext_->inputDataBufferInfoQueue.empty()) {
            continue;
        }    
        
        std::unique_lock<std::mutex> lock(screenShareDecContext_->inputMutex);
        bool condRet = screenShareDecContext_->inputCond.wait_for(
            lock, 5s, [this]() { 
                return !isStarted_ || 
                       (screenShareDecContext_ && !screenShareDecContext_->inputBufferInfoQueue.empty()); 
            });
            
        CHECK_AND_BREAK_LOG(isStarted_, "Work done, thread out");
        
        if (!screenShareDecContext_ || screenShareDecContext_->inputBufferInfoQueue.empty()) {
            std::unique_lock<std::mutex> dataLock(screenShareDecContext_->inputDataMutex);
            while(screenShareDecContext_->inputDataBufferInfoQueue.size() > 10) {
                CodecBufferInfo dataBufferInfo = screenShareDecContext_->inputDataBufferInfoQueue.front();
                screenShareDecContext_->inputDataBufferInfoQueue.pop();
             
                if(dataBufferInfo.bufferAddr) {
                    delete dataBufferInfo.bufferAddr;
                    dataBufferInfo.bufferAddr = nullptr;
                }            
            }            
            continue;
        }
        
        CodecBufferInfo bufferInfo = screenShareDecContext_->inputBufferInfoQueue.front();
        screenShareDecContext_->inputBufferInfoQueue.pop();
        screenShareDecContext_->inputFrameCount++;
        lock.unlock();

        if (!screenShareDecoder_) {
            AVCODEC_SAMPLE_LOGE("Invalid screen share decoder");
            break;
        }
        
        std::unique_lock<std::mutex> dataLock(screenShareDecContext_->inputDataMutex);
        CodecBufferInfo dataBufferInfo = screenShareDecContext_->inputDataBufferInfoQueue.front();
        screenShareDecContext_->inputDataBufferInfoQueue.pop();
        dataLock.unlock();
        
        uint8_t* addr = OH_AVBuffer_GetAddr((OH_AVBuffer *)bufferInfo.buffer) + bufferInfo.attr.offset;
        
        if(addr && dataBufferInfo.bufferAddr) {
            memcpy(addr, dataBufferInfo.bufferAddr, dataBufferInfo.attr.size);
            delete dataBufferInfo.bufferAddr;
            dataBufferInfo.bufferAddr = nullptr;
            bufferInfo.attr.size = dataBufferInfo.attr.size;
            OH_AVBuffer_SetBufferAttr((OH_AVBuffer *)bufferInfo.buffer, &bufferInfo.attr);
        }
        
        int32_t ret = screenShareDecoder_->PushInputBuffer(bufferInfo);
        CHECK_AND_BREAK_LOG(ret == AVCODEC_SAMPLE_ERR_OK, "Push data failed, thread out");

        CHECK_AND_BREAK_LOG(!(bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_EOS), "Catch EOS, thread out");
    }
}

void Decoder::ScreenShareDecOutputThread() {
    if (!screenShareDecContext_) {
        AVCODEC_SAMPLE_LOGE("Invalid screen share decoder context");
        return;
    }

    screenShareSampleInfo_.frameInterval = MICROSECOND / screenShareSampleInfo_.frameRate;
    while (true) {
        thread_local auto lastPushTime = std::chrono::system_clock::now();
        CHECK_AND_BREAK_LOG(isStarted_, "Screen share decoder output thread out");
        
        if (!screenShareDecContext_) {
            AVCODEC_SAMPLE_LOGE("Invalid screen share decoder context");
            break;
        }

        std::unique_lock<std::mutex> lock(screenShareDecContext_->outputMutex);
        bool condRet = screenShareDecContext_->outputCond.wait_for(
            lock, 5s, [this]() { 
                return !isStarted_ || 
                       (screenShareDecContext_ && !screenShareDecContext_->outputBufferInfoQueue.empty()); 
            });
            
        CHECK_AND_BREAK_LOG(isStarted_, "Screen share decoder output thread out");
        
        if (!screenShareDecContext_ || screenShareDecContext_->outputBufferInfoQueue.empty()) {
            continue;
        }

        CodecBufferInfo bufferInfo = screenShareDecContext_->outputBufferInfoQueue.front();
        screenShareDecContext_->outputBufferInfoQueue.pop();
        
        if (bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_EOS) {
            AVCODEC_SAMPLE_LOGI("Catch EOS, thread out");
            break;
        }
        
        screenShareDecContext_->outputFrameCount++;
        
        if (!screenShareDecoder_) {
            AVCODEC_SAMPLE_LOGE("Invalid screen share decoder");
            break;
        }

        int32_t ret = screenShareDecoder_->FreeOutputBuffer(bufferInfo.bufferIndex, true);
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Free output buffer failed");
            break;
        }

        std::this_thread::sleep_until(lastPushTime + 
            std::chrono::microseconds(screenShareSampleInfo_.frameInterval));
        lastPushTime = std::chrono::system_clock::now();
    }
    StartRelease();
}



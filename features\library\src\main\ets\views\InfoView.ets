import { ParticipantClass } from '../models'
import { promptAction, router } from '@kit.ArkUI'
import { pasteboard } from '@kit.BasicServicesKit'
import { MeetingInfo } from '../utils/XmlManager'

@Component
export struct InfoView {
  @State meetingInfo: MeetingInfo = {} as MeetingInfo
  @Prop participants: Array<ParticipantClass>
  @State participant: ParticipantClass = new ParticipantClass({ nickname: '' })
  @State meetingLink: string = ''

  aboutToAppear(): void {
    this.meetingInfo = (router.getParams() as object)['xmlObj']
    const moderator = this.participants.find((participant) => participant.role === 1)
    if (moderator) {
      this.participant = moderator
    }
    this.meetingLink = this.meetingInfo.InviteWebApi +
      `/app/simpleJoin/index.action?confKey=${this.meetingInfo.confId}&site=box`
  }

  // 复制文本
  async copyText(text: string) {
    const pasteboardData = pasteboard.createData(pasteboard.MIMETYPE_TEXT_PLAIN, text);
    const systemPasteboard = pasteboard.getSystemPasteboard();
    await systemPasteboard.setData(pasteboardData); // 将数据放入剪切板
    const data = await systemPasteboard.getData()
    if (data) {
      promptAction.showToast({ message: '复制成功' });
    } else {
      promptAction.showToast({ message: '复制失败' });
    }
  }

  build() {
    Scroll() {
      Column({ space: 15 }) {
        this.infoItemBuilder('会议主题：', this.meetingInfo.MeetingName)
        this.infoItemBuilder('会议号：', this.meetingInfo.confId)
        this.infoItemBuilder('主持人：', this.participant.nickname || '')
        this.infoItemBuilder('会议密码：', this.meetingInfo.MeetingPassword)

        Column({ space: 15 }) {
          Text('会议连接：')
          Text(this.meetingLink)
          Button('复制')
            .borderRadius(10)
            .padding({
              left: 20,
              right: 20,
              top: 10,
              bottom: 10
            })
            .alignSelf(ItemAlign.End)
            .onClick(() => {
              this.copyText(this.meetingLink)
            })
        }
        .width('100%')
        .padding(15)
        .backgroundColor(Color.White)
        .alignItems(HorizontalAlign.Start)

      }
      .width('100%')
      .backgroundColor('#ffefefef')
    }
    .width('100%')
    .layoutWeight(1)
  }

  @Builder
  infoItemBuilder(title: string, text: string) {
    Row() {
      Text(title)
      Text(text)
        .layoutWeight(1)
        .textAlign(TextAlign.End)
    }
    .padding(15)
    .width('100%')
    .backgroundColor(Color.White)
  }
}
{
  "apiType": "stageMode",
//  "buildOption": {
//    "externalNativeOptions": {
//      "path": "./src/main/cpp/CMakeLists.txt",
//      "arguments": "",
//      "cppFlags": ""
//    }
//  },
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": false,
            "files": [
              "./obfuscation-rules.txt"
            ]
          }
        }
      }
    },
  ],
  "targets": [
    {
      "name": "default"
    },
    {
      "name": "ohosTest",
    }
  ]
}
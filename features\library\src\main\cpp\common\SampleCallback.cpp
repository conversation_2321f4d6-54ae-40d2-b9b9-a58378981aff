#include "SampleCallback.h"
#include "common/dfx/log/AVCodecSampleLog.h"

namespace {
constexpr int LIMIT_LOGD_FREQUENCY = 50;
}

// Custom write data function
int32_t SampleCallback::OnRenderWriteData(OH_AudioRenderer *renderer, void *userData, void *buffer, int32_t length) {
    (void)renderer;
    (void)length;
    CodecUserData *codecUserData = static_cast<CodecUserData *>(userData);

    // Write the data to be played to the buffer by length
    uint8_t *dest = (uint8_t *)buffer;
    size_t index = 0;
    std::unique_lock<std::mutex> lock(codecUserData->outputMutex);
    // Retrieve the length of the data to be played from the queue
    while (!codecUserData->renderQueue.empty() && index < length) {
        dest[index++] = codecUserData->renderQueue.front();
        codecUserData->renderQueue.pop();
    }
    AVCODEC_SAMPLE_LOGD("render BufferLength:%{public}d Out buffer count: %{public}u, renderQueue.size: %{public}u "
                        "renderReadSize: %{public}u",
                        length, codecUserData->outputFrameCount, (unsigned int)codecUserData->renderQueue.size(),
                        (unsigned int)index);
    if (codecUserData->renderQueue.size() < length) {
        codecUserData->renderCond.notify_all();
    }
    return 0;
}
// Customize the audio stream event function
int32_t SampleCallback::OnRenderStreamEvent(OH_AudioRenderer *renderer, void *userData, OH_AudioStream_Event event) {
    (void)renderer;
    (void)userData;
    (void)event;
    // Update the player status and interface based on the audio stream event information represented by the event
    return 0;
}
// Customize the audio interrupt event function
int32_t SampleCallback::OnRenderInterruptEvent(OH_AudioRenderer *renderer, void *userData,
                                               OH_AudioInterrupt_ForceType type, OH_AudioInterrupt_Hint hint) {
    (void)renderer;
    (void)userData;
    (void)type;
    (void)hint;
    // Update the player status and interface based on the audio interrupt information indicated by type and hint
    return 0;
}
// Custom exception callback functions
int32_t SampleCallback::OnRenderError(OH_AudioRenderer *renderer, void *userData, OH_AudioStream_Result error) {
    (void)renderer;
    (void)userData;
    (void)error;
    AVCODEC_SAMPLE_LOGE("OnRenderError");
    // Handle the audio exception information based on the error message
    return 0;
}

void SampleCallback::OnCodecError(OH_AVCodec *codec, int32_t errorCode, void *userData) {
    (void)codec;
    (void)errorCode;
    (void)userData;
    AVCODEC_SAMPLE_LOGI("On codec error, error code: %{public}d", errorCode);
}

void SampleCallback::OnCodecFormatChange(OH_AVCodec *codec, OH_AVFormat *format, void *userData) {
    AVCODEC_SAMPLE_LOGI("On codec format change");
}

void SampleCallback::OnNeedInputBuffer(OH_AVCodec *codec, uint32_t index, OH_AVBuffer *buffer, void *userData) {
    if (userData == nullptr) {
        return;
    }
    (void)codec;
    CodecUserData *codecUserData = static_cast<CodecUserData *>(userData);
    std::unique_lock<std::mutex> lock(codecUserData->inputMutex);
    codecUserData->inputBufferInfoQueue.emplace(index, buffer);
    codecUserData->inputCond.notify_all();
}

void SampleCallback::OnNewOutputBuffer(OH_AVCodec *codec, uint32_t index, OH_AVBuffer *buffer, void *userData) {
    if (userData == nullptr || buffer == nullptr) {
        AVCODEC_SAMPLE_LOGE("Invalid parameters in OnNewOutputBuffer");
        return;
    }
    
    CodecUserData *codecUserData = static_cast<CodecUserData *>(userData);
    if (!codecUserData) {
        AVCODEC_SAMPLE_LOGE("Invalid codec user data");
        return;
    }

    try {
        std::unique_lock<std::mutex> lock(codecUserData->outputMutex);
        
        // 先获取缓冲区属性
        OH_AVCodecBufferAttr attr;
        if (OH_AVBuffer_GetBufferAttr(buffer, &attr) != 0) {
            AVCODEC_SAMPLE_LOGE("Failed to get buffer attributes");
            return;
        }
        
        // 创建临时对象并检查其有效性
        CodecBufferInfo tempInfo(index, buffer);
        if (!tempInfo.buffer) {
            AVCODEC_SAMPLE_LOGE("Failed to create buffer info");
            return;
        }
        
        // 使用push而不是emplace
        codecUserData->outputBufferInfoQueue.push(tempInfo);
        codecUserData->outputCond.notify_one();
        
    } catch (const std::exception& e) {
        AVCODEC_SAMPLE_LOGE("Exception in OnNewOutputBuffer: %{public}s", e.what());
    }
}
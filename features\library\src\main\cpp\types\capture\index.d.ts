/**
 * Screen capture state enum
 */
export enum CaptureState {
  UNKNOWN = 0,
  STARTED = 1,
  STOPPED = 2,
  INTERRUPTED = 3
}

/**
 * Response interface for initNative
 */
export interface Response {
  code: number;
}

/**
 * Initialize the native screen capture module
 * @returns Promise<Response> Returns a promise that resolves with the initialization result
 */
export function initNative(): Promise<Response>;

/**
 * Register a callback function to receive screen capture state changes
 * @param callback Function that will be called with the new state
 * @returns void
 */
export function registerCallback(callback: (state: CaptureState) => void): void;

/**
 * Release all resources used by the screen capture module.
 * This will stop any ongoing capture and clean up native resources.
 * @returns Promise<void>
 */
export function release(): Promise<void>;

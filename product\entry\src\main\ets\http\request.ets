import { http } from '@kit.NetworkKit'
import { BaseUrl } from './BasicUrl';
import { promptAction } from '@kit.ArkUI';
import { ResponsesData } from '../model';

async function requestHttp<T>(url : string = "" , method : http.RequestMethod = http.RequestMethod.GET , data ?: object ){
  // 为每个请求创建新的HTTP客户端实例，避免重复使用已销毁的实例
  const httpRequest = http.createHttp();
  console.info('[HTTP请求] 开始处理请求');
  console.info('[HTTP请求] 原始URL:', url);
  console.info('[HTTP请求] 请求方法:', method);
  console.info('[HTTP请求] 请求数据:', JSON.stringify(data));
  
  let urlStr = url.startsWith('http') ? url : BaseUrl + url;
  console.info('[HTTP请求] 处理后的URL:', urlStr);
  
  let extraData = ''
  if (method === http.RequestMethod.GET || method === http.RequestMethod.POST) {
    if (data && Object.keys(data).length) {
      console.info('[HTTP请求] 检测到请求参数，开始处理');
      if (method === http.RequestMethod.GET) {
        console.info('[HTTP请求] GET请求，将参数添加到URL');
        // 如果URL已经包含参数（有?标记），则使用&连接，否则使用?开始
        const connector = urlStr.includes('?') ? '&' : '?';
        const paramString = Object.keys(data).map(key => {
          if (data[key]) {
            return `${key}=${encodeURIComponent(data[key])}`
          }
          return ''
        }).filter(item => item !== '').join('&');
        
        urlStr += connector + paramString;
        console.info('[HTTP请求] GET请求最终URL:', urlStr);
      } else {
        console.info('[HTTP请求] POST请求，将参数添加到请求体');
        if (data && Object.keys(data).length) {
          extraData += Object.keys(data).map(key => {
            if (data[key]) {
              return `${key}=${encodeURIComponent(data[key])}`
            }
            return ''
          }).filter(item => item !== '').join('&')
        }
        console.info('[HTTP请求] POST请求体数据:', extraData);
      }
    } else {
      console.info('[HTTP请求] 无请求参数');
    }
  }

  let config: http.HttpRequestOptions = {
    method,
    readTimeout: 10000,
    extraData: method === http.RequestMethod.GET ? '' : extraData,
    header: {
      'Content-Type': method === http.RequestMethod.GET ? 'application/json' :
        'application/x-www-form-urlencoded;charset=UTF-8',
    }
  }
  
  console.info('[HTTP请求] 请求配置:', JSON.stringify(config));

  try {
    console.info('[HTTP请求] 开始发送HTTP请求');
    const res = await httpRequest.request(urlStr, config)
    console.info('[HTTP请求] 收到响应，状态码:', res.responseCode);
    console.info('[HTTP请求] 响应头:', JSON.stringify(res.header));
    
    if(res.responseCode !== 200){
      console.error('[HTTP请求] 请求失败，状态码:', res.responseCode);
      console.error('[HTTP请求] 响应结果:', res.result);
      throw new Error(`HTTP请求失败，状态码：${res.responseCode}`);
    }else {
      console.info('[HTTP请求] 请求成功，响应数据长度:', typeof res.result === 'string' ? res.result.length : 'unknown');
      console.info('[HTTP请求] 响应数据预览:', typeof res.result === 'string' ? res.result.substring(0, 200) + '...' : res.result);
      return res.result as T
    }
    
  } catch (error) {
    console.error('[HTTP请求] 请求异常:', error);
    console.error('[HTTP请求] 异常详情:', JSON.stringify(error));
    return Promise.reject(error)
  }
  finally {
    console.info('[HTTP请求] 销毁HTTP客户端');
    httpRequest.destroy()
  }
}

export class Request {
  static get<T>(url: string, data?: object): Promise<T> {
    console.info('[Request.get] 调用GET请求:', url);
    return requestHttp<T>(url, http.RequestMethod.GET, data)
  }

  static post<T>(url: string, data?: object): Promise<T> {
    console.info('[Request.post] 调用POST请求:', url);
    return requestHttp<T>(url, http.RequestMethod.POST, data)
  }

  static put<T>(url: string, data?: object): Promise<T> {
    console.info('[Request.put] 调用PUT请求:', url);
    return requestHttp<T>(url, http.RequestMethod.PUT, data)
  }

  static delete<T>(url: string, data?: object): Promise<T> {
    console.info('[Request.delete] 调用DELETE请求:', url);
    return requestHttp<T>(url, http.RequestMethod.DELETE, data)
  }
}
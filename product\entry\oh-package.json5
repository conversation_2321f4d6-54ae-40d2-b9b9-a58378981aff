{
  "name": "entry",
  "version": "1.0.0",
  "description": "Please describe the basic information.",
  "main": "",
  "author": "",
  "bundleName": "com.hs.hsmeeting",
  "license": "",
  "dependencies": {
    // "hsmeeting_hmos_sdk": "file:../../features/library",
//    "libentry.so": "file:./src/main/cpp/types/libentry",
    "routermoudel": "file:../../commons/RouterMoudel",
    "home": "file:../../features/home",
    "settings": "file:../../features/settings",
    'basic' : "file:../../commons/basic",
    "schedulemeeting" : "file:../../features/schedulemeeting",
    "createmeeting" : "file:../../features/createmeeting",
  }
}


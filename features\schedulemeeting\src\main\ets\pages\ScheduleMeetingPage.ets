import { BuilderNameConstants, RouterModule, RouterNameConstants } from 'routermoudel';
import router from '@ohos.router';
import { promptAction } from '@kit.ArkUI';
import { MeetingService, MeetingData } from '../services/MeetingService';
import emitter from '@ohos.events.emitter'; // 添加emitter导入
import { UserInfo } from 'basic';

@Component
export struct ScheduleMeetingPage {
  @State meetingTitle: string = 'administrator的会议';
  @State hostPassword: string = '';
  @State meetingPassword: string = '';
  @State startTime: string = '';
  @State endTime: string = '';
  @State meetingMode: string = '自由模式';
  @State participantCount: number = 10;
  @State isPublic: boolean = true;
  
  @State showStartDatePicker: boolean = false;
  @State showEndDatePicker: boolean = false;
  @State showMeetingModeSelector: boolean = false;
  @State showParticipantCountDialog: boolean = false;
  @State isScheduling: boolean = false; // 添加预约状态
  
  // 选中的日期和时间对象
  @State selectedStartDate: Date = new Date();
  @State selectedEndDate: Date = new Date();
  @State selectedStartTime: Date = new Date();
  @State selectedEndTime: Date = new Date();
  
  // 会议模式选项
  private meetingModes: string[] = ['自由模式', '主持人模式'];
  
  aboutToAppear() {
    // 设置默认开始时间和结束时间（当前时间后推1小时和2小时）
    const now = new Date();
    const startDate = new Date(now.getTime() + 60 * 60 * 1000); // 1小时后
    const endDate = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2小时后
    
    this.selectedStartDate = startDate;
    this.selectedEndDate = endDate;
    this.selectedStartTime = startDate;
    this.selectedEndTime = endDate;
    this.startTime = this.formatDate(startDate);
    this.endTime = this.formatDate(endDate);
  }
  
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
  
  // 合并日期和时间
  combineDateTime(date: Date, time: Date): Date {
    const combined = new Date(date);
    combined.setHours(time.getHours(), time.getMinutes(), 0, 0);
    return combined;
  }
  
  // 预约会议方法
  private async scheduleMeeting() {
    if (this.isScheduling) return;
    
    // 验证必填字段
    if (!this.meetingTitle.trim()) {
      promptAction.showToast({ message: '请输入会议主题' });
      return;
    }
    
    if (!this.startTime || !this.endTime) {
      promptAction.showToast({ message: '请选择会议时间' });
      return;
    }
    
    // 验证时间逻辑
    const startDate = new Date(this.startTime);
    const endDate = new Date(this.endTime);
    if (startDate >= endDate) {
      promptAction.showToast({ message: '结束时间必须晚于开始时间' });
      return;
    }

    this.isScheduling = true;
    
    try {
      const meetingService = MeetingService.getInstance();
      const meetingData: MeetingData = {
        subject: this.meetingTitle,
        startTime: this.startTime,
        endTime: this.endTime,
        attendeeAmount: this.participantCount,
        hostName: 'admin',
        creator: 'admin',
        openType: this.isPublic,
        passwd: this.meetingPassword || '123456',
        conferencePattern: this.meetingMode === '主持人模式' ? 0 : 1,  // 修正：主持人模式=0, 自由模式=1
        agenda: `会议主题：${this.meetingTitle}`,
        attendees: [
          {
            name: '张三',
            email: '<EMAIL>',
            phone: '13800138001',
            systemUser: 1
          },
          {
            name: '李四',
            email: '<EMAIL>',
            phone: '13800138002',
            systemUser: 1
          },
          {
            name: '王五',
            email: '<EMAIL>',
            phone: '13800138003',
            systemUser: 0
          }
        ]
      };

      const result = await meetingService.scheduleMeeting(meetingData);
      console.log('会议预约结果:', result);
      
      promptAction.showToast({ message: '会议预约成功' });

      emitter.emit('meetingScheduled');

      
      RouterModule.pop(RouterNameConstants.HomeIndex);
      
    } catch (error) {
      console.error('预约会议失败:', error);
      promptAction.showToast({ message: `预约失败: ${error.message}` });
    } finally {
      this.isScheduling = false;
    }
  }

  build() {
    NavDestination() {
      Stack() {
        Column() {
          // 表单内容
          List() {
            // 会议主题
            ListItem() {  
              Row() {
                Text('会议主题')
                  .fontSize(16)
                  .fontColor('#333333')
                
                TextInput({ placeholder: '请输入会议主题', text: this.meetingTitle })
                  .width('60%')
                  .fontSize(16)
                  .fontColor('#999999')
                  .backgroundColor(Color.Transparent)
                  .placeholderColor('#999999')
                  .textAlign(TextAlign.End)
                  .onChange((value: string) => {
                    this.meetingTitle = value;
                  })
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            .margin({ bottom: 8 })  // 添加底部间距
            
            // 会议密码
            ListItem() {
              Row() {
                Text('会议密码')
                  .fontSize(16)
                  .fontColor('#333333')
                
                Row() {
                  TextInput({ placeholder: '请输入会议密码', text: this.meetingPassword })
                    .width('80%')
                    .fontSize(16)
                    .fontColor('#999999')
                    .backgroundColor(Color.Transparent)
                    .placeholderColor('#999999')
                    .textAlign(TextAlign.End)
                    .onChange((value: string) => {
                      this.meetingPassword = value;
                    })
                  
                  Text('>')
                    .fontSize(16)
                    .fontColor('#999999')
                }
                .width('60%')
                .justifyContent(FlexAlign.End)
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            .margin({ bottom: 8 })  // 添加底部间距
            
            // 开始时间
            ListItem() {
              Row() {
                Text('开始时间')
                  .fontSize(16)
                  .fontColor('#333333')
                
                Row() {
                  Text(this.startTime)
                    .fontSize(16)
                    .fontColor('#999999')
                  
                  Text('>')
                    .fontSize(16)
                    .fontColor('#999999')
                    .margin({ left: 8 })
                }
                .width('60%')
                .justifyContent(FlexAlign.End)
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            .margin({ bottom: 8 })  // 添加底部间距
            .onClick(() => {
              this.showStartDatePicker = true;
            })
            
            // 结束时间
            ListItem() {
              Row() {
                Text('结束时间')
                  .fontSize(16)
                  .fontColor('#333333')
                
                Row() {
                  Text(this.endTime)
                    .fontSize(16)
                    .fontColor('#999999')
                  
                  Text('>')
                    .fontSize(16)
                    .fontColor('#999999')
                    .margin({ left: 8 })
                }
                .width('60%')
                .justifyContent(FlexAlign.End)
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            .margin({ bottom: 8 })  // 添加底部间距
            .onClick(() => {
              this.showEndDatePicker = true;
            })
            
            // 会议模式
            ListItem() {
              Row() {
                Text('会议模式')
                  .fontSize(16)
                  .fontColor('#333333')
                
                Row() {
                  Text(this.meetingMode)
                    .fontSize(16)
                    .fontColor('#999999')
                  
                  Text('>')
                    .fontSize(16)
                    .fontColor('#999999')
                    .margin({ left: 8 })
                }
                .width('60%')
                .justifyContent(FlexAlign.End)
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            .margin({ bottom: 8 })  // 添加底部间距
            .onClick(() => {
              this.showMeetingModeSelector = true;
            })
            
            // 参加人数
            ListItem() {
              Row() {
                Text('参加人数')
                  .fontSize(16)
                  .fontColor('#333333')
                
                Row() {
                  Text(this.participantCount.toString())
                    .fontSize(16)
                    .fontColor('#999999')
                  
                  Text('>')
                    .fontSize(16)
                    .fontColor('#999999')
                    .margin({ left: 8 })
                }
                .width('60%')
                .justifyContent(FlexAlign.End)
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            .margin({ bottom: 8 })  // 添加底部间距
            .onClick(() => {
              this.showParticipantCountDialog = true;
            })
            
            // 不公开会议
            ListItem() {
              Row() {
                Text('公开会议')
                  .fontSize(16)
                  .fontColor('#333333')
                
                Toggle({ type: ToggleType.Switch, isOn: this.isPublic })
                  .onChange((isOn: boolean) => {
                    this.isPublic = isOn;
                  })
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })
            }
            .backgroundColor(Color.White)
            // 最后一项不需要底部间距
          }
          .divider(null)  // 移除原有的分割线，因为现在用间距分隔
          .backgroundColor('#F5F5F5')
          .margin({ top: 12 })
          
          Blank()
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#F5F5F5')  // 添加整体背景色
        
        // 底部按钮
        Row() {
          Button(this.isScheduling ? '预约中...' : '完成')
            .width('100%')
            .height(48)
            .fontSize(16)
            .fontColor(Color.White)
            .backgroundColor(this.isScheduling ? '#CCCCCC' : '#0A59F7')
            .borderRadius(8)
            .enabled(!this.isScheduling)
            .onClick(() => {
              this.scheduleMeeting();
            })
        }
        .width('100%')
        .padding(16)
        .position({ x: 0, y: '100%' })
        .translate({ x: 0, y: -80 })
        
        // 开始时间选择器（日期+时间）
        if (this.showStartDatePicker) {
          Column() {
            // 顶部标题栏，增加完成按钮
            Row() {
              Text('选择开始时间')
                .fontSize(18)
                .fontWeight(FontWeight.Bold)
              
              // 右上角完成按钮
              Text('完成')
                .fontSize(16)
                .fontColor('#0A59F7')
                .fontWeight(FontWeight.Medium)
                .onClick(() => {
                  const combined = this.combineDateTime(this.selectedStartDate, this.selectedStartTime);
                  this.startTime = this.formatDate(combined);
                  this.showStartDatePicker = false;
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceBetween)
            .margin({ bottom: 20 })
            
            // 日期选择器
            Text('选择日期')
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .margin({ bottom: 10 })
              .alignSelf(ItemAlign.Start)
            
            DatePicker({
              start: new Date('1970-1-1'),
              end: new Date('2030-1-1'),
              selected: this.selectedStartDate
            })
            .disappearTextStyle({color: Color.Gray, font: {size: '16fp', weight: FontWeight.Bold}})
            .textStyle({color: '#ff182431', font: {size: '18fp', weight: FontWeight.Normal}})
            .selectedTextStyle({color: '#ff0000FF', font: {size: '26fp', weight: FontWeight.Regular}})
            .onDateChange((value: Date) => {
              this.selectedStartDate = value;
              const combined = this.combineDateTime(value, this.selectedStartTime);
              this.startTime = this.formatDate(combined);
              console.info('select start date is: ' + value.toString());
            })
            
            // 时间选择器
            Text('选择时间')
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .margin({ top: 20, bottom: 10 })
              .alignSelf(ItemAlign.Start)
            
            TimePicker({
              selected: this.selectedStartTime
            })
            .useMilitaryTime(true)
            .onChange((value: TimePickerResult) => {
              if(value.hour >= 0) {
                this.selectedStartTime.setHours(value.hour, value.minute);
                const combined = this.combineDateTime(this.selectedStartDate, this.selectedStartTime);
                this.startTime = this.formatDate(combined);
                console.info('select start time is: ' + JSON.stringify(value));
              }
            })
            .disappearTextStyle({color: Color.Gray, font: {size: '16fp', weight: FontWeight.Bold}})
            .textStyle({color: '#ff182431', font: {size: '18fp', weight: FontWeight.Normal}})
            .selectedTextStyle({color: '#ff0000FF', font: {size: '26fp', weight: FontWeight.Regular}})
            
            // 底部按钮组
            Row() {
              Button('取消')
                .backgroundColor(Color.Transparent)
                .fontColor('#666666')
                .onClick(() => {
                  this.showStartDatePicker = false;
                })
              
              Button('确定')
                .backgroundColor('#0A59F7')
                .fontColor(Color.White)
                .onClick(() => {
                  const combined = this.combineDateTime(this.selectedStartDate, this.selectedStartTime);
                  this.startTime = this.formatDate(combined);
                  this.showStartDatePicker = false;
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceAround)
            .margin({ top: 20 })
          }
          .width('100%')
          .padding(20)
          .backgroundColor(Color.White)
          .borderRadius({ topLeft: 12, topRight: 12 })
          .position({ x: 0, y: '100%' })
          .translate({ x: 0, y: -450 })
          // 添加点击外部区域关闭功能
          .onClick(() => {
            // 阻止事件冒泡，避免点击选择器内部时关闭
          })
        }
        
        // 结束时间选择器（日期+时间）
        if (this.showEndDatePicker) {
          Column() {
            // 顶部标题栏，增加完成按钮
            Row() {
              Text('选择结束时间')
                .fontSize(18)
                .fontWeight(FontWeight.Bold)
              
              // 右上角完成按钮
              Text('完成')
                .fontSize(16)
                .fontColor('#0A59F7')
                .fontWeight(FontWeight.Medium)
                .onClick(() => {
                  const combined = this.combineDateTime(this.selectedEndDate, this.selectedEndTime);
                  this.endTime = this.formatDate(combined);
                  this.showEndDatePicker = false;
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceBetween)
            .margin({ bottom: 20 })
            
            // 日期选择器
            Text('选择日期')
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .margin({ bottom: 10 })
              .alignSelf(ItemAlign.Start)
            
            DatePicker({
              start: new Date('1970-1-1'),
              end: new Date('2030-1-1'),
              selected: this.selectedEndDate
            })
            .disappearTextStyle({color: Color.Gray, font: {size: '16fp', weight: FontWeight.Bold}})
            .textStyle({color: '#ff182431', font: {size: '18fp', weight: FontWeight.Normal}})
            .selectedTextStyle({color: '#ff0000FF', font: {size: '26fp', weight: FontWeight.Regular}})
            .onDateChange((value: Date) => {
              this.selectedEndDate = value;
              const combined = this.combineDateTime(value, this.selectedEndTime);
              this.endTime = this.formatDate(combined);
              console.info('select end date is: ' + value.toString());
            })
            
            // 时间选择器
            Text('选择时间')
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .margin({ top: 20, bottom: 10 })
              .alignSelf(ItemAlign.Start)
            
            TimePicker({
              selected: this.selectedEndTime
            })
            .useMilitaryTime(true)
            .onChange((value: TimePickerResult) => {
              if(value.hour >= 0) {
                this.selectedEndTime.setHours(value.hour, value.minute);
                const combined = this.combineDateTime(this.selectedEndDate, this.selectedEndTime);
                this.endTime = this.formatDate(combined);
                console.info('select end time is: ' + JSON.stringify(value));
              }
            })
            .disappearTextStyle({color: Color.Gray, font: {size: '16fp', weight: FontWeight.Bold}})
            .textStyle({color: '#ff182431', font: {size: '18fp', weight: FontWeight.Normal}})
            .selectedTextStyle({color: '#ff0000FF', font: {size: '26fp', weight: FontWeight.Regular}})
            
            // 底部按钮组
            Row() {
              Button('取消')
                .backgroundColor(Color.Transparent)
                .fontColor('#666666')
                .onClick(() => {
                  this.showEndDatePicker = false;
                })
              
              Button('确定')
                .backgroundColor('#0A59F7')
                .fontColor(Color.White)
                .onClick(() => {
                  const combined = this.combineDateTime(this.selectedEndDate, this.selectedEndTime);
                  this.endTime = this.formatDate(combined);
                  this.showEndDatePicker = false;
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceAround)
            .margin({ top: 20 })
          }
          .width('100%')
          .padding(20)
          .backgroundColor(Color.White)
          .borderRadius({ topLeft: 12, topRight: 12 })
          .position({ x: 0, y: '100%' })
          .translate({ x: 0, y: -450 })
          // 添加点击外部区域关闭功能
          .onClick(() => {
            // 阻止事件冒泡，避免点击选择器内部时关闭
          })
        }
        
        // 会议模式选择器
        if (this.showMeetingModeSelector) {
          Column() {
            Text('选择会议模式')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ bottom: 20 })
            
            ForEach(this.meetingModes, (mode: string) => {
              Row() {
                Text(mode)
                  .fontSize(16)
                  .fontColor('#333333')
                
                if (this.meetingMode === mode) {
                  Text('✓')
                    .fontSize(16)
                    .fontColor('#0A59F7')
                }
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
              .padding(16)
              .onClick(() => {
                this.meetingMode = mode;
                this.showMeetingModeSelector = false;
              })
            })
            
            Button('取消')
              .width('100%')
              .backgroundColor(Color.Transparent)
              .fontColor('#666666')
              .margin({ top: 20 })
              .onClick(() => {
                this.showMeetingModeSelector = false;
              })
          }
          .width('100%')
          .padding(20)
          .backgroundColor(Color.White)
          .borderRadius({ topLeft: 12, topRight: 12 })
          .position({ x: 0, y: '100%' })
          .translate({ x: 0, y: -250 })
        }
        
        // 参加人数输入对话框
        if (this.showParticipantCountDialog) {
          Column() {
            Text('设置参加人数')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ bottom: 20 })
            
            TextInput({ placeholder: '请输入参加人数', text: this.participantCount.toString() })
              .width('100%')
              .fontSize(16)
              .type(InputType.Number)
              .onChange((value: string) => {
                const num = parseInt(value);
                if (!isNaN(num) && num > 0) {
                  this.participantCount = num;
                }
              })
            
            Row() {
              Button('取消')
                .backgroundColor(Color.Transparent)
                .fontColor('#666666')
                .onClick(() => {
                  this.showParticipantCountDialog = false;
                })
              
              Button('确定')
                .backgroundColor('#0A59F7')
                .fontColor(Color.White)
                .onClick(() => {
                  this.showParticipantCountDialog = false;
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceAround)
            .margin({ top: 20 })
          }
          .width('100%')
          .padding(20)
          .backgroundColor(Color.White)
          .borderRadius({ topLeft: 12, topRight: 12 })
          .position({ x: 0, y: '100%' })
          .translate({ x: 0, y: -200 })
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F5F5F5')
    }
    .title('预约会议')
    .onBackPressed(() => {
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }
}

// 注册构建器
@Builder
export function ScheduleMeeting_Page(value: object) {
  ScheduleMeetingPage()
}

const builderName = BuilderNameConstants.ScheduleMeetingPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(ScheduleMeeting_Page);
  RouterModule.registerBuilder(builderName, builder);
}
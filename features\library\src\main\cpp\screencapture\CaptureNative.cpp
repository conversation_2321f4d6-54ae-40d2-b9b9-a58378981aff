//
// Created on 2025/3/20.
//
// Node APIs are not fully supported. To solve the compilation error of the interface cannot be found,
// please include "napi/native_api.h".

#include <cstdint>
#include <iostream>
#include <thread>
#include <mutex>
#include <memory>
#include <atomic>
#include <condition_variable>
#include <node_api.h>
#include <js_native_api.h>
#include <js_native_api_types.h>
#include <napi/native_api.h>
#include "VideoData.h"

#include "CaptureNative.h"
#include <multimedia/player_framework/native_avscreen_capture.h>
#include <multimedia/player_framework/native_avscreen_capture_base.h>
#include <multimedia/player_framework/native_avformat.h>
#include <multimedia/player_framework/native_avscreen_capture_errors.h>
#include <native_buffer/native_buffer.h>
#include <fcntl.h>
#include "screencapture/Capture.h"
#include <string>
#include <unistd.h>
#include <multimedia/player_framework/native_avcodec_videoencoder.h>
#include <multimedia/player_framework/native_avcapability.h>
#include <multimedia/player_framework/native_avcodec_base.h>
#include <multimedia/player_framework/native_avformat.h>
#include <multimedia/player_framework/native_avbuffer.h>
#include <fstream>
#include "capbilities/include/VideoEncoder.h"


static OH_AVScreenCapture* g_capture = nullptr;
static VideoEncoder* g_videoEncoder = nullptr;
static int32_t g_videoWidth = 720;  // 修改点3：定义默认分辨率
static int32_t g_videoHeight = 1280;

// 直接传输数据结构体 (不需要文件)
struct EncoderCallbackData {
    // 不再需要输出文件
    // 可以添加其他需要的数据...
};

// 编码器回调函数 - 处理编码后的数据
void OnNewEncodedData(OH_AVCodecBufferAttr attr, OH_AVMemory *data, void* userData) {
    if (data) {
        // 获取编码后的数据缓冲区
        uint8_t* buffer = reinterpret_cast<uint8_t*>(OH_AVMemory_GetAddr(data));
        if (buffer) {
            // 确定是否为关键帧
            bool isKeyFrame = (attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) != 0;

            // 使用sendAsData函数直接发送编码后的数据
            // 假设H.264编码的比特深度为8
//            int result = sendAsData(buffer, attr.size, isKeyFrame, g_videoWidth, g_videoHeight, true, 8);
//            
//            printf("已发送编码帧，大小: %d 字节，关键帧: %d，结果: %d\n", 
//                   attr.size, isKeyFrame ? 1 : 0, result);
        }
    }
}



// 定义用户数据结构体
struct CaptureUserData {
    napi_env env;
    napi_ref callback;
    // 其他需要的数据...
};

void CaptureNative::OnError(OH_AVScreenCapture* capture, int32_t error, void* userData) {
    if (userData != nullptr) {
        CaptureUserData* data = static_cast<CaptureUserData*>(userData);
        // 使用 data 中的数据...
    }
    std::cout << "录屏错误: " << error << std::endl;
}

// 初始化静态成员
napi_ref CaptureNative::stopCallback_ = nullptr;
napi_env CaptureNative::env_ = nullptr;
napi_threadsafe_function CaptureNative::tsfn_ = nullptr;

// 添加注册回调的实现
void NAPI_CALL(struct napi_env__ * env, napi_status n) {

}

// 实现新的 CallJs 函数
void CaptureNative::CallJs(napi_env env, napi_value js_cb, void* context, void* data) {
    if (data != nullptr) {
        // 从传入的数据中获取状态值
        int32_t state = *static_cast<int32_t*>(data);
        // 释放数据内存
        delete static_cast<int32_t*>(data);

        napi_value undefined;
        napi_get_undefined(env, &undefined);
        
        napi_value argv[1];
        napi_create_int32(env, state, &argv[0]);
        
        napi_value result;
        napi_status status = napi_call_function(env, undefined, js_cb, 1, argv, &result);
        if (status != napi_ok) {
            std::cout << "Failed to call JavaScript callback function" << std::endl;
        }
    }
}

napi_value CaptureNative::RegisterCallback(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1];
    napi_status status = napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
    
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to get callback info");
        return nullptr;
    }

    if (argc < 1) {
        napi_throw_error(env, nullptr, "需要一个回调函数参数");
        return nullptr;
    }

    // Store the environment
    env_ = env;

    // If there's an existing callback reference, delete it
    if (stopCallback_ != nullptr) {
        napi_delete_reference(env, stopCallback_);
        stopCallback_ = nullptr;
    }

    // Create a reference to the callback function
    status = napi_create_reference(env, args[0], 1, &stopCallback_);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to create reference to callback");
        return nullptr;
    }

    // If there's an existing threadsafe function, release it
    if (tsfn_ != nullptr) {
        napi_release_threadsafe_function(tsfn_, napi_tsfn_release);
        tsfn_ = nullptr;
    }

    // Create a resource name for the threadsafe function
    napi_value resource_name;
    status = napi_create_string_utf8(env, "screenCaptureCallback", NAPI_AUTO_LENGTH, &resource_name);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to create resource name");
        return nullptr;
    }
    
    // Create the threadsafe function
    status = napi_create_threadsafe_function(
        env,
        args[0],              // callback function
        nullptr,             // async_resource
        resource_name,       // async_resource_name
        0,                  // max_queue_size
        1,                  // initial_thread_count
        nullptr,            // thread_finalize_data
        nullptr,            // thread_finalize_cb
        nullptr,            // context
        CallJs,             // call_js_cb
        &tsfn_             // result
    );
    
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to create threadsafe function");
        return nullptr;
    }

    std::cout << "Callback registered successfully" << std::endl;

    napi_value result;
    napi_get_undefined(env, &result);
    return result;
}

void CaptureNative::OnStateChange(OH_AVScreenCapture* capture, OH_AVScreenCaptureStateCode stateCode, void* userData) {
    (void)capture;
    (void)userData;
    
    int32_t callbackState = 0;
    
    switch (stateCode) {
        case OH_SCREEN_CAPTURE_STATE_STARTED:
            std::cout << "Screen capture started" << std::endl;
            callbackState = 1;
            break;
        case OH_SCREEN_CAPTURE_STATE_STOPPED_BY_USER:
        case OH_SCREEN_CAPTURE_STATE_STOPPED_BY_CALL:
        case OH_SCREEN_CAPTURE_STATE_STOPPED_BY_USER_SWITCHES:
            std::cout << "Screen capture stopped" << std::endl;
            callbackState = 2;
            // Cleanup resources when stopped
//            if (g_capture != nullptr) {
//                OH_AVScreenCapture_Release(g_capture);
//                g_capture = nullptr;
//            }
            break;
        case OH_SCREEN_CAPTURE_STATE_INTERRUPTED_BY_OTHER:
            std::cout << "Screen capture interrupted" << std::endl;
            callbackState = 3;
            break;
        default:
            break;
    }

    // 确保 tsfn_ 存在再调用
    if (tsfn_ != nullptr) {
        // 在堆上分配数据，让回调函数负责释放
        int32_t* data = new int32_t(callbackState);
        napi_status status = napi_call_threadsafe_function(tsfn_, data, napi_tsfn_blocking);
        if (status != napi_ok) {
            std::cout << "Failed to call threadsafe function" << std::endl;
            delete data;
        }
    }
}

// 在析构或清理时释放线程安全函数
void CaptureNative::Release() {
    // 释放线程安全函数
    if (tsfn_ != nullptr) {
        napi_release_threadsafe_function(tsfn_, napi_tsfn_release);
        tsfn_ = nullptr;
    }

    // 删除回调引用
    if (stopCallback_ != nullptr) {
        napi_delete_reference(env_, stopCallback_);
        stopCallback_ = nullptr;
    }
}

// 获取并处理音视频原始码流数据回调函数OnBufferAvailable()。
void CaptureNative::OnBufferAvailable(OH_AVScreenCapture *capture, OH_AVBuffer *buffer, 
    OH_AVScreenCaptureBufferType bufferType, int64_t timestamp, void *userData) {
    // 处于录屏取码流状态。
    if (bufferType == OH_SCREEN_CAPTURE_BUFFERTYPE_VIDEO) {
/*        
        // 视频buffer。
        OH_NativeBuffer *nativeBuffer = OH_AVBuffer_GetNativeBuffer(buffer);
        if (nativeBuffer != nullptr && capture != nullptr) {
            // 获取buffer属性。
            OH_AVCodecBufferAttr info;
            OH_AVBuffer_GetBufferAttr(buffer, &info);
*/            
        OH_AVCodecBufferAttr attr = {0, 0, 0, AVCODEC_BUFFER_FLAGS_NONE};
        OH_AVBuffer_GetBufferAttr(buffer, &attr);
        uint8_t* encodedData = OH_AVBuffer_GetAddr(buffer) + attr.offset;
 
        bool bKeyFrame = (attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) != 0;
        
//        int result = sendAsData(encodedData, attr.size, bKeyFrame, g_videoWidth, g_videoHeight, true, 32);
        
            // 处理帧数据
            // 注意：使用Surface模式时，编码器已经配置为使用与屏幕捕获相同的surface
            // 所以不需要手动将数据传递给编码器
            
            // 使用完毕后释放原生缓冲区
//            OH_NativeBuffer_Unreference(nativeBuffer);
    } else if (bufferType == OH_SCREEN_CAPTURE_BUFFERTYPE_AUDIO_INNER) {
        // 内录buffer。
        // 获取buffer属性。
        OH_AVCodecBufferAttr info;
        OH_AVBuffer_GetBufferAttr(buffer, &info);

        // 获取buffer容量。
        int bufferLen = OH_AVBuffer_GetCapacity(buffer);

        // 获取buffer地址。
        uint8_t *buf = OH_AVBuffer_GetAddr(buffer);
        if (buf != nullptr) {
            return;
        }
        // 使用buffer数据。
    } else if (bufferType == OH_SCREEN_CAPTURE_BUFFERTYPE_AUDIO_MIC) {
        // 麦克风buffer。
        // 获取buffer容量。
        int bufferLen = OH_AVBuffer_GetCapacity(buffer);

        // 获取buffer地址。
        uint8_t *buf = OH_AVBuffer_GetAddr(buffer);
        if (buf != nullptr) {
            return;
        }
        // 使用buffer数据。
    }
}

napi_value CaptureNative::Init(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
    
    // 如果屏幕捕获实例尚未创建，则创建它
    if (g_capture == nullptr) {
        g_capture = OH_AVScreenCapture_Create();
        if (g_capture == nullptr) {
            napi_throw_error(env, nullptr, "Failed to create screen capture instance");
            return nullptr;
        }
        
        // Create user data for callbacks
        CaptureUserData* userData = new CaptureUserData();
        userData->env = env;
        
        // Set callbacks
        OH_AVScreenCapture_SetErrorCallback(g_capture, OnError, userData);
        OH_AVScreenCapture_SetStateCallback(g_capture, OnStateChange, userData);
        OH_AVScreenCapture_SetDataCallback(g_capture, OnBufferAvailable, userData);
        
        
        // Configure screen capture
        OH_AudioCaptureInfo micCapInfo = {
            .audioSampleRate = 48000,
            .audioChannels = 2,
            .audioSource = OH_MIC
        };
        
        OH_AudioCaptureInfo innerCapInfo = {
            .audioSampleRate = 48000,
            .audioChannels = 2,
            .audioSource = OH_ALL_PLAYBACK
        };
        
        OH_VideoCaptureInfo videoCapInfo = {
            .videoFrameWidth = g_videoWidth,
            .videoFrameHeight = g_videoHeight,
            .videoSource = OH_VIDEO_SOURCE_SURFACE_RGBA
        };
        
        OH_AudioInfo audioInfo = {
            .micCapInfo = micCapInfo,
            .innerCapInfo = innerCapInfo
        };
        
        OH_VideoInfo videoInfo = {
            .videoCapInfo = videoCapInfo,
        };
        
        OH_AVScreenCaptureConfig config = {
            .captureMode = OH_CAPTURE_HOME_SCREEN,
            .dataType = OH_ORIGINAL_STREAM,//OH_ENCODED_STREAM
            .audioInfo = audioInfo,
            .videoInfo = videoInfo
        };
        
        // Initialize screen capture with config
        int32_t ret = OH_AVScreenCapture_Init(g_capture, config);
        if (ret != 0) {
            napi_throw_error(env, nullptr, "Failed to initialize screen capture");
            OH_AVScreenCapture_Release(g_capture);
            g_capture = nullptr;
            delete userData;
            return nullptr;
        }
    
            
        // 设置SampleInfo（采样信息）
        SampleInfo sampleInfo;
        sampleInfo.videoWidth = g_videoWidth;        // 视频宽度
        sampleInfo.videoHeight = g_videoHeight;      // 视频高度
        sampleInfo.frameRate = 4.0;                 // 帧率
        sampleInfo.bitrate = 500000;                // 4 Mbps比特率
//      sampleInfo.hevcProfile = 0;                  // 编码器配置文件
        sampleInfo.videoCodecMime = MIME_VIDEO_AVC;
        
        Capture::GetInstance().Init(sampleInfo);
        ret = Capture::GetInstance().Start();
            
        if (ret != 0) {
            napi_throw_error(env, nullptr, "启动视频编码器失败");
            return nullptr;
        }
        
        // 启动编码输出线程
        // 关键改动：将编码器的surface与屏幕捕获绑定
        // 启动屏幕捕获并使用编码器的surface
        ret = OH_AVScreenCapture_StartScreenCaptureWithSurface(g_capture, sampleInfo.window);
        if (ret != 0) {
            napi_throw_error(env, nullptr, "启动带surface的屏幕捕获失败");
            return nullptr;
        }
        // Enable microphone by default
        OH_AVScreenCapture_SetMicrophoneEnabled(g_capture, false);
    }
    
    napi_value result;
    napi_get_boolean(env, true, &result);
    return result;
}

// Add this new function after Init()
napi_value CaptureNative::ReleaseNative(napi_env env, napi_callback_info info) {
    // 创建一个异步工作来处理释放操作
    napi_async_work work;
    napi_value workName;
    napi_create_string_utf8(env, "releaseWork", NAPI_AUTO_LENGTH, &workName);

    // 在工作线程中执行释放操作
    napi_create_async_work(env, nullptr, workName,
        // Execute callback - 在工作线程中运行
        [](napi_env env, void* data) {
            if (g_capture != nullptr) {
                // 等待任何挂起的操作完成
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                
                // 先停止编码器
                Capture::GetInstance().StartRelease();
                
                // 释放屏幕捕获
                OH_AVScreenCapture_Release(g_capture);
                g_capture = nullptr;
            }
        },
        // Complete callback - 在主线程中运行
        [](napi_env env, napi_status status, void* data) {
            // 释放回调和线程安全函数
            if (tsfn_ != nullptr) {
                napi_release_threadsafe_function(tsfn_, napi_tsfn_abort);
                tsfn_ = nullptr;
            }

            if (stopCallback_ != nullptr && env_ != nullptr) {
                napi_delete_reference(env_, stopCallback_);
                stopCallback_ = nullptr;
            }

            Release();
        },
        nullptr, &work);

    // 启动异步工作
    napi_queue_async_work(env, work);

    napi_value result;
    napi_get_undefined(env, &result);
    return result;
}

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports) {
    napi_property_descriptor desc[] = {
        {"initNative", nullptr, CaptureNative::Init, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"registerCallback", nullptr, CaptureNative::RegisterCallback, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"release", nullptr, CaptureNative::ReleaseNative, nullptr, nullptr, nullptr, napi_default, nullptr},
    };

    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module captureModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "capture",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

extern "C" __attribute__((constructor)) void RegisterEncoderModule(void) {
    napi_module_register(&captureModule);
}

// 定义静态成员变量
std::unique_ptr<std::thread> CaptureNative::releaseThread_;
std::mutex CaptureNative::mutex_;
std::atomic<bool> CaptureNative::isStarted_(false);
std::condition_variable CaptureNative::doneCond_;

// 实现缺少的StartRelease方法
void CaptureNative::StartRelease() {
    // StartRelease的实现
    std::lock_guard<std::mutex> lock(mutex_);
    isStarted_ = false;
    doneCond_.notify_all();
} 

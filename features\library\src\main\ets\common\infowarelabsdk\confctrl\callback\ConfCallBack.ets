
/**
 * C代码与Java之间的回调接口,处理相关逻辑
 *
 * <AUTHOR>
 */
export interface ConfCallback{

  /**
   * 加会结果,C回调方法
   *
   * @param result ：0 成功 非0 失败
   */

  onJoinConference : (result : number) => number

  /**
   * 离开会议的回调
   *
   * @param result ：
   */

  onConferenceLeave : (result : number) => number

  /**
   * 更新用户列表,C回调方法
   *
   * @param action 操作类型 (0 is add,i is remove 2 is change host)
   * @param uid    用户ID
   * @param name   用户名
   * @param role   角色( 0 = attendee 1 = host 2 = presentor)
   */

  onRosterUpdate : (action : number , uid : number , name : string , role : number) => void

  /**
   * 角色更改，C回调方法
   *
   * @param uid     用戶ID
   * @param newRole 新角色
   */

  onUserRole : (uid : number , newRole : number) => void

  /**
   * 权限设置
   *
   * @param type  权限类型
   * @param state 状态
   */

  onUpdateUserPriviledge : (type : number , state : boolean) => void

  onRemoveUser : (action : number , uid : number) => void

  /**
   * 录播操作失败
   *
   * @param isBegin true表示打开录播失败，false表示关闭录播失败
   */
  onBeginRecordFailed : (isBegin : number) => void

  /**
   * 录播操作成功
   *
   * @param isBegin true表示打开录播成功，false表示关闭录播成功
   */
  onRecordNotify : (isBegin : boolean) => void

  /**
   * 呼叫手机结果回调
   *
   * @param phoneNum  手机号码(utf-8)
   * @param isSuccess 是否呼叫成功
   */
  onInvitePhoneConfirm : (phoneNum : string , isSuccess : boolean) => void

  onCallAtt : (response : boolean , timeout : number , id : number) => void

  onTransparentRecvData : (data : Array<number> , length : number) => void

  onRecordStateResponse : (state : number , isRecording : boolean) => void

  onRecordStopRequest : (srcuserid : number , dstuserid : number) => void

  onSubtitles : (bShow : boolean , strText : string) => void

  onFirstJoin : (bFirst : boolean) => void

  onInviteH323Response : (arg1 : number , arg2 : string , arg3 : string ,arg4 : string) => void

  onConferenceSupportH323 : (arg1 : boolean , arg2 : number , agr3 : number , arg4 : number , agr5 : number ,arg6 : number) => void

  onConferenceSupportH3231 : (arg1 : boolean , arg2 : number) => void

  onInviteH3231Response : (arg2 : number) => void

  onUserViewState : (type : number) => void

  OnLiveState : (type : number) => void

  //权限问题
  onAsPriviledge : (bAs : number) => void

  onVideoAdaptive : (bVideoAdaptive : boolean) => void

  onConfAvOpenConfirm : (bAvOpenConfirm : boolean) => void

  onConfMode : (bFreeMode : boolean) => void

  OnConfFlag : (flag : number) => void

  onNetworkSpeed : (nSpeed : number , nGoodSpeed : number , nBadSpeed : number) => void

  onHandUp : (userid : number ,handUp : boolean) => void

  onRequest : (type : number , userId : number , channelId : number , data1 : number , data2 : number , strData : string) => void

  onResponse : (type : number , userId : number , channelId : number , data1 : number , data2 : number , strData : string) => void
}

import { RouterModule } from '../utils/RouterModule';

export class RouterModel{
  builderName:string = '';
  routerName:string = '';
  params?:object = new Object()
}

export function builderRouterModel(routerName:string,builderName:string,param?:object){
  let router:RouterModel = new RouterModel()
  router.routerName = routerName
  router.builderName = builderName
  router.params = param
  RouterModule.push(router)
}
import { emitter } from '@kit.BasicServicesKit'
import { WhiteBarSet } from '.'
import { removeAS_Data, sendAS_Data, StopShareDoc, switchDoc } from '../common/infowarelabsdk/shareDs'
import { ANTINFO, Participant, ParticipantClass } from '../models'

export interface IWhiteBoard {
  id: number
  title: string
  width?: number
  height?: number
  imageBitmap?: ImageBitmap
}

// 文档共享组件
@Component
export struct WhiteBoard {
  @Prop rotation: number
  @Prop isOpenSetting: boolean
  @Prop userData: Participant
  @Prop participants: ParticipantClass[]
  @Link allAntInfos: ANTINFO[] //所有线条
  @Link whiteBoards: IWhiteBoard[] // 白板数组
  @Link board: IWhiteBoard //当前白板对象
  @State isShowBoardSelectMenu: boolean = false
  //--------------------------------------------------------------------------
  antInfos: ANTINFO[] = [] //当前白板的线条集合
  canvasContext: CanvasRenderingContext2D = new CanvasRenderingContext2D()
  strokeStyle: string = '#000000'
  setIndex: number = -1
  line: DrawLine[] = [] //绘制线条的临时存储
  //点击颜色设置按钮回调
  openSettingBarClick: (isOpen: boolean) => void = () => {
  }

  aboutToAppear(): void {
    this.registerEmitters()
    this.canvasContext.lineWidth = 1
    this.canvasContext.strokeStyle = Color.Black
  }

  aboutToDisappear(): void {
    this.removeEmitters()
  }

  build() {
    RelativeContainer() {

      Canvas(this.canvasContext)
        .constraintSize({ maxWidth: '100%', maxHeight: '100%' })
        .aspectRatio((this.board.width!) / (this.board.height!))
        .backgroundColor(Color.White)
        .onReady(() => this.initCanvas())//
        .onTouch((event: TouchEvent) => this.handleTouch(event))// 绑定触摸事件
        .alignRules({
          center: { anchor: "__container__", align: VerticalAlign.Center },
          middle: { anchor: "__container__", align: HorizontalAlign.Center }
        })

      Image(this.isOpenSetting ? $r('app.media.icon_ds_tools_on') : $r('app.media.icon_ds_tools_normal'))
        .width(40)
        .aspectRatio(1)
        .id('hss_setIcon')
        .alignRules({
          left: { anchor: "__container__", align: HorizontalAlign.Start },
          bottom: { anchor: "__container__", align: VerticalAlign.Bottom }
        })
        .offset({ x: 10, y: -15 })
        .onClick(() => {
          if (this.isOpenSetting) {
            this.openSettingBarClick(false)
            this.setIndex = -1
            //绘制前线删除原来的线条
            const ants = this.allAntInfos.filter(ant => (ant.ownerId === this.userData.uid && ant.type === 167))
            if (ants.length !== 0) {
              ants.forEach(ant => {
                removeAS_Data(ant)
                const index = this.allAntInfos.findIndex(a => a.id === ant.id)
                this.allAntInfos.splice(index, 1)
              })
            }
          } else {
            this.openSettingBarClick(true)
          }
        })

      Text('更换白板')
        .fontSize(14)
        .borderRadius(20)
        .offset({ x: 10 + 20, y: -15 })
        .textAlign(TextAlign.Center)
        .backgroundColor('#fface0ce')
        .alignRules({
          left: { anchor: "hss_setIcon", align: HorizontalAlign.End },
          center: { anchor: "hss_setIcon", align: VerticalAlign.Center }
        })
        .padding({
          left: 10,
          right: 10,
          top: 5,
          bottom: 5
        })
        .onClick(() => {
          this.isShowBoardSelectMenu = true
        })
        .bindContextMenu(this.isShowBoardSelectMenu, this.MenuBuilder(), {
          placement: Placement.TopRight,
          borderRadius: 5,
          backgroundBlurStyle: BlurStyle.NONE,
          backgroundColor: '#ffc4c1c1',
          offset: { x: 0, y: -10 },
          onDisappear: () => {
            console.log('run onDisappear')
            this.isShowBoardSelectMenu = false
          }
        })

      Text(this.board.title)
        .fontColor(Color.Gray)
        .alignRules({
          right: { anchor: "__container__", align: HorizontalAlign.End },
          bottom: { anchor: "__container__", align: VerticalAlign.Bottom }
        })
        .offset({ x: -2, y: -15 })

    }
    .width('100%')
    .height('100%')
    .backgroundColor('#ffececec')
  }

  @Builder
  MenuBuilder() {
    Row() {
      List({ space: 10 }) {
        ForEach(this.whiteBoards, (board: IWhiteBoard, index) => {
          ListItem() {
            Column({ space: 10 }) {
              Text()
                .backgroundColor(Color.White)
                .layoutWeight(1)
                .width('100%')

              Text(board.title)
                .width('100%')
                .fontSize(14)
                .textAlign(TextAlign.Center)
                .maxLines(1)
                .textOverflow({ overflow: TextOverflow.Ellipsis })
            }
            .padding({ top: 5, bottom: 5 })
            .width(60)
            .height('100%')
            .onClick(() => {
              //切换到当前白板
              switchDoc(board.id)
              this.board = this.whiteBoards[index]
              // this.currentBoardIndex = index
            })
          }
        })
      }
      .width('100%')
      .height('100%')
      .listDirection(Axis.Horizontal)
      .scrollBar(BarState.Off)
    }
    .width(210)
    .padding(10)
    .height(130)
    .borderRadius(5)
    .backgroundColor('#ffc4c1c1')

  }

  //初始化画布（横屏时会触发刷新）
  private initCanvas() {
    console.log('iiiii 画布初始化')

    if (this.board.id === 0) {
      //如果还没有指定画布
      this.board = this.whiteBoards[0]
    }

    this.filterAndDrawAll()

  }

  registerEmitters() {
    //绘制白板的设置选择
    emitter.on('hss_whiteBoard_set', (data) => {
      const barSet: WhiteBarSet = data.data as WhiteBarSet
      if (barSet.setType === 'set') {
        this.setIndex = barSet.setIndex!
      } else if (barSet.setType === 'color') {
        this.strokeStyle = barSet.color as string
        this.canvasContext.strokeStyle = barSet.color as string
      } else if (barSet.setType === 'del') {
        this.delWhiteBoard()
      }
    })
    emitter.on('hss_whiteBoard_clear_draw', (data) => {
      const type = (data.data as object)['type'] as number
      if (type === 0) {
        //将本白板的线条过滤出来，并将为未转换比例的线条进行比例转换，再重新绘制所有线条
        this.filterAndDrawAll()
      } else {
        this.canvasContext.clearRect(0, 0, this.canvasContext.width, this.canvasContext.height)
      }
    })
  }

  removeEmitters() {
    emitter.off('hss_whiteBoard_set')
    emitter.off('hss_whiteBoard_clear_draw')
  }

  filterAndDrawAll() {
    const rw = this.canvasContext.width / this.board.width!
    const rh = this.canvasContext.height / this.board.height!
    this.antInfos = this.allAntInfos.filter(ant => ant.docId === this.board.id).map(ant => {
      if (!ant.ratio) {
        ant.pt_x = ant.pt_x.map(x => x * rw)
        ant.pt_y = ant.pt_y.map(y => y * rh)
        ant.ratio = true
        console.log('iiii 转换比例线条', JSON.stringify(ant))
      }
      return ant
    })
    console.log('iiii 开始绘制所有线')
    this.canvasContext.clearRect(0, 0, this.canvasContext.width, this.canvasContext.height)
    this.drawAllLines()
  }

  //绘制时触发调用
  private handleTouch(event: TouchEvent) {
    if (this.setIndex === 0) {
      //绘制线条
      this.drawLine(event.touches[0].x, event.touches[0].y, event.type)
    } else if (this.setIndex === 1) {
      //使用橡皮擦
      this.removeLinesWithinRange(event.touches[0].x, event.touches[0].y)
    } else if (this.setIndex === 2) {
      //todo:绘制指针
      this.drawPointer(event.touches[0].x, event.touches[0].y, event.type)
    }
  }

  //绘制一小段线
  drawLine(x: number, y: number, touchType: TouchType) {
    if (touchType === TouchType.Down) {
      this.canvasContext.beginPath()
      this.canvasContext.moveTo(x, y)
    }
    if (touchType === TouchType.Move) {
      this.canvasContext.lineTo(x, y)
      this.canvasContext.stroke()
    }
    if (touchType === TouchType.Up) {
      this.canvasContext.closePath()
      //按照比例转换
      const rw = (this.board.width!) / this.canvasContext.width
      const rh = (this.board.height!) / this.canvasContext.height
      //比例转换后的数组
      const xs = this.line.map(line => Math.round(line.x * rw))
      const ys = this.line.map(line => Math.round(line.y * rh))
      const rgb = getRgb(this.strokeStyle)
      const antInfo: ANTINFO = {
        type: 165,
        docId: this.board.id,
        pageId: 1,
        id: 0,
        ptCount: xs.length,
        pt_x: xs,
        pt_y: ys,
        strText: "",
        rgb: rgb,
        fontSize: 10,
        lineType: 1,
        lineWidth: 1,
        ownerId: this.userData.uid || 1000,
        left: 0,
        right: Math.max(...xs),
        top: 0,
        bottom: Math.max(...ys),
        ratio: false
      };
      const id = sendAS_Data(antInfo)
      antInfo.id = id
      console.log('iiii 我绘制的线', JSON.stringify(antInfo))
      this.allAntInfos.push(antInfo)
      emitter.emit('hss_whiteBoard_clear_draw', { data: { type: 0 } }) //将线条刷入antInfo中
      this.line = []
      return
    }
    this.line.push({ x, y })
  }

  //绘制所有线
  drawAllLines() {
    const tempColor = this.canvasContext.strokeStyle
    const tempWidth = this.canvasContext.lineWidth
    this.antInfos.forEach((ant: ANTINFO) => {
      this.drawOneLine(ant)
    })
    this.canvasContext.strokeStyle = tempColor
    this.canvasContext.lineWidth = tempWidth
  }

  //绘制图形前置-比例转换
  drawOneLine(ant: ANTINFO) {
    //绘制一条线
    this.canvasContext.lineWidth = px2vp(ant.lineWidth)
    this.canvasContext.strokeStyle = getColor(ant.rgb)
    this.canvasContext.beginPath()

    this.draw(ant)

    this.canvasContext.stroke()
    this.canvasContext.closePath()
  }

  //绘制一个图形
  draw(ant: ANTINFO) {
    const type = ant.type
    const xs = ant.pt_x
    const ys = ant.pt_y
    if (type === 161) {
      //直线
      this.canvasContext.moveTo(xs[0], ys[0])
      this.canvasContext.lineTo(xs[1], ys[1])
    } else if (type === 162) {
      //矩形
      this.canvasContext.rect(xs[0], ys[0], xs[1] - xs[0], ys[1] - ys[0])
    } else if (type === 163) {
      //椭圆
      const ox = (xs[1] + xs[0]) / 2
      const oy = (ys[1] + ys[0]) / 2
      //半径
      const rx = xs[1] - ox
      const ry = ys[1] - oy
      this.canvasContext.ellipse(ox, oy, rx, ry, 0, 0, 2 * Math.PI)

    } else if (type === 165) {
      //自由绘制
      this.canvasContext.moveTo(xs[0], ys[0])
      for (let i = 1; i < xs.length; i++) {
        this.canvasContext.lineTo(xs[i], ys[i])
      }
    } else if (type === 167) {
      //指针
      const w = this.canvasContext.lineWidth
      const c = this.canvasContext.strokeStyle
      const f = this.canvasContext.fillStyle
      this.canvasContext.lineWidth = 2
      this.canvasContext.strokeStyle = Color.Pink

      this.canvasContext.moveTo(xs[0], ys[0])
      this.canvasContext.lineTo(xs[0] + 16, ys[0] + 16)
      this.canvasContext.stroke()

      const user = this.participants.find(item => item.uid === ant.ownerId)
      const measure = this.canvasContext.measureText(user?.nickname)

      //矩形
      this.canvasContext.fillStyle = Color.Pink
      this.canvasContext.fillRect(xs[0] + 18, ys[0] + 18, measure?.width + 4, 20)

      //文字
      this.canvasContext.fillStyle = Color.Black
      this.canvasContext.font = '16vp sans-serif'
      this.canvasContext.fillText(user?.nickname, xs[0] + 18 + 2, ys[0] + 18 + 17, 86)

      this.canvasContext.lineWidth = w
      this.canvasContext.strokeStyle = c
      this.canvasContext.fillStyle = f
    } else if (type === 168) {
      //文字
      let vpNum = 16
      const font = fonts.find(font => font.fontNum === ant.fontSize)
      if (font) {
        vpNum = font.fontVp
      }
      this.canvasContext.font = vpNum + 'vp sans-serif'
      this.canvasContext.fillStyle = getColor(ant.rgb)
      this.canvasContext.fillText(ant.strText, xs[0], ys[0] + this.canvasContext.measureText(ant.strText).height)
    }
  }

  //删除白板
  delWhiteBoard() {
    const delIndex = this.whiteBoards.findIndex(board => this.board.id == board.id)
    //如果找不到该索引则不往下执行了（防止空指针）
    if (delIndex === -1) {
      return
    }
    //展示的新白板的索引
    let currentBoardIndex: number = 0
    const boardId: number = this.board.id
    //如果删除的白板是数组的最后一个，则展示前一个
    if (delIndex === this.whiteBoards.length - 1) { //最后一个
      currentBoardIndex = delIndex - 1
    }
    //在数组中删除该白板
    this.whiteBoards.splice(delIndex, 1)
    //如果删除后没有白板了，则不给board赋新值
    if (this.whiteBoards.length !== 0) {
      this.board = this.whiteBoards[currentBoardIndex]
    }
    StopShareDoc(boardId, this.board.id)
  }

  // 触摸删除线条函数
  removeLinesWithinRange(x: number, y: number) {
    for (let i = this.antInfos.length - 1; i >= 0; i--) {
      const line = this.antInfos[i];
      for (let j = 0; j < line.ptCount; j++) {
        const pointX = line.pt_x[j];
        const pointY = line.pt_y[j];
        // 计算触摸点与线上点的距离
        const distance = Math.sqrt((x - pointX) * (x - pointX) + (y - pointY) * (y - pointY));
        if (distance <= 10) {
          // 若距离在 10 以内，删除该线
          console.log('iiii 触发删除线条', JSON.stringify(line))
          const index = this.allAntInfos.findIndex(ant => ant.id === line.id)
          if (index !== -1) {
            //删除这条线的条件，要么是主持人，要么是自己的线
            if (this.userData.role === 1 || this.userData.uid === line.ownerId) {
              removeAS_Data(this.allAntInfos[index])
              this.allAntInfos.splice(index, 1)
            }
          }
          this.filterAndDrawAll()
          break;
        }
      }
    }
  }

  pointLine: ANTINFO = { id: 0 } as ANTINFO

  //绘制指针
  drawPointer(x: number, y: number, touchType: TouchType) {
    if (touchType === TouchType.Down) {
      //绘制前线删除原来的线条
      const index=this.allAntInfos.findIndex(ant => ant.id === this.pointLine.id)
      if (index !== -1) {
        removeAS_Data(this.allAntInfos[index])
        this.allAntInfos.splice(index, 1)
      }
      // const ants = this.allAntInfos.filter(ant => (ant.ownerId === this.userData.uid && ant.type === 167))
      // if (ants.length !== 0) {
      //   ants.forEach(ant => {
      //     removeAS_Data(ant)
      //     const index = this.allAntInfos.findIndex(a => a.id === ant.id)
      //     this.allAntInfos.splice(index, 1)
      //   })
      // }
      const rw = (this.board.width!) / this.canvasContext.width
      const rh = (this.board.height!) / this.canvasContext.height
      //比例转换后的数组
      const rgb = getRgb(this.strokeStyle)
      const antInfo: ANTINFO = {
        type: 167,
        docId: this.board.id,
        pageId: 1,
        id: 0,
        ptCount: 1,
        pt_x: [x * rw],
        pt_y: [y * rh],
        strText: this.userData.nickname || 'admin',
        rgb: rgb,
        fontSize: 10,
        lineType: 1,
        lineWidth: 1,
        ownerId: this.userData.uid || 1000,
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        ratio: false
      };
      const id = sendAS_Data(antInfo)
      antInfo.id = id
      this.pointLine = antInfo
      console.log('iiii 我绘制的线', JSON.stringify(antInfo))
      this.allAntInfos.push(antInfo)
      this.filterAndDrawAll()
    }
  }
}

interface DrawLine {
  x: number
  y: number
}

function getColor(rgb: number) {
  if (rgb === 0) {
    return Color.Black
  } else if (rgb === 10329501) {
    return Color.Gray
  } else if (rgb === 65280) {
    return Color.Green
  } else if (rgb === 65535) {
    return Color.Yellow
  } else if (rgb === 255) {
    return Color.Red
  } else {
    return Color.Black
  }
}

function getRgb(color: string) {
  if (color === Color.Black.toString()) {
    return 0
  } else if (color === Color.Gray.toString()) {
    return 10329501
  } else if (color === Color.Green.toString()) {
    return 65280
  } else if (color === Color.Yellow.toString()) {
    return 65535
  } else if (color === Color.Red.toString()) {
    return 255
  } else {
    return 0
  }
}

interface fontStyle {
  title: string
  fontNum: number
  fontVp: number
}

const fonts: fontStyle[] = [
  { title: '初号', fontNum: 4294967000, fontVp: 26 },
  { title: '小初', fontNum: 4294967042, fontVp: 23 },
  { title: '一号', fontNum: 4294967113, fontVp: 20 },
  { title: '小一', fontNum: 4294967127, fontVp: 18 },
  { title: '二号', fontNum: 4294967141, fontVp: 15 },
  { title: '小二', fontNum: 4294967169, fontVp: 13 },
  { title: '三号', fontNum: 4294967184, fontVp: 12 },
  { title: '小三', fontNum: 4294967191, fontVp: 11 },
  { title: '四号', fontNum: 4294967198, fontVp: 10 },
  { title: '小四', fontNum: 4294967212, fontVp: 9 },
  { title: '五号', fontNum: 4294967219, fontVp: 8 },
  { title: '小五', fontNum: 4294967233, fontVp: 7 },
  { title: '六号', fontNum: 4294967240, fontVp: 6 },
  { title: '小六', fontNum: 4294967247, fontVp: 5 },
  { title: '七号', fontNum: 4294967256, fontVp: 4 },
  { title: '八号', fontNum: 4294967261, fontVp: 3 }
]

{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "C:\\Users\\<USER>\\.ohos\\config\\default_hsmeeting-HarmonyOS_vfHQpYAqtrmpGYhuIJAZPWwrHgwmD4lfHN_VNICYmHc=.cer",
          "storePassword": "0000001A0BE705A574CFCEDE8F7EFD68F326111DD34EF9A0C011612644E4119764897A1B31DA477CC9B2",
          "keyAlias": "debugKey",
          "keyPassword": "0000001A91B139145E47B2CA599C8DDEAD549D8FE2675BBC2D8EE01169324791D43D696FB05F7FE3605B",
          "profile": "C:\\Users\\<USER>\\.ohos\\config\\default_hsmeeting-HarmonyOS_vfHQpYAqtrmpGYhuIJAZPWwrHgwmD4lfHN_VNICYmHc=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\default_hsmeeting-HarmonyOS_vfHQpYAqtrmpGYhuIJAZPWwrHgwmD4lfHN_VNICYmHc=.p12"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.2(14)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./product/entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    // {
    //   "name": "library",
    //   "srcPath": "./features/library",
    //   "targets": [
    //     {
    //       "name": "default",
    //       "applyToProducts": [
    //         "default"
    //       ]
    //     }
    //   ]
    // },
    {
      "name": "RouterMoudel",
      "srcPath": "./commons/RouterMoudel",
    },
    {
      "name": "basic",
      "srcPath": "./commons/basic",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "home",
      "srcPath": "./features/home",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "settings",
      "srcPath": "./features/settings",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "schedulemeeting",
      "srcPath": "./features/schedulemeeting",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "createmeeting",
      "srcPath": "./features/createmeeting",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}
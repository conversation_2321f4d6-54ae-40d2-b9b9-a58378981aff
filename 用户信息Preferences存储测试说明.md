# 用户信息Preferences存储测试说明

## 修改内容

### 1. 创建UserPreferences管理类
- 在`commons/basic/src/main/ets/api/LoginApi.ets`中新增`UserPreferences`类
- 参考`ServerConfig`的实现，使用HarmonyOS的Preferences API进行持久化存储
- 支持用户信息、登录状态、缓存用户名的存储和管理
- 提供降级机制，如果Preferences初始化失败会使用AppStorage作为备选

### 2. 修改UserInfo类
- 将`saveUserInfo()`和`clear()`方法改为异步方法
- 集成`UserPreferences`实例进行数据持久化
- 在构造函数中自动加载已保存的用户信息
- 添加`ensureInitialized()`、`saveCachedUsername()`、`getCachedUsername()`方法

### 3. 更新相关调用
- 更新`LoginPage.ets`中匿名登录的`saveUserInfo()`调用为异步
- 更新`LoginApi.login()`方法中的`saveUserInfo()`调用为异步
- 更新`LoginApi.logout()`方法为异步
- 更新`LogoutPage.ets`中的`logout()`调用为异步

### 4. 更新用户名缓存机制
- 修改`JoinMeetingPage.ets`中的`getCachedUsername()`方法使用Preferences
- 更新`aboutToAppear()`方法为异步以支持Preferences加载

### 5. 初始化改进
- 在`EntryAbility.ets`中添加UserPreferences初始化
- 确保应用启动时Preferences已正确初始化

## 技术细节

### 存储机制
- **主要存储**：使用HarmonyOS的Preferences API进行持久化存储
- **备选方案**：如果Preferences初始化失败，降级使用AppStorage
- **同步机制**：Preferences和AppStorage保持同步，确保UI组件能正常访问数据
- **异步处理**：所有存储操作都是异步的，避免阻塞UI线程

### 存储内容
- `userInfo`：完整的用户信息JSON字符串
- `isLoggedIn`：登录状态布尔值
- `cachedUsername`：缓存的用户名字符串

### 向后兼容性
- UI组件继续使用@StorageLink绑定AppStorage
- UserPreferences会自动同步数据到AppStorage
- 现有的UI组件无需修改

## 测试步骤

### 1. 用户登录测试
1. 打开应用
2. 进入登录页面
3. 输入用户名和密码进行登录
4. 验证登录成功后用户信息显示正确
5. 完全退出应用（从后台清除）
6. 重新启动应用
7. 验证用户仍然处于登录状态，用户信息正确显示

### 2. 匿名登录测试
1. 打开应用
2. 进入登录页面
3. 点击匿名登录
4. 验证匿名登录成功
5. 完全退出应用（从后台清除）
6. 重新启动应用
7. 验证匿名用户状态保持

### 3. 用户注销测试
1. 在已登录状态下
2. 进入设置页面
3. 点击账户进入退出页面
4. 点击退出账户
5. 验证退出成功，显示未登录状态
6. 完全退出应用（从后台清除）
7. 重新启动应用
8. 验证仍然是未登录状态

### 4. 用户名缓存测试
1. 进入加入会议页面
2. 输入显示名
3. 退出应用并重新启动
4. 再次进入加入会议页面
5. 验证显示名是否被正确缓存和恢复

### 5. 设置页面显示测试
1. 测试不同用户状态下设置页面的账户项显示：
   - 未登录：显示"未登录"
   - 正式用户：显示用户名
   - 匿名用户：显示"未登录"

## 预期结果

修改后，用户信息应该能够在应用退出后保持，不会因为清理内存而丢失：

1. **正式登录用户**：登录状态和用户信息在应用重启后保持
2. **匿名用户**：匿名登录状态在应用重启后保持
3. **用户名缓存**：在加入会议页面输入的显示名会被缓存并在下次使用
4. **UI一致性**：所有UI组件显示正确的用户状态
5. **性能稳定**：Preferences初始化失败时能正常降级到AppStorage

## 注意事项

1. 所有涉及用户信息存储的操作现在都是异步的
2. 应用启动时会自动加载已保存的用户信息
3. 数据同时存储在Preferences（持久化）和AppStorage（UI绑定）中
4. 如果Preferences不可用，系统会自动降级到AppStorage模式

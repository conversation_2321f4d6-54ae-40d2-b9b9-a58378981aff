import player from 'libplayer.so'

@Component
export struct VideoComponent {
  // 可选的外部传入surfaceId
  @Prop surfaceId?: string
  // 组件宽度和高度，支持外部配置，使用Length类型
  @State private componentWidth: Length = '100%'
  @State private componentHeight: Length = '100%'
  private innerSurfaceId: string = ''

  aboutToAppear() {
    // 如果没有外部传入surfaceId，则生成一个唯一ID
    if (!this.surfaceId) {
      this.innerSurfaceId = `surface_${Date.now()}_${Math.random().toString(36).slice(2)}`
    } else {
      this.innerSurfaceId = this.surfaceId
    }
  }

  build() {
    Column() {
      XComponent({
        id: this.innerSurfaceId,
        type: XComponentType.SURFACE,
        libraryname: 'player'
      })
        // .width(this.componentWidth)
        // .height(this.componentHeight)
        .onLoad(() => {
          // Surface创建完成后的回调
          // console.info(`Surface created with id: ${this.innerSurfaceId}`)
        })
        // .onDestroy(() => {
        //   player.release(parseInt(this.innerSurfaceId))
        // })

    }
    .constraintSize({ maxWidth: '100%', maxHeight: '100%' })
    .aspectRatio(16 / 9)
    .alignRules({
      middle: { anchor: "__container__", align: HorizontalAlign.Center },
      center: { anchor: "__container__", align: VerticalAlign.Center }
    })
  }
}

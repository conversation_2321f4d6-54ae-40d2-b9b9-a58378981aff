#include <cstdint>
#include <iostream>
#include <thread>
#include "VideoData.h"
#include "capbilities/include/VideoEncoder.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "common/dfx/log/AVCodecSampleLog.h"
#include "screencapture/Capture.h"


#undef LOG_TAG

namespace {
using namespace std::chrono_literals;
constexpr int64_t MICROSECOND = 1000000;
} // namespace

Capture::~Capture() { StartRelease(); }


int32_t Capture::Init(SampleInfo &sampleInfo) {
    std::lock_guard<std::mutex> lock(mutex_);
   

    sampleInfo_ = sampleInfo;

    videoEncoder_ = std::make_unique<VideoEncoder>();

    int32_t ret = videoEncoder_->Create(sampleInfo_.videoCodecMime);
   

    encContext_ = new CodecUserData;
    ret = videoEncoder_->Config(sampleInfo_, encContext_);


    sampleInfo.window = sampleInfo_.window;

    releaseThread_ = nullptr;
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Capture::Start() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    int32_t ret = videoEncoder_->Start();

    isStarted_ = true;
    encOutputThread_ = std::make_unique<std::thread>(&Capture::EncOutputThread, this);
    if (encOutputThread_ == nullptr) {
        StartRelease();
        return AVCODEC_SAMPLE_ERR_ERROR;
    }

    return AVCODEC_SAMPLE_ERR_OK;
}

void Capture::EncOutputThread() {
    
    char *pFirstPack = nullptr;
    int nFirstPackLen = 0;
    while (true) {
        
        std::unique_lock<std::mutex> lock(encContext_->outputMutex);

        if (encContext_ == nullptr) {
            return;
        }

        bool condRet = encContext_->outputCond.wait_for(
            lock, 5s, [this]() { 
                return !isStarted_ || 
                    (encContext_ != nullptr && !encContext_->outputBufferInfoQueue.empty()); 
            });

        if (!isStarted_) {
            break;
        }

        if (encContext_->outputBufferInfoQueue.empty()) {
            continue;
        }
        
        CodecBufferInfo bufferInfo = encContext_->outputBufferInfoQueue.front();
        encContext_->outputBufferInfoQueue.pop();
        lock.unlock();

        if ((bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) ||
            (bufferInfo.attr.flags == AVCODEC_BUFFER_FLAGS_NONE)) {
            encContext_->outputFrameCount++;
            bufferInfo.attr.pts = encContext_->outputFrameCount * MICROSECOND / sampleInfo_.frameRate;
        } else {
            bufferInfo.attr.pts = 0;
        }

        if (videoEncoder_ == nullptr) {
            break;
        }

        uint8_t* encodedData = OH_AVBuffer_GetAddr((OH_AVBuffer *)bufferInfo.buffer) + bufferInfo.attr.offset;
 
        bool bKeyFrame = (bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) != 0;
        
        if(!pFirstPack) {
            bKeyFrame = true;
            nFirstPackLen = bufferInfo.attr.size;
            pFirstPack = new char[nFirstPackLen + 1];
            memcpy(pFirstPack, encodedData, bufferInfo.attr.size);
        } else {
            if(bKeyFrame) {
                    sendAsData(pFirstPack, nFirstPackLen, 
                          true, sampleInfo_.videoWidth, 
                          sampleInfo_.videoHeight, true, 24);              
            }
        }
        int nRet = sendAsData(encodedData, bufferInfo.attr.size, 
                      bKeyFrame, sampleInfo_.videoWidth, 
                      sampleInfo_.videoHeight, true, 24);        

        int32_t ret = videoEncoder_->FreeOutputBuffer(bufferInfo.bufferIndex);
        if (ret != 0) {
            break;
        }
    }
    if(pFirstPack) {
        delete []pFirstPack;
    }
    StartRelease();
}

void Capture::StartRelease() {
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (!isStarted_) {
            return; // 已经停止了，直接返回
        }
        isStarted_ = false;
        
        // 通知所有等待的线程
        if (encContext_ != nullptr) {
            encContext_->outputCond.notify_all();
        }
    } // 释放锁，这样其他线程可以继续运行

    // 等待编码输出线程结束
    if (encOutputThread_ && encOutputThread_->joinable()) {
        try {
            encOutputThread_->join();
        } catch (const std::system_error& e) {
            // 忽略线程已经结束的错误
        }
        encOutputThread_.reset();
    }

    {
        std::lock_guard<std::mutex> lock(mutex_);
        // 释放编码器资源
        if (videoEncoder_ != nullptr) {
            videoEncoder_->Stop();
            videoEncoder_->Release();
            videoEncoder_.reset();
        }
        
        // 清理上下文
        if (encContext_ != nullptr) {
            delete encContext_;
            encContext_ = nullptr;
        }
    }
}

void Capture::Release() {
    std::lock_guard<std::mutex> lock(mutex_);
    isStarted_ = false;
    
    // Notify any waiting threads
    if (encContext_ != nullptr) {
        encContext_->outputCond.notify_all();
    }
    
    if (encOutputThread_ && encOutputThread_->joinable()) {
        encOutputThread_->join();
        encOutputThread_.reset();
    }

    if (videoEncoder_ != nullptr) {
        videoEncoder_->Stop();
        if (sampleInfo_.window != nullptr) {
            OH_NativeWindow_DestroyNativeWindow(sampleInfo_.window);
            sampleInfo_.window = nullptr;
        }
        videoEncoder_->Release();
        videoEncoder_.reset();
    }
    
    if (encContext_ != nullptr) {
        delete encContext_;
        encContext_ = nullptr;
    }
    
    doneCond_.notify_all();
}

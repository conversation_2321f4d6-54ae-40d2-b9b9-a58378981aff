import { CurrentUserModel } from '../common/model'
import { ParticipantItem, ParticipantSetDialog } from '../components'
import { Participant, ParticipantClass } from '../models'

// 参会者列表组件
@Component
export struct ParticipantsView {
  /**
   * 点击自己-弹出提示框，有语音和视频选项
   */
  //本人用户信息
  @Prop userInfo: Participant
  @Link participants: ParticipantClass[]
  @Prop currentParticipant: ParticipantClass
  @Prop isPublicChatHaveMessage: boolean
  onChatClick: (index: number, participant: ParticipantClass | undefined) => void = () => {
  }
  dialog: CustomDialogController = new CustomDialogController({
    builder: ParticipantSetDialog({
      participant: this.currentParticipant,
      userInfo: this.userInfo,
      onclick: (index: number) => {
        //index->0开启/静音，1打开/关闭视频，2设为/取消主持人，3私聊，4移出会议
        this.onChatClick(index, this.currentParticipant)
      }
    })
  })

  build() {
    List() {
      ListItem() {
        this.allChatBuilder()
      }

      Repeat<ParticipantClass>(this.participants)
        .each((item: RepeatItem<ParticipantClass>) => {
          ListItem() {
            ParticipantItem({
              userInfo: this.userInfo,
              participant: item.item,
              onChatClick: () => {
                //我是主持人或者点击自己的时候才打开弹窗
                if (this.userInfo.role === 1 || this.userInfo.uid === item.item.uid) {
                  this.currentParticipant = item.item
                  this.dialog.open()
                  return
                }
                //否则直接进入私聊
                this.onChatClick(3, item.item)
              },
              onKick: () => {
                //直接踢出会议
                this.onChatClick(4, item.item)
              }
            })
          }
        })
        .key((item: ParticipantClass) => item.uid!.toString())
        .virtualScroll({ totalCount: this.participants.length })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.White)
  }

  @Builder
  allChatBuilder() {
    Column() {
      Row({ space: 10 }) {
        Row() {
          Stack({ alignContent: Alignment.TopEnd }) {
            Image($r('app.media.ic_att_device_all')).objectFit(ImageFit.Contain)
            if (this.isPublicChatHaveMessage) {
              Text()
                .width(8)
                .aspectRatio(1)
                .borderRadius(4)
                .backgroundColor(Color.Red)
            }
          }
        }
        .width(40)
        .height(40)
        .padding(9)
        .justifyContent(FlexAlign.Center)

        Text('公聊')
      }
      .width('100%')
      .margin({ bottom: 15 })

      Divider()
    }
    .width('100%')
    .padding(15)
    .onClick(() => {
      this.onChatClick(3, undefined)
    })
  }
}

export function userRoleCallback(userlist: CurrentUserModel[]) {
  console.log('userlist 触发函数-userRoleCallback，人数为:' + userlist.length, JSON.stringify(userlist))
  let userList = userlist.map((user) => {
    const u = user
    console.log('userlist 当前name', user.name)
    if (!user.name) {
      console.log('userlist 无name数据-', JSON.stringify(user))
      u.name = "noName"
    }
    return u
  })
  return userList.map((user: CurrentUserModel) => {
    const u: Participant = {
      uid: user.uid,
      nickname: user.name,
      role: 8,
      audioOpen: false,
      videoOpen: false,
      handUp: false
    }
    //console.log('userlist 当前push数据-', JSON.stringify(user))
    return u
  })
}

// ForEach(this.participants, (item: ParticipantClass, index: number) => {
//   ListItem() {
//     ParticipantItem({
//       userInfo: this.userInfo,
//       participant: item,
//       onChatClick: () => {
//         //我是主持人或者点击自己的时候才打开弹窗
//         if (this.userInfo.role === 1 || this.userInfo.uid === item.uid) {
//           this.currentParticipant = item
//           this.dialog.open()
//           return
//         }
//         //否则直接进入私聊
//         this.onChatClick(3, item)
//       },
//       onKick: () => {
//         //直接踢出会议
//         this.onChatClick(4, item)
//       }
//     })
//   }
// }, (item: Participant) => item.uid!.toString())
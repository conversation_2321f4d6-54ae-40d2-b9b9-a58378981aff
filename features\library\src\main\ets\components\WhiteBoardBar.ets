import { emitter } from "@kit.BasicServicesKit"

@Component
export struct WhiteBoardBar {
  @Prop isShow: boolean
  @Prop rotation: number
  @State colorIndex: number = 0
  @State setIndex: number = -1
  colors: ResourceColor[] = [Color.Black, Color.Gray, Color.Green, Color.Yellow, Color.Red]

  build() {
    if (this.isShow) {
      Row({ space: 15 }) {
        ForEach(this.colors, (color: ResourceColor, index) => {
          this.colorItemBuilder(index, color)
        })

        this.setItemBuilder(0, $r('app.media.icon_ds_pen_normal'), $r('app.media.icon_ds_pen_on'))
        this.setItemBuilder(1, $r('app.media.icon_ds_eraser_normal'), $r('app.media.icon_ds_eraser_on'))
        this.setItemBuilder(2, $r('app.media.icon_ds_pointer_normal'), $r('app.media.icon_ds_pointer_on'))

        Blank()

        Text('删除')
          .fontColor(Color.Red)
          .onClick(() => {
            console.log('删除')
            emitter.emit('hss_whiteBoard_set', {
              data: {
                setType: 'del'
              } as WhiteBarSet
            })
          })
      }
      .width('100%')
      .height(64)
      .padding({ left: 10, right: 10 })
      .backgroundColor('#000000')
      .alignRules({
        left: { anchor: "__container__", align: HorizontalAlign.Start },
        bottom: { anchor: "__container__", align: VerticalAlign.Bottom }
      })
      .onDisAppear(() => {
        this.setIndex = -1
      })
    }
  }

  @Builder
  colorItemBuilder(index: number, color: ResourceColor) {
    Row() {
      Text()
        .width('100%')
        .height('100%')
        .backgroundColor(color)
        .border({ width: 1, color: Color.White, radius: 1 })
    }
    .width(26)
    .aspectRatio(1)
    .padding(3)
    .border({ width: 1, color: this.colorIndex === index ? "#00BFFF" : Color.Transparent, radius: 1 })
    .onClick(() => {
      this.colorIndex = index
      emitter.emit('hss_whiteBoard_set', {
        data: {
          setType: 'color',
          color: color
        } as WhiteBarSet
      })
    })
  }

  @Builder
  setItemBuilder(index: number, icon: ResourceStr, selectedIcon: ResourceStr) {
    Image(this.setIndex === index ? selectedIcon : icon)
      .height(20)
      .onClick(() => {
        if (this.setIndex === index) {
          this.setIndex = -1
        } else {
          this.setIndex = index
        }
        emitter.emit('hss_whiteBoard_set', {
          data: {
            setType: 'set',
            setIndex: this.setIndex
          } as WhiteBarSet
        })
      })
  }
}

export interface WhiteBarSet {
  setType: 'color' | 'set' | 'del'
  color?: ResourceColor
  setIndex?: number
}
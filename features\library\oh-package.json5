{"name": "hsmeeting_hmos_sdk", "version": "1.0.0", "description": "Please describe the basic information.", "main": "Index.ets", "bundleName": "com.hs.hsmeeting", "author": "", "license": "Apache-2.0", "dependencies": {"libentry.so": "file:./src/main/cpp/types/libentry", "libencoder.so": "file:./src/main/cpp/types/encoder", "libplayer.so": "file:./src/main/cpp/types/decoder", "libdesktopshare.so": "file:./src/main/cpp/types/desktopshare", "libcapture.so": "file:./src/main/cpp/types/capture", "basic": "file:../../commons/basic"}}
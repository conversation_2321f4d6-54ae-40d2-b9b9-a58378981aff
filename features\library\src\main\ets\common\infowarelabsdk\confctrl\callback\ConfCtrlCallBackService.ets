import testNapi from 'libhstinterface.so'

//设置用户角色
function SetUserRole(){

}
//设置用户名称
function SetUserName(){

}
//检查是否支持SVC(可伸缩视频编码)
function IsSupportSvc(){

}
//检查是否支持云录制
function IsSupportCloudRecord(){

}
//开始云录制
function BeginCloudRecord(){

}
//停止云录制
function StopCloudRecord(){

}
//设置会议盒子
function SetMeetingBox(){

}
//设置混合视频
function SetMixVideo(){

}
//设置会议布局
function SetLayout(){

}
//更新直播状态
function UpdateLiveState(){

}
//获取直播状态
function GetLiveState(){

}
//更新应用共享状态
function UpdateASState(){

}
//设置字幕
function SetSubtitles(){

}
//初始化角色
function InitRole(){

}
//设置会议控模式
function SetConfControlMode(){

}
//邀请H323用户
function InviteH323User(){

}
//获取用户视图状态
function GetUserViewState(){

}
//发送用户透明数据
function Transparent_SendUserData(){

}


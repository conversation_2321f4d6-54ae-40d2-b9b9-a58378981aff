{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"basic@../../commons/basic": "basic@../../commons/basic", "createmeeting@../../features/createmeeting": "createmeeting@../../features/createmeeting", "home@../../features/home": "home@../../features/home", "routermoudel@../../commons/RouterMoudel": "routermoudel@../../commons/RouterMoudel", "schedulemeeting@../../features/schedulemeeting": "schedulemeeting@../../features/schedulemeeting", "settings@../../features/settings": "settings@../../features/settings"}, "packages": {"basic@../../commons/basic": {"name": "basic", "version": "1.0.0", "resolved": "../../commons/basic", "registryType": "local", "packageType": "InterfaceHar"}, "createmeeting@../../features/createmeeting": {"name": "createmeeting", "version": "1.0.0", "resolved": "../../features/createmeeting", "registryType": "local", "dependencies": {"basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}, "home@../../features/home": {"name": "home", "version": "1.0.0", "resolved": "../../features/home", "registryType": "local", "dependencies": {"basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}, "routermoudel@../../commons/RouterMoudel": {"name": "routermoudel", "version": "1.0.0", "resolved": "../../commons/RouterMoudel", "registryType": "local"}, "schedulemeeting@../../features/schedulemeeting": {"name": "schedulemeeting", "version": "1.0.0", "resolved": "../../features/schedulemeeting", "registryType": "local", "dependencies": {"basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}, "settings@../../features/settings": {"name": "settings", "version": "1.0.0", "resolved": "../../features/settings", "registryType": "local", "dependencies": {"basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}}}
import http from '@ohos.net.http';
import { serverConfig } from 'basic';

// 获取服务器地址的函数
function getServerUrl(): string {
  try {
    // 使用ServerConfig获取服务器地址，确保数据一致性
    return serverConfig.getServerUrl();
  } catch (error) {
    console.error('获取服务器地址失败:', error);
    return ''; // 移除硬编码的qa27地址
  }
}

export interface AttendeeData {
  name: string;
  email: string;
  phone: string;
  systemUser: number;
}

export interface MeetingData {
  subject: string;
  startTime: string;
  endTime: string;
  attendeeAmount: number;
  hostName: string;
  creator: string;
  openType: boolean;
  passwd: string;
  conferencePattern: number;
  agenda?: string;
  attendees?: AttendeeData[];
}

export interface HttpRequestOptions {
  method: http.RequestMethod;
  header: Record<string, string>;
  extraData: string;
  readTimeout: number;
  connectTimeout: number;
}

export interface TimeUpdateData {
  startTime: string;
  endTime: string;
}

export class MeetingService {
  private static instance: MeetingService;

  private constructor() {}

  // 获取动态的baseUrl
  private getBaseUrl(): string {
    return `${getServerUrl()}/integration/xml`;
  }

  public static getInstance(): MeetingService {
    if (!MeetingService.instance) {
      MeetingService.instance = new MeetingService();
    }
    return MeetingService.instance;
  }

  // 创建参会人员 XML
  private createAttendeesXML(attendees: AttendeeData[]): string {
    if (!attendees || attendees.length === 0) {
      return '';
    }
    
    const attendeeElements: string = attendees.map((attendee: AttendeeData) => 
      `    <attendee>
      <name>${attendee.name}</name>
      <email>${attendee.email}</email>
      <phone>${attendee.phone}</phone>
      <systemUser>${attendee.systemUser}</systemUser>
    </attendee>`
    ).join('\n');
    
    return `    <attendees>\n${attendeeElements}\n    </attendees>`;
  }

  // 创建固定会议的 XML 请求体
  private createMeetingXML(meetingData: MeetingData): string {
    const xmlData: string = `<?xml version="1.0" encoding="UTF-8"?>
<Message>
  <header>
    <action>createFixedMeeting</action>
    <service>meeting</service>
    <type>xml</type>
    <siteName>box</siteName>
    <userName>admin</userName>
    <password>abc-123</password>
    <version>50</version>
  </header>
  <body>
    <subject>${meetingData.subject}</subject>
    <startTime>${meetingData.startTime}</startTime>
    <endTime>${meetingData.endTime}</endTime>
    <timeZoneId>45</timeZoneId>
    <attendeeAmount>${meetingData.attendeeAmount}</attendeeAmount>
    <hostName>${meetingData.hostName}</hostName>
    <creator>${meetingData.creator}</creator>
    <openType>${meetingData.openType}</openType>
    <fromType>1</fromType>
    <passwd>${meetingData.passwd}</passwd>
    <conferencePattern>${meetingData.conferencePattern}</conferencePattern>
    <conferenceType>2</conferenceType>
    <agenda>${meetingData.agenda || '会议议程'}</agenda>
    <mailTemplateLocal>zh_CN</mailTemplateLocal>
    <beforehandTime>15</beforehandTime>
    <webBaseUrl>${getServerUrl()}</webBaseUrl>
    <videoType>4</videoType>
    <openVideo>1</openVideo>
    <openAudio>1</openAudio>
    <scrInt>600</scrInt>
    <signIn>1</signIn>
    <businessType>1</businessType>
    ${meetingData.attendees ? this.createAttendeesXML(meetingData.attendees) : ''}
  </body>
</Message>`;
    return xmlData;
  }

  // 格式化时间为 ISO 格式
  private formatTimeForAPI(dateStr: string): string {
    // 将 "2024-01-15 09:00" 格式转换为 "2024-01-15T09:00:00"
    const date: Date = new Date(dateStr);
    return date.toISOString().slice(0, 19);
  }

  // 手动复制对象属性，避免使用 Object.assign
  private copyMeetingData(original: MeetingData, updates: TimeUpdateData): MeetingData {
    const result: MeetingData = {
      subject: original.subject,
      startTime: updates.startTime,
      endTime: updates.endTime,
      attendeeAmount: original.attendeeAmount,
      hostName: original.hostName,
      creator: original.creator,
      openType: original.openType,
      passwd: original.passwd,
      conferencePattern: original.conferencePattern,
      agenda: original.agenda,
      attendees: original.attendees
    };
    return result;
  }

  // 预约会议
  public async scheduleMeeting(meetingData: MeetingData): Promise<string> {
    const httpRequest = http.createHttp();
    
    try {
      // 格式化时间 - 使用自定义方法避免标准库限制
      const timeUpdates: TimeUpdateData = {
        startTime: this.formatTimeForAPI(meetingData.startTime),
        endTime: this.formatTimeForAPI(meetingData.endTime)
      };
      
      const formattedMeetingData: MeetingData = this.copyMeetingData(meetingData, timeUpdates);

      const xmlData: string = this.createMeetingXML(formattedMeetingData);
      console.log('预约会议xml:', xmlData);

      const requestOptions: HttpRequestOptions = {
        method: http.RequestMethod.POST,
        header: {
          'Content-Type': 'text/xml'
        },
        extraData: xmlData,
        readTimeout: 30000,
        connectTimeout: 10000
      };
      
      const response = await httpRequest.request(this.getBaseUrl(), requestOptions);

      if (response.responseCode === 200) {
        return response.result as string;
      } else {
        throw new Error(`请求失败: ${response.responseCode}`);
      }
    } catch (err) {
      if (err instanceof Error) {
        console.error('预约会议失败:', err);
        throw new Error(`预约会议发生错误: ${err.message}`);
      } else {
        console.error('预约会议失败:', err);
        throw new Error('预约会议发生未知错误');
      }
    } finally {
      httpRequest.destroy();
    }
  }
}
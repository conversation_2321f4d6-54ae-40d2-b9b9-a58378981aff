import testNapi from 'libhstinterface.so'

export function GetSelfID(): number {
  console.log('selfID', testNapi.conference_GetSelfID())
  return testNapi.conference_GetSelfID()
}

/**
 * type false 取消举手
 * type true 举手
 * */
export function HandUp(id: number, type: boolean): number {
  return testNapi.conference_HandUp(id, type)
}

//conference_SetUserName
export function SetUserName(uid: number, name: string): number {
  return testNapi.conference_SetUserName(uid, name)
}
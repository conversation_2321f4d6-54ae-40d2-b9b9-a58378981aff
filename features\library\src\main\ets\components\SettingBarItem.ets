@Component
export struct SettingBarItem {
  @Prop showChatPage: boolean = false
  @Prop icon: ResourceStr
  @Prop text: string
  @Prop fontColor: ResourceColor
  onclick: () => void = () => {
  }

  build() {
    Column() {
      Image(this.icon)
        .width(24)
        .height(24)
      Text(this.text)
        .fontSize(12)
        .fontColor(this.fontColor)
        .margin({ top: 4 })
    }
    .height('100%')
    .padding({ left: 15, right: 15 })
    .justifyContent(FlexAlign.Center)
    .onClick(() => {
      this.onclick()
    })
  }
}
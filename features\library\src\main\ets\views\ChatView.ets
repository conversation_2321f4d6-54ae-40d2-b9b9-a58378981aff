import { ChatMessageItem, NavBar } from '../components'
import { ChatMessage, Participant } from '../models'
import { SendChatMessage } from '../common/model'
import { chat_Send_ChatMsg } from '../common/infowarelabsdk/chat'
import { screenManager } from '../utils'
import { emitter } from '@kit.BasicServicesKit'
import { promptAction } from '@kit.ArkUI'

// 聊天页面组件
@Component
export struct ChatView {
  @Prop UserInfo: Participant
  @Link messages: ChatMessage[]
  @State showMessages: ChatMessage[] = []
  @State inputText: string = ''
  //当前的聊天对象（公聊时为undefined,私聊时为被点击的参会者信息）
  @Prop currentChatTarget: Participant
  scroller: Scroller = new Scroller()
  //点击返回退出当前聊天页面
  onBack: () => void = () => {

  }

  //发送消息
  private sendMessage(): void {
    if (!this.inputText.trim()) {
      promptAction.showToast({ message: '不能发送空消息' })
      return
    }
    let targetUid = 0
    let isPublic = true
    if (this.currentChatTarget) {
      targetUid = this.currentChatTarget.uid!
      isPublic = false
    }
    let data: SendChatMessage = {
      targetUid,
      content: this.inputText,
      username: this.UserInfo.nickname!,
      isPublic
    }
    let t = chat_Send_ChatMsg(data)
    const newMessage: ChatMessage = {
      id: this.messages.length + 1,
      uid: this.UserInfo.uid!,
      username: this.UserInfo.nickname!,
      message: this.inputText,
      date: new Date().toLocaleTimeString(),
      isPublic: !this.currentChatTarget,
      isReaded: true
    }
    //将消息加入到本地的消息列表中
    this.showMessages.push(newMessage)
    this.messages.push(newMessage)
    //清空输入框
    this.inputText = ''
  }

  aboutToAppear(): void {
    // 设置键盘压缩
    screenManager.setKeyBoardMode(getContext(this), true)
    //加载聊天记录
    this.loadChattingRecords()
    //消息接收监听
    emitter.on('hss_chat_message_receive', (data) => {
      const message = data.data as ChatMessage;
      // 如果当前有聊天目标，且消息发送者是当前聊天目标
      if (this.currentChatTarget && message.uid === this.currentChatTarget.uid) {
        this.handleMessage(message);
        return;
      }
      // 如果没有当前聊天目标，且消息是公开消息
      if (!this.currentChatTarget && message.isPublic) {
        this.handleMessage(message);
      }
    })
  }

  //添加消息滚动到底部
  handleMessage = (message: ChatMessage) => {
    const isEnd = this.scroller.isAtEnd();
    this.showMessages.push(message);
    if (isEnd) {
      this.scroller.scrollEdge(Edge.Bottom);
    }
  };

  aboutToDisappear(): void {
    // 设置关闭键盘压缩
    screenManager.setKeyBoardMode(getContext(this), false)
    emitter.off('hss_chat_message_receive')
  }

  //加载聊天记录
  loadChattingRecords() {
    if (this.currentChatTarget) {
      //私聊
      this.showMessages = this.messages.filter((msg: ChatMessage) => {
        return !msg.isPublic && (msg.uid === this.UserInfo.uid || msg.uid === this.currentChatTarget.uid);
      });
    } else {
      //公聊
      this.showMessages = this.messages.filter(msg => msg.isPublic)
    }
  }

  build() {
    Column() {
      // 聊天页面顶部导航
      NavBar({
        title: this.currentChatTarget ? `与 ${this.currentChatTarget.nickname} 的私聊` : '正与所有人聊天',
        leftText: '< 返回',
        leftClick: () => this.onBack(),
        // rightText: '打印',
        // rightClick: () => {
        //   console.log('sss ', JSON.stringify(this.scroller.currentOffset()))
        // }
      })
      // 消息列表
      List({ scroller: this.scroller }) {
        ForEach(this.showMessages, (msg: ChatMessage) => {
          ListItem() {
            ChatMessageItem({
              message: msg as ChatMessage,
              isSelf: msg.uid as number === this.UserInfo.uid as number
            })
          }
        }, (msg: ChatMessage) => msg.id.toString())
      }
      .backgroundColor('#F5F5F5')
      .layoutWeight(1)
      .onAppear(() => {
        this.scroller.scrollToIndex(this.showMessages.length - 1)
      })

      // 底部输入区域
      Row({ space: 10 }) {
        TextInput({
          placeholder: '请输入消息...',
          text: $$this.inputText
        })
          .layoutWeight(1)
          .height(35)
          .backgroundColor('#FFFFFF')
          .border({ width: 1, color: '#ffcfcfcf', radius: 3 })
        Button('发送')
          .width(60)
          .height(35)
          .backgroundColor('#007DFF')
          .borderRadius(10)
          .onClick(() => {
            this.sendMessage()
          })
      }
      .width('100%')
      .padding(12)
      .backgroundColor('#F5F5F5')
    }
    .width('100%')
    .height('100%')
    .zIndex(1)
  }
}
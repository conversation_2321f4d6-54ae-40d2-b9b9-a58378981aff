import { ShareSelectDialog } from '../components'
import { emitter } from '@kit.BasicServicesKit'
import { IWhiteBoard, WhiteBoard } from '../components/WhiteBoard'
import { DesktopShareComponent } from '../components/DesktopShareComponent'
import { StartDesktopShare } from '../common/infowarelabsdk/shareAs'
import {
  addAllShareAsCallback,
  addCloseShareAsCallback,
  addNewAntCallback,
  addRemoveAllAntCallback,
  addRemoveAntCallback,
  addShareOnASCallback,
  removeAllShareAsCallback,
  removeCloseShareAsCallback,
  removeNewAntCallback,
  removeRemoveAllAntCallback,
  removeRemoveAntCallback,
  removeShareOnASCallback,
  StartShareDoc
} from '../common/infowarelabsdk/shareDs'
import { ANTINFO, Participant, ParticipantClass } from '../models'
import { promptAction } from '@kit.ArkUI'

// 文档共享组件
@Component
export struct DocSharingView {
  @Prop @Watch('shareChange') isOpenShare: boolean // 是否打开共享
  @Prop rotation: number // 当前设备旋转角度
  @Prop isOpenSetting: boolean // 是否打开颜色设置
  @Prop userData: Participant
  @Prop participants: ParticipantClass[]
  @State scrollable: boolean = true //Tabs是否可滑动
  @State @Watch('currentTabIndexChange') currentTabIndex: number = 1
  @State whiteBoards: IWhiteBoard[] = []
  @State allAntInfos: ANTINFO[] = [] //所有线条
  //当前白板
  @State board: IWhiteBoard = {
    id: 0,
    title: '',
    width: 4096,
    height: 2160
  }
  openSettingBarClick = (isOpen: boolean) => {
  }

  aboutToAppear(): void {
    // this.shareDialog.open()
    this.registerEmitters()
    this.registerCallback()
  }

  aboutToDisappear(): void {
    this.removeEmitters()
    this.removeCallback()
  }

  //返回全部
  allShareAsCallback = (nDocId: number, strDocTitle: string) => {
    console.log('iiii 拿到全部的白板', nDocId, strDocTitle)
    this.whiteBoards.push({
      id: nDocId,
      title: strDocTitle,
    })
    if (!this.isOpenShare) {
      this.currentTabIndex = 1
      this.scrollable = false
    }
  }
  //关闭一个白板
  closeShareAsCallback = (nDocID: number) => {
    console.log('iiii 关闭白板', nDocID)
    const index = this.whiteBoards.findIndex(board => board.id === nDocID)
    if (index === -1) {
      return
    }
    //todo:delete
    if (this.whiteBoards.length === 1) {
      //退出白板
      this.whiteBoards.splice(index, 1)
      return
    }
    if (index === this.whiteBoards.length - 1) {
      this.board = this.whiteBoards[index-1] //如果是最后一个的话就要往前一位
    }
    this.whiteBoards.splice(index, 1)
  }
  //当前的白板(主持人控制白板生效，自己调用switchDoc生效)
  shareOnASCallback =
    (nDocId: number, nPageId: number, data: ArrayBuffer, nPageWidth: number, nPageHeight: number, bNDF: boolean) => {
      console.log('iiii 当前的白板的数据', nDocId, nPageId, JSON.stringify(data), nPageWidth, nPageHeight, bNDF)
      // 当前白板
      const board = this.whiteBoards.find(board => board.id === nDocId)
      if (board) {
        this.board = board
      }
      this.board.width = nPageWidth
      this.board.height = nPageHeight
      emitter.emit('hss_whiteBoard_clear_draw', { data: { type: 1 } })
      // this.canvasContext.clearRect(0, 0, this.canvasContext.width, this.canvasContext.height) //防止下一个画布是空的
    }
  timerId: number = -1
  //返回的线条
  newAntCallback = (ant: ANTINFO) => {
    console.log('iiii 拿到的线条', JSON.stringify(ant))
    if (this.allAntInfos.findIndex(a => a.id === ant.id) === -1) {
      ant.ratio = false
      this.allAntInfos.push(ant)
    }
    clearTimeout(this.timerId)
    this.timerId = setTimeout(() => {
      //线条添加完毕-在所有线条中把当前白板的线条搜集到展示数组中
      emitter.emit('hss_whiteBoard_clear_draw', { data: { type: 0 } })
    }, 300)
  }
  //删除线条回调
  removeAntCallback = (nDocId: number, nPageId: number, nAntId: number) => {
    console.log('iiii 移出的线条111$$$  ', nDocId, nPageId, nAntId)
    const index = this.allAntInfos.findIndex(item => item.id === nAntId)
    if (index !== -1) {
      this.allAntInfos.splice(index, 1)
    }
    clearTimeout(this.timerId)
    this.timerId = setTimeout(() => {
      //线条添加完毕-在所有线条中把当前白板的线条搜集到展示数组中
      emitter.emit('hss_whiteBoard_clear_draw', { data: { type: 0 } })
    }, 300)
  }
  //删除全部线条回调
  removeAllAntCallback = (nDocId: number, nPageId: number) => {
    this.allAntInfos = this.allAntInfos.filter(item => item.docId !== nDocId)
    emitter.emit('hss_whiteBoard_clear_draw', { data: { type: 0 } })
  }

  registerEmitters() {
    emitter.on('hss_share_dialog_open', (data) => {
      const index = (data.data as object)['index'] as number
      if (index === 1 || (this.isOpenShare === false && this.whiteBoards.length === 0)) {
        this.shareDialog.open()
      }
    })
  }

  removeEmitters() {
    emitter.off('hss_share_dialog_open')
  }

  //共享开启/关闭
  shareChange() {
    if (this.isOpenShare) {
      this.currentTabIndex = 0
      if (this.whiteBoards.length === 0) {
        this.scrollable = false
      } else {
        this.scrollable = true
      }
    } else {
      if (this.whiteBoards.length > 0) {
        this.currentTabIndex = 1
        this.scrollable = false
      } else {
        this.currentTabIndex = 0
      }
    }
  }

  currentTabIndexChange() {
    //index变为1且没有打开共享
    if (this.currentTabIndex === 1 && this.isOpenShare === false) {
      this.scrollable = false
    }
  }

  build() {
    RelativeContainer() {

      //索引0-共享页面，共享他人的屏幕
      Tabs({ index: $$this.currentTabIndex }) {
        TabContent() {
          Stack() {
            DesktopShareComponent()
          }
          .width('100%')
          .height('100%')
          // .backgroundColor(Color.Orange)
          .backgroundColor(Color.Black)
        }

        if (this.whiteBoards.length > 0) {
          TabContent() {
            WhiteBoard({
              board: this.board,
              whiteBoards: this.whiteBoards,
              allAntInfos: this.allAntInfos,
              userData: this.userData,
              participants: this.participants,
              isOpenSetting: this.isOpenSetting,
              openSettingBarClick: (isOpen: boolean) => {
                this.openSettingBarClick(isOpen)
              }
            })
          }
        }

      }
      .width('100%')
      .height('100%')
      .barHeight(0)
      .scrollable(this.scrollable)
      .zIndex(2)

      if (!this.isOpenShare && this.whiteBoards.length === 0) {
        Stack({ alignContent: Alignment.Center }) {
          Text('暂无共享文档')
            .fontColor(Color.Black)
            .alignRules({
              center: { anchor: "__container__", align: VerticalAlign.Center },
              middle: { anchor: "__container__", align: HorizontalAlign.Center }
            })
        }
        .width('100%')
        .height('100%')
        .backgroundColor(Color.White)
        .zIndex(6)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#ffececec')
  }

  registerCallback() {
    addCloseShareAsCallback(this.closeShareAsCallback)
    addShareOnASCallback(this.shareOnASCallback)
    addNewAntCallback(this.newAntCallback)
    addAllShareAsCallback(this.allShareAsCallback)
    addRemoveAntCallback(this.removeAntCallback)
    addRemoveAllAntCallback(this.removeAllAntCallback)
  }

  removeCallback() {
    removeCloseShareAsCallback()
    removeShareOnASCallback()
    removeNewAntCallback()
    removeAllShareAsCallback()
    removeRemoveAntCallback()
    removeRemoveAllAntCallback()
  }

  createWhiteBoard() {
    let title = ''
    if (this.whiteBoards.length === 0) {
      title += 1
    } else {
      title += incrementTrailingNumber(this.whiteBoards[this.whiteBoards.length-1].title)
    }
    //todo:通知服务器白板创建
    const docId = StartShareDoc(title)
    this.whiteBoards.unshift({
      id: docId,
      title: title,
      width: 4096,
      height: 2160
    })
    //切换到当前白板
    // switchDoc(docId)
    this.board = this.whiteBoards[this.whiteBoards.length-1]
    this.currentTabIndex = 1
  }

  shareDialog: CustomDialogController = new CustomDialogController({
    builder: ShareSelectDialog({
      creamClick: () => {
        promptAction.showToast({ message: '暂未开放' })
        // this.getCream()
      },
      photoClick: () => {
        promptAction.showToast({ message: '暂未开放' })
        // this.getPhoto()
      },
      whiteBoardClick: () => {
        this.createWhiteBoard()
      },
      desktopClick: async () => {
        StartDesktopShare(18)
      }
    }),
    alignment: DialogAlignment.Bottom,
    cornerRadius: 5,
    offset: { dx: 0, dy: -20 },
    backgroundColor: Color.Transparent
  })
}

function incrementTrailingNumber(str: string): string {
  // 使用正则表达式匹配字符串末尾的数字
  const match = str.match(/(\d+)$/);
  if (match) {
    // 提取匹配到的数字字符串
    const numberStr = match[0];
    // 将数字字符串转换为数字类型
    const number = parseInt(numberStr, 10);
    // 对数字加 1
    const newNumber = number + 1;
    // 将原字符串中匹配到的数字替换为新的数字
    return newNumber.toString();
  }
  // 如果没有匹配到数字，返回原字符串
  return '-1';
}

//相机选取图片保存到画布上
/*  async getCream() {
    try {
      const res = await cameraPicker.pick(getContext(this), [cameraPicker.PickerMediaType.PHOTO], {
        cameraPosition: camera.CameraPosition.CAMERA_POSITION_BACK
      })
      if (!res.resultUri) {
        return
      }
      const imageBitmap = new ImageBitmap(res.resultUri)
      //创建白板
      this.createWhiteBoard(imageBitmap)
    } catch (e) {
      console.error('ccc creamError', JSON.stringify(e))
    }
  }

  //相册选取图片保存到画布上
  async getPhoto() {
    try {
      const photoSelectOptions = new photoAccessHelper.PhotoSelectOptions()
      photoSelectOptions.MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE
      photoSelectOptions.maxSelectNumber = 1
      // 选择照片对象
      const photoPicker = new photoAccessHelper.PhotoViewPicker()
      const photoResult = await photoPicker.select(photoSelectOptions)
      const uri = photoResult.photoUris[0]
      const imageBitmap = new ImageBitmap(uri)
      this.createWhiteBoard(imageBitmap)
    } catch (e) {
      console.error('ccc creamError', JSON.stringify(e))
    }
  }
*/
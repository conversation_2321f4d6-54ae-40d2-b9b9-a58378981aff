#include "Decoder.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "common/dfx/log/AVCodecSampleLog.h"
#include <queue>
#include "render/include/PluginManager.h"  // 包含PluginManager的头文件
#undef LOG_TAG
#define LOG_TAG "Decoder"

namespace {
constexpr int BALANCE_VALUE = 5;
using namespace std::chrono_literals;
} // namespace

Decoder::~Decoder() { Decoder::StartRelease(); }

#include <iostream>
#include <string>
#include <native_image/native_image.h>
#include <native_window/external_window.h>
#include <native_buffer/native_buffer.h>
#include <multimedia/player_framework/native_avcodec_videodecoder.h>

int32_t Decoder::CreateVideoDecoder() {
    AVCODEC_SAMPLE_LOGW("video mime:%{public}s", sampleInfo_.videoCodecMime.c_str());
    int32_t ret = videoDecoder_->Create(sampleInfo_.videoCodecMime);
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGW("Create video decoder failed, mime:%{public}s", sampleInfo_.videoCodecMime.c_str());
    } else {
        videoDecContext_ = new CodecUserData;
        sampleInfo_.window = NativeXComponentSample::PluginManager::GetInstance()->GetWindowForChannel(sampleInfo_.channelId);
        ret = videoDecoder_->Config(sampleInfo_, videoDecContext_);
        CHECK_AND_RETURN_RET_LOG(ret == AVCODEC_SAMPLE_ERR_OK, ret, "Video Decoder config failed");
    }
    
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Decoder::Init(SampleInfo &sampleInfo) {
    std::unique_lock<std::mutex> lock(mutex_);
    CHECK_AND_RETURN_RET_LOG(!isStarted_, AVCODEC_SAMPLE_ERR_ERROR, "Already started.");

    sampleInfo_ = sampleInfo;
    videoDecoder_ = std::make_unique<VideoDecoder>();
    isReleased_ = false;
    
    int32_t ret = CreateVideoDecoder();
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGE("Create video decoder failed");
        doneCond_.notify_all();
        lock.unlock();
        StartRelease();
        return AVCODEC_SAMPLE_ERR_ERROR;
    }
    
    isInitialized_ = true;
    AVCODEC_SAMPLE_LOGI("Init Succeed");
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Decoder::Start() {
    std::unique_lock<std::mutex> lock(mutex_);
    int32_t ret;
    CHECK_AND_RETURN_RET_LOG(!isStarted_, AVCODEC_SAMPLE_ERR_ERROR, "Already started.");
    if (videoDecContext_) {
        ret = videoDecoder_->Start();
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Video Decoder start failed");
            lock.unlock();
            StartRelease();
            return AVCODEC_SAMPLE_ERR_ERROR;
        }
        isStarted_ = true;
        videoDecInputThread_ = std::make_unique<std::thread>(&Decoder::VideoDecInputThread, this);
        videoDecOutputThread_ = std::make_unique<std::thread>(&Decoder::VideoDecOutputThread, this);

        if (videoDecInputThread_ == nullptr || videoDecOutputThread_ == nullptr) {
            AVCODEC_SAMPLE_LOGE("Create thread failed");
            lock.unlock();
            StartRelease();
            return AVCODEC_SAMPLE_ERR_ERROR;
        }
    }
//    if (audioDecContext_) {
//        ret = audioDecoder_->Start();
//        if (ret != AVCODEC_SAMPLE_ERR_OK) {
//            AVCODEC_SAMPLE_LOGE("Audio Decoder start failed");
//            lock.unlock();
//            StartRelease();
//            return AVCODEC_SAMPLE_ERR_ERROR;
//        }
//        isStarted_ = true;
//        audioDecInputThread_ = std::make_unique<std::thread>(&Decoder::AudioDecInputThread, this);
//        audioDecOutputThread_ = std::make_unique<std::thread>(&Decoder::AudioDecOutputThread, this);
//        if (audioDecInputThread_ == nullptr || audioDecOutputThread_ == nullptr) {
//            AVCODEC_SAMPLE_LOGE("Create thread failed");
//            lock.unlock();
//            StartRelease();
//            return AVCODEC_SAMPLE_ERR_ERROR;
//        }
//    }
    // Clear the queue
    while (audioDecContext_ && !audioDecContext_->renderQueue.empty()) {
        audioDecContext_->renderQueue.pop();
    }
    if (audioRenderer_) {
        OH_AudioRenderer_Start(audioRenderer_);
    }
    AVCODEC_SAMPLE_LOGI("Succeed");
    doneCond_.notify_all();
    return AVCODEC_SAMPLE_ERR_OK;
}

void Decoder::StartRelease() {
    isInitialized_ = false;
    AVCODEC_SAMPLE_LOGI("start release");
//    if (audioRenderer_) {
//        OH_AudioRenderer_Stop(audioRenderer_);
//    }
    if (!isReleased_) {
        isReleased_ = true;
        Release();
    }
}

void Decoder::ReleaseThread() {
    // 先停止所有线程
    isStarted_ = false;
    
    // 等待并join线程,而不是直接detach
    if (videoDecInputThread_) {
        if (videoDecInputThread_->joinable()) {
            videoDecInputThread_->join();
        }
        videoDecInputThread_.reset();
    }
    
    if (videoDecOutputThread_) {
        if (videoDecOutputThread_->joinable()) {
            videoDecOutputThread_->join();
        }
        videoDecOutputThread_.reset();
    }

    if (audioDecInputThread_) {
        if (audioDecInputThread_->joinable()) {
            audioDecInputThread_->join();
        }
        audioDecInputThread_.reset();
    }

    if (audioDecOutputThread_) {
        if (audioDecOutputThread_->joinable()) {
            audioDecOutputThread_->join();
        }
        audioDecOutputThread_.reset();
    }
}

void Decoder::Release() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 先停止所有线程
    isStarted_ = false;
    
    // 通知所有条件变量,避免线程阻塞
    if (videoDecContext_) {
        videoDecContext_->inputDataCond.notify_all();
        videoDecContext_->inputCond.notify_all();
        videoDecContext_->outputCond.notify_all();
    }

    // 等待并释放线程
    ReleaseThread();
    
    if (videoDecoder_ != nullptr) {
        videoDecoder_->Release();
        videoDecoder_.reset();
    }    

    // 清理队列和其他资源
    if (videoDecContext_ != nullptr) {
        while (!videoDecContext_->inputBufferInfoQueue.empty()) {
            videoDecContext_->inputBufferInfoQueue.pop();
        }
        while (!videoDecContext_->outputBufferInfoQueue.empty()) {
            videoDecContext_->outputBufferInfoQueue.pop();
        }
        while (!videoDecContext_->inputDataBufferInfoQueue.empty()) {
            videoDecContext_->inputDataBufferInfoQueue.pop(); 
        }
        delete videoDecContext_;
        videoDecContext_ = nullptr; 
    }

    if (audioDecContext_ != nullptr) {
        delete audioDecContext_;
        audioDecContext_ = nullptr;
    }
    
    if (builder_ != nullptr) {
        OH_AudioStreamBuilder_Destroy(builder_);
        builder_ = nullptr;
    }

    doneCond_.notify_all();
    
    if (sampleInfo_.playDoneCallback && sampleInfo_.playDoneCallbackData) {
        sampleInfo_.playDoneCallback(sampleInfo_.playDoneCallbackData);
    }
}

#include <chrono>
#include <iostream>

int64_t GetMicroseconds() {
    // 获取当前时间点 ‌:ml-citation{ref="1,3" data="citationList"}
    auto now = std::chrono::high_resolution_clock::now();
    // 转换为自 Epoch 以来的微秒数 ‌:ml-citation{ref="4,5" data="citationList"}
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

void Decoder::VideoDecInputThread() {
    if (!videoDecContext_) {
        return;
    }

    while (isStarted_) {
        std::unique_lock<std::mutex> dataLock(videoDecContext_->inputDataMutex);
        
        // 增加超时等待,避免永久阻塞
        bool hasData = videoDecContext_->inputDataCond.wait_for(
            dataLock, 
            std::chrono::seconds(5),
            [this]() {
                return !isStarted_ || 
                    (videoDecContext_ && !videoDecContext_->inputDataBufferInfoQueue.empty());
            });

        if (!isStarted_) {
            break;
        }

        if (!hasData || videoDecContext_->inputDataBufferInfoQueue.empty()) {
            continue;
        }

        // 获取输入缓冲区
        std::unique_lock<std::mutex> bufferLock(videoDecContext_->inputMutex);
        if (!videoDecContext_) {  // 再次检查，因为可能在等待过程中被释放
            break;
        }

        bool hasBuffer = videoDecContext_->inputCond.wait_for(
            bufferLock, 5s, [this]() {
                return !isStarted_ || 
                    (videoDecContext_ && !videoDecContext_->inputBufferInfoQueue.empty());
            });

        if (!isStarted_ || !videoDecContext_) {
            break;
        }

        if (!hasBuffer || videoDecContext_->inputBufferInfoQueue.empty()) {
            continue;
        }

        // 获取缓冲区信息
        CodecBufferInfo bufferInfo = videoDecContext_->inputBufferInfoQueue.front();
        videoDecContext_->inputBufferInfoQueue.pop();
        
        if (!videoDecContext_->inputDataBufferInfoQueue.empty()) {
            CodecBufferInfo dataInfo = videoDecContext_->inputDataBufferInfoQueue.front();
            videoDecContext_->inputDataBufferInfoQueue.pop();

            if (videoDecoder_ && bufferInfo.buffer) {
                uint8_t* addr = OH_AVBuffer_GetAddr((OH_AVBuffer*)bufferInfo.buffer);
                if (addr && dataInfo.bufferAddr) {
                    memcpy(addr + bufferInfo.attr.offset, dataInfo.bufferAddr, dataInfo.attr.size);
                    delete[] static_cast<uint8_t*>(dataInfo.bufferAddr);
                    
                    bufferInfo.attr.size = dataInfo.attr.size;
                    OH_AVBuffer_SetBufferAttr((OH_AVBuffer*)bufferInfo.buffer, &bufferInfo.attr);
                    
                    videoDecContext_->inputFrameCount++;
                    
                    if (videoDecoder_->PushInputBuffer(bufferInfo) != AVCODEC_SAMPLE_ERR_OK) {
                        AVCODEC_SAMPLE_LOGE("Push data failed");
                        break;
                    }
                }
            }
        }
    }
}

void Decoder::VideoDecOutputThread() {
    if (!videoDecContext_) {
        return;
    }

    sampleInfo_.frameInterval = MICROSECOND / sampleInfo_.frameRate;
    while (isStarted_) {  // 改为 isStarted_ 检查
        std::unique_lock<std::mutex> lock(videoDecContext_->outputMutex);
        
        // 增加超时等待,避免永久阻塞
        bool hasData = videoDecContext_->outputCond.wait_for(
            lock, 
            std::chrono::seconds(5),
            [this]() {
                return !isStarted_ || 
                    (videoDecContext_ && !videoDecContext_->outputBufferInfoQueue.empty());
            });
            
        if (!isStarted_) {
            break;
        }

        if (!hasData || !videoDecContext_ || videoDecContext_->outputBufferInfoQueue.empty()) {
            continue;
        }

        CodecBufferInfo bufferInfo = videoDecContext_->outputBufferInfoQueue.front();
        videoDecContext_->outputBufferInfoQueue.pop();
        
        if (bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_EOS) {
            AVCODEC_SAMPLE_LOGI("Catch EOS, thread out");
            break;
        }
        
        if (!videoDecoder_) {
            break;
        }

        videoDecContext_->outputFrameCount++;
        
        int32_t ret = videoDecoder_->FreeOutputBuffer(bufferInfo.bufferIndex, true);
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Free output buffer failed");
            break;
        }

        auto now = std::chrono::system_clock::now();
        std::this_thread::sleep_until(now + std::chrono::microseconds(sampleInfo_.frameInterval));
    }

    // 不要在线程中直接调用 StartRelease
    // StartRelease();
}



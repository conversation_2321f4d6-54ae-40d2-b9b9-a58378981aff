import { BuilderNameConstants, RouterModule, RouterNameConstants } from 'routermoudel';
import { promptAction } from '@kit.ArkUI';
import { UserInfo } from 'basic';

@Component
export struct CreateMeeting {
  @State meetingTitle: string = '';
  @State meetingPassword: string = '';

  build() {
    NavDestination() {
      Column() {
        // 表单内容
        Column() {
          // 会议主题
          Column() {
            Row() {
              Text('会议主题')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)

              Blank()

              TextInput({ placeholder: '会议主题', text: this.meetingTitle })
                .width(200)
                .fontSize(16)
                .fontColor('#333333')
                .backgroundColor(Color.Transparent)
                .placeholderColor('#CCCCCC')
                .textAlign(TextAlign.End)
                .border({ width: 0 })
                .onChange((value: string) => {
                  this.meetingTitle = value;
                })
            }
            .width('100%')
            .height(50)
            .padding({ left: 16, right: 16 })
            .alignItems(VerticalAlign.Center)
          }
          .backgroundColor(Color.White)
          .borderRadius(0)

          // 分割线
          Divider()
            .strokeWidth(0.5)
            .color('#E5E5E5')
            .margin({ left: 16, right: 16 })

          // 会议密码
          Column() {
            Row() {
              Text('会议密码')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)

              Blank()

              TextInput({ placeholder: '请输入密码', text: this.meetingPassword })
                .width(200)
                .fontSize(16)
                .fontColor('#333333')
                .backgroundColor(Color.Transparent)
                .placeholderColor('#CCCCCC')
                .textAlign(TextAlign.End)
                .border({ width: 0 })
                .type(InputType.Password)
                .onChange((value: string) => {
                  this.meetingPassword = value;
                })
            }
            .width('100%')
            .height(50)
            .padding({ left: 16, right: 16 })
            .alignItems(VerticalAlign.Center)
          }
          .backgroundColor(Color.White)
          .borderRadius(0)
        }
        .backgroundColor(Color.White)
        .borderRadius(12)
        .margin({ top: 20, left: 16, right: 16 })

        Blank()

        // 底部按钮
        Column() {
          Button('发起会议')
            .width('100%')
            .height(50)
            .fontSize(18)
            .fontColor(Color.White)
            .backgroundColor('#007AFF')
            .borderRadius(12)
            .fontWeight(FontWeight.Medium)
            .onClick(() => {
              // 检查登录状态
              if (!UserInfo.getInstance().isLoggedIn()) {
                promptAction.showToast({ message: '匿名用户不能操作此功能' });
                return;
              }

              if (this.meetingTitle.trim() === '') {
                promptAction.showToast({ message: '请输入会议主题' });
                return;
              }

              // 这里可以添加创建会议的逻辑
              console.log('创建会议：', {
                meetingTitle: this.meetingTitle,
                meetingPassword: this.meetingPassword
              });

              // 创建成功后可以跳转到会议室页面或显示提示
              promptAction.showToast({ message: '会议创建成功' });
              RouterModule.pop(RouterNameConstants.HomeIndex);
            })
        }
        .width('100%')
        .padding({ left: 16, right: 16, bottom: 34 })
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F5F5F5')
    }
    .title('发起会议')
    .onBackPressed(() => {
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }
}

// 注册构建器
@Builder
export function CreateMeeting_Page(value: object) {
  CreateMeeting()
}

const builderName = BuilderNameConstants.CreateMeetingPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(CreateMeeting_Page);
  RouterModule.registerBuilder(builderName, builder);
}
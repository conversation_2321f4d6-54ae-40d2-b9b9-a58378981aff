import router from '@ohos.router';

// 定义设置项接口
interface SettingItem {
  title: string;
  value?: string;
  arrow?: boolean;
}

@Entry
@Component
struct SettingsPage {
  @State settingsItems: SettingItem[] = [
    { title: '账户', value: '未登录', arrow: true },
    { title: '服务器地址', arrow: true },
    { title: '视频设置', arrow: true },
    { title: '修改密码', arrow: true },
    { title: '清除缓存', arrow: true },
    { title: '关于我们', arrow: true }
  ]

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Text('设置')
          .fontSize(20)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .padding({ left: 16, right: 16, top: 16, bottom: 16 })
      .backgroundColor(Color.White)

      // 设置项列表
      List() {
        // 直接在List中使用ListItem，而不是ForEach
        ForEach(this.settingsItems, (item: SettingItem, index: number) => {
          ListItem() {
            Column() {
              Row() {
                Text(item.title)
                  .fontSize(16)
                  .fontColor('#333333')

                Blank()

                if (item.value) {
                  Text(item.value)
                    .fontSize(16)
                    .fontColor('#007AFF')
                }

                if (item.arrow) {
                  Text('>')
                    .fontSize(16)
                    .fontColor('#999999')
                    .margin({ left: 8 })
                }
              }
              .width('100%')
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })

              if (index < this.settingsItems.length - 1) {
                Divider()
                  .width('100%')
                  .height(1)
                  .color('#F0F0F0')
              }
            }
            .backgroundColor(Color.White)
          }
          .onClick(() => {
            // 处理设置项点击事件
            console.info(`点击了设置项: ${item.title}`);

            // 根据点击的设置项执行不同的操作
            if (item.title === '账户') {
              // 跳转到登录页面
              router.pushUrl({
                url: 'pages/LoginPage'
              }).catch((err:string) => {
                console.error(`跳转到登录页面失败: ${err}`);
              });
            }
          })
        })
      }
      .backgroundColor(Color.White)
      .margin({ top: 12 })


    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
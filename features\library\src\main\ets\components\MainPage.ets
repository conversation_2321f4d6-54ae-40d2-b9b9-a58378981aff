import testNapi from 'libhstinterface.so';
import { hilog } from '@kit.PerformanceAnalysisKit';


@Component
export struct MainPage {
  @State message: string = 'Hello World';

  aboutToAppear() {
    let retInit:boolean = testNapi.initSdk();
    hilog.info(0, "hongshantong", `hongshantong111 initSdk End, return: ${retInit}`);
    hilog.info(0, "hongshantong", "hongshantong joinConference begin");
    let retJoin:number = testNapi.conference_JoinConference("<root><return>0</return><message><confId>76157457</confId><ps><p><n>UserListFlag</n><v>0</v></p><p><n>VoteEnable</n><v>1</v></p><p><n>MeetingEndTime</n><v>2026-02-01 12:00:00.0</v></p><p><n>hardwareaudiocodec</n><v>1</v></p><p><n>AvOpenConfirm</n><v>false</v></p><p><n>TimeoutThreshold</n><v>100</v></p><p><n>DefaultRights</n><v>1072300032</v></p><p><n>resolution</n><v>4</v></p><p><n>PstnPhoneNumber</n></p><p><n>mtype</n><v>0</v></p><p><n>MeetingNo</n><v>100000000006231</v></p><p><n>ServiceType</n><v>24</v></p><p><n>PushRtmpUrl</n><v>rtmp://************:9018/live/100000000006115?token=fe2d3d6323385b3d403dfd6201c9c585101b8d5bd430408b95471830681c4f16043553852f3b1a231f2f546bf680e1ed5f8d7df67cb6401aecc91fa6b6ab5f2b06d8ac69b0dabd2fe6e895f22e7939jB6shnvjrGbnWVr9UAojnqSJMrczAHjM1g3Ch5RC1b04a9af8cf6313cc82e01db25462db4ce8495b4ddcc8a5ced772c9cf0</v></p><p><n>ExtendData</n><v>rvt93uTNQBAD6QYgT0WvJRqwLK68YpR5o/33hZy6/64y0JbtDskj1pvCGSoWrCYtaiGruUFeLT9px4DImtEBKNlTOSiyLev9j0VTG8e3jlFkV+Qu0YBLZllJiOVdY++tNqoYAUXJe46D9WB8BaIouw7iP++VNtaPs9F12sIPN8jhRMVqnGKW0Xpnj4Q0qebBOOyBKwNA|rOF30cPoXRYz9BA3PAD5JBCsd8jlJsEDv/TD7Lp7g5s5AX4fvd9qowKG4gUzgRWMnHoc7F5BKxZMzYbDh8cqE8FcPj+yJOCrk0JOCuWqrFFzXfgR3a1bZUhJtK0AOv71dPwEDFTzUpWT+WFhXbpl+Fy8KKrUUoSVv54ihpRPab22H584wk2Q3XRgmbUTse6eb66OJx5LKjFAPn3qURvcwZAssJeuz/Nb/1XlcQsTz4fW7zoqF4C306U+oi6GoEGv3mSxD7Lp7g5s5AX4fvd9qowKG4gUzgRWMnHoc7jUMAo1hFUlM7DKcrWIqxptEzNTFjoZbPz84ZjIZe+etqVV+4NQUg==</v></p><p><n>GoodNetworkTimeoutThreshold</n><v>70</v></p><p><n>SecurityFlag</n><v>1</v></p><p><n>netbandwidth</n><v>8</v></p><p><n>MixedScreen</n><v>1</v></p><p><n>hardwarenetbandwidth</n><v>8</v></p><p><n>MeetingPassword</n><v/></p><p><n>VideoAdaptive</n><v>0</v></p><p><n>MeetingType</n><v>2048</v></p><p><n>ModeSwitch</n><v>1</v></p><p><n>DefaultMode</n><v>1</v></p><p><n>SVC</n><v>1</v></p><p><n>dvrNumber</n></p><p><n>UserInfoEmail</n><v/></p><p><n>hardwarevideocodec</n><v>98</v></p><p><n>InviteWebApi</n><v>http://qa27.hongshantong.cn:9090/meeting</v></p><p><n>VideoChannelsForHost</n><v>64</v></p><p><n>SystemUserI</n><v>0</v></p><p><n>Timestamp</n><v>null</v></p><p><n>VideoFileMaxSize</n><v>0</v></p><p><n>VideoChannels</n><v>64</v></p><p><n>h323AutoCall</n><v>false</v></p><p><n>MaxAttendeeNumber</n><v>100</v></p><p><n>Jvtype</n><v>2</v></p><p><n>MonitorLimit</n><v>0</v></p><p><n>WebServiceIP</n><v>http://qa27.hongshantong.cn:9090/meeting</v></p><p><n>h323CallType</n><v>0</v></p><p><n>SiteID</n><v>1</v></p><p><n>audioquality</n><v>0</v></p><p><n>UploadFileMaxSize</n><v>500</v></p><p><n>ControllerList</n><v>tcp://qa27.hongshantong.cn:9091</v></p><p><n>PullRtmpUrl</n><v>rtmp://************:9018/live/100000000006115?token=bf063dcfed42fcfa3466033bb02ce251383ef465f052f7e7c6111ce1e393acf54c064b2e51966f75191a9427413d0903aa72c6f9d4ddac271f0e9cd95abcb87d66746ca4fdf13fc779bb0804cbffe8bc39f680abfcc0dad7a5420b2329e60dddfcbb30f09823c2154401433382cb2508337f36d13568ab9e7710466cdab066f1</v></p><p><n>token</n><v>39jB6shnvjrGbnWVr9UAojnqSJMrczAHjM9qowKG4gUzgRWMnHoc7F3F88BC8058</v></p><p><n>MeetingCreatorID</n><v>1;administrator;1</v></p><p><n>AudioChannels</n><v>100</v></p><p><n>SessionLoad</n><v>-68157440</v></p><p><n>SingleServer</n><v>1</v></p><p><n>AutoRecord</n><v>0</v></p><p><n>DroppedFlag</n><v>0</v></p><p><n>KeyWord</n><v>taNIR74UspH5kFqNx78NMq5Ltnxtn6auHfse9bxRtGoQeReUt47kx27NKmTfwKPD7Lp7g5s5AX4fvd9qowKG4gUzgRWMnHoc71zWX6OEEjQLzKOijSrdL547IwYj2ovo</v></p><p><n>FeedbackWindow</n><v>0</v></p><p><n>Recording</n><v>1</v></p><p><n>openTelConference</n><v>false</v></p><p><n>MeetingDuration</n><v>29552400</v></p><p><n>ConfRights</n><v>3222536192</v></p><p><n>SubConferenceNumber</n><v>0</v></p><p><n>enableLogging</n><v>0</v></p><p><n>ConfAreaCode</n><v>top</v></p><p><n>SiteName</n><v>box</v></p><p><n>ClusterID</n><v>cluster1</v></p><p><n>JoinBeforeWaitTime</n><v>5400</v></p><p><n>TotalPersons</n><v>0</v></p><p><n>UserAreaCode</n><v>top</v></p><p><n>AutoJoinType</n><v>false</v></p><p><n>hardwareresolution</n><v>8</v></p><p><n>CourseNum</n><v>76157457</v></p><p><n>SiteUserPass</n><v/></p><p><n>AudioConnect</n><v>UDP</v></p><p><n>ChatSendInterval</n><v>5</v></p><p><n>RecordLoginInfo</n><v>1</v></p><p><n>defaultposition</n><v>3</v></p><p><n>IntergrateValue</n><v>121</v></p><p><n>HostKey</n><v>123456</v></p><p><n>FileEnable</n><v>1</v></p><p><n>5GVoTEL</n><v>1</v></p><p><n>Language</n><v>zh_CN</v></p><p><n>fromType</n><v>1</v></p><p><n>UserID</n><v>322744223</v></p><p><n>VideoFullScreenFlag</n><v>0</v></p><p><n>SiteUserName</n><v/></p><p><n>BadNetworkTimeoutThreshold</n><v>100</v></p><p><n>wx</n><v>1</v></p><p><n>JoinBefore</n><v>1</v></p><p><n>LicenceType</n><v>0</v></p><p><n>Terminal</n><v>true</v></p><p><n>assistantUrl</n><v>http://qa27.hongshantong.cn:9090/live/app/user/assistant/chat.action?site=box&confId=100000000006115&nickname=harmony&attId=0&signature=2afc4e70a8a53447d369a2e1f78d76ae</v></p><p><n>MixScreen</n><v>1</v></p><p><n>MeetingStartTime</n><v>2025-02-24 11:00:00.0</v></p><p><n>VideoQuality</n><v>1</v></p><p><n>ReconnectTimeout</n><v>4</v></p><p><n>UserRole</n><v>attendee</v></p><p><n>UserInfoName</n><v>harmony</v></p><p><n>MeetingName</n><v>fangpu鐨勪細璁?/v></p><p><n>Hvtype</n><v>1</v></p><p><n>chatUrl</n><v>http://qa27.hongshantong.cn:9090/live/app/userview/chat.action?site=box&confId=100000000006115&nickname=harmony&attId=0&signature=2afc4e70a8a53447d369a2e1f78d76ae</v></p></ps><iwloader><DownFileVersion>V8400.2024.1114.0</DownFileVersion><RootURL>http://qa27.hongshantong.cn:9090/download/box40</RootURL><AppFileListURL>/client/meeting/v4.0.0.0/conf.ini</AppFileListURL><DownFileURL>/download/v8400.2024.1114.0/windows/UpdateCore.dll</DownFileURL><LocalPath>conferenceMeeting</LocalPath><ContentLanguage>zh_CN</ContentLanguage></iwloader></message></root>")
    hilog.info(0, "hongshantong", `hongshantong joinConference End, return: ${retJoin}`);
  }

  build() {
    Row() {
      Column() {
        Text(this.message)
          .fontSize($r('app.float.page_text_font_size'))
          .fontWeight(FontWeight.Bold)
          .onClick(() => {
            this.message = 'Welcome';
          })
      }
      .width('100%')
    }
    .height('100%')
  }
}

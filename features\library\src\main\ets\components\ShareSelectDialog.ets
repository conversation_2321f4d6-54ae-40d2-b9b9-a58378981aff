@CustomDialog
export struct ShareSelectDialog {
  dialogController: CustomDialogController
  creamClick: () => void = () => {
  }
  photoClick: () => void = () => {

  }
  whiteBoardClick: () => void = () => {

  }
  desktopClick: () => void = () => {

  }


  build() {
    Column() {
      Text('选择共享')
        .fontSize(15)
        .fontColor(Color.Gray)
        .width('100%')
        .padding(10)
        .backgroundColor(Color.White)
        .textAlign(TextAlign.Center)

      // Divider()
      //
      // Button('拍照')
      //   .buttonExtend('#ff0000')
      //   .onClick(() => {
      //     this.creamClick()
      //     this.dialogController.close()
      //   })
      //
      // Divider()
      //
      // Button('相册')
      //   .buttonExtend('#007DFF')
      //   .onClick(() => {
      //     this.photoClick()
      //     this.dialogController.close()
      //   })

      Divider()

      Button('白板')
        .buttonExtend('#007DFF')
        .onClick(() => {
          this.whiteBoardClick()
          this.dialogController.close()
        })

      Divider()

      Button('桌面')
        .buttonExtend('#007DFF')
        .onClick(() => {
          this.desktopClick()
          this.dialogController.close()
        })

      Column() {
        Button('取消')
          .buttonExtend('#007DFF')
          .onClick(()=>{
              this.dialogController.close()
          })
      }
      .justifyContent(FlexAlign.End)
      .height(52)
      .width('100%')
      .backgroundColor(Color.Transparent)

    }
  }
}

@Extend(Button)
function buttonExtend(fontColor: ResourceColor) {
  .fontSize(20)
  .type(ButtonType.Normal)
  .width('100%')
  .fontWeight(400)
  .backgroundColor(Color.White)
  .fontColor(fontColor)
  .padding(12)
}
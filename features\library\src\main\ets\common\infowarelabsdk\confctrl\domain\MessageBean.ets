import { faceDetector } from "@kit.CoreVisionKit";

export class MessageBean {
  // 序列化版本号（TypeScript 中没有直接等价的概念，通常不需要）
  private static readonly serialVersionUID: number = 1;

  /** 用户ID */
  private uid: number;
  /** 用户名 */
  private username: string;

  /** 时间 */
  private date: string;
  /** 消息内容 */
  private message: string;
  /** 是否公聊 */
  private isPublic: boolean;

  /** 是否是收到的消息 */
  private isComeMeg: boolean;

  /** 是否显示时间 */
  private isShowTime: boolean;

  /** 是否已读 */
  private isReaded: boolean;

  constructor(
    uid: number = 0,
    username: string = '',
    date: string = '',
    message: string = '',
    isPublic: boolean = false,
    isComeMeg: boolean = false,
    isShowTime: boolean = false,
    isReaded: boolean = false
  ) {
    this.uid = uid
    this.username = username
    this.date = date
    this.message = message
    this.isPublic = isPublic
    this.isComeMeg = isComeMeg
    this.isShowTime = isShowTime
    this.isReaded = isReaded
  }

  getUid() : number {
    return this.uid;
  }

  setUid(uid:number) {
    this.uid = uid;
  }

  getUsername() : string {
    return this.username;
  }

  setUsername (username:string) {
    this.username = username;
  }

  getDate() : string {
  return this.date;
 }

  setDate(date:string) {
    this.date = date;
  }

  getMessage() : string {
    return this.message;
  }

  setMessage(message : string) {
    this.message = message;
  }

  isPublicFun() : boolean {
    return this.isPublic;
  }

  setPublic(isPublic : boolean) {
    this.isPublic = isPublic;
  }

  setComeMeg(isComeMeg : boolean) {
    this.isComeMeg = isComeMeg;
  }

  sComeMegFun() : boolean {
    return this.isComeMeg;
  }

  isShowTimeFun() : boolean {
    return this.isShowTime;
  }

  setShowTime(isShowTime : boolean) {
    return this.isShowTime = isShowTime;
  }

  isReadedFun() : boolean {
    return this.isReaded;
  }

  setReaded(isReaded : boolean) {
    this.isReaded = isReaded;
  }
}

//
// Created on 2025/3/20.
//
// Node APIs are not fully supported. To solve the compilation error of the interface cannot be found,
// please include "napi/native_api.h".

#ifndef HSMEETING_HARMONYOS_CAPTURE_H
#define HSMEETING_HARMONYOS_CAPTURE_H

#include <js_native_api_types.h>
#include <multimedia/player_framework/native_avscreen_capture_base.h>
#include <node_api_types.h>
#include <thread>
#include <mutex>
#include <atomic>
#include <condition_variable>
#include <queue>
#include "capbilities/include/VideoEncoder.h"
#include "common/SampleInfo.h"

// 修改：使用CodecUserData而不是重新定义CaptureUserData
// 如果需要额外字段，可以扩展CodecUserData或创建一个新的结构体，但使用不同的名称
class CaptureNative {
public:
    // 添加构造函数来正确初始化静态成员
    CaptureNative() : encOutputThread_(nullptr), videoEncoder_(nullptr), encContext_(nullptr) {}

    static napi_value Init(napi_env env, napi_callback_info info);
    static void Release();
    
    // 添加设置回调的方法
    static napi_value RegisterCallback(napi_env env, napi_callback_info info);

    static CaptureNative &GetInstance() {
        static CaptureNative capture;
        return capture;
    }

    // Add callback types
    static void CallStopCallback(int32_t state);

    // 添加主线程回调相关方法
    static void PostCallbackToMainThread(int32_t state);
    static napi_value CreateAsyncWork(napi_env env, int32_t state);
    static void ExecuteWork(napi_env env, void* data);
    static void CompleteWork(napi_env env, napi_status status, void* data);

    // 修改回调函数签名
    static void CallJs(napi_env env, napi_value js_cb, void* context, void* data);

    // 添加析构函数确保资源释放
    ~CaptureNative() {
        Release();
    }

    // Add this line in the public section of the CaptureNative class
    static napi_value ReleaseNative(napi_env env, napi_callback_info info);

private:
    static void OnError(OH_AVScreenCapture* capture, int32_t error, void* userData);
    static void OnStateChange(OH_AVScreenCapture* capture, OH_AVScreenCaptureStateCode stateCode, void* userData);
    static void OnBufferAvailable(OH_AVScreenCapture* capture, OH_AVBuffer* buffer,
        OH_AVScreenCaptureBufferType bufferType, int64_t timestamp, void* userData);
    
    // 非静态方法，处理编码数据
    void EncOutputThread();
    static void StartRelease();
    
    // 非静态成员
    std::unique_ptr<std::thread> encOutputThread_;
    std::unique_ptr<VideoEncoder> videoEncoder_;
    SampleInfo sampleInfo_;
    CodecUserData *encContext_;
    
    // 静态成员
    static std::unique_ptr<std::thread> releaseThread_;
    static std::mutex mutex_;
    static std::atomic<bool> isStarted_;
    static std::condition_variable doneCond_;
    
    // 添加回调相关的静态成员
    static napi_ref stopCallback_;
    static napi_env env_;

    // 添加异步工作相关成员
    static napi_threadsafe_function tsfn_;
};

#endif //HSMEETING_HARMONYOS_CAPTURE_H

#include "DecoderNative.h"
#include "VideoData.h"
#include "common/SampleInfo.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "common/dfx/log/AVCodecSampleLog.h"
#include "encoder/Encorder.h"
#include "render/include/PluginManager.h"  // 包含PluginManager的头文件
#include "videoDataSink.h"

#undef LOG_DOMAIN
#undef LOG_TAG
#define LOG_DOMAIN 0xFF00
#define LOG_TAG "Decoder"

DecoderNative* DecoderNative::instance_ = nullptr;

DecoderNative::DecoderNative() {
    m_pVideoDataSink = nullptr;
}

DecoderNative::~DecoderNative() {
    if (m_pVideoDataSink) {
        m_pVideoDataSink->ClearAllDecoders();
    }
}

DecoderNative* DecoderNative::GetInstance() {
    if (instance_ == nullptr) {
        instance_ = new DecoderNative();
    }
    return instance_;
}

void DecoderNative::SetVideoDataSink(CVideoDataSink* pSink) {
    m_pVideoDataSink = pSink;
}

void Callback(void *asyncContext) {
    uv_loop_s *loop = nullptr;
    CallbackContext *context = (CallbackContext *)asyncContext;
    napi_get_uv_event_loop(context->env, &loop);
    uv_work_t *work = new uv_work_t;
    work->data = context;
    uv_queue_work(
        loop, work, [](uv_work_t *work) {},
        [](uv_work_t *work, int status) {
            CallbackContext *context = (CallbackContext *)work->data;
            napi_handle_scope scope = nullptr;
            // Manage the lifecycle of napi_value to prevent memory leaks.
            napi_open_handle_scope(context->env, &scope);
            napi_value callback = nullptr;
            napi_get_reference_value(context->env, context->callbackRef, &callback);
            // Callback to UI side.
            napi_call_function(context->env, nullptr, callback, 0, nullptr, nullptr);
            napi_close_handle_scope(context->env, scope);
            delete context;
            delete work;
        });
}

napi_value DecoderNative::Play(napi_env env, napi_callback_info info) {
    SampleInfo sampleInfo;
    size_t argc = 4;
    napi_value args[4] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    int32_t two = 2;
    int32_t three = 3;
    napi_get_value_int32(env, args[0], &sampleInfo.inputFd);
    napi_get_value_int64(env, args[1], &sampleInfo.inputFileOffset);
    napi_get_value_int64(env, args[two], &sampleInfo.inputFileSize);

    auto asyncContext = new CallbackContext();
    asyncContext->env = env;
    napi_create_reference(env, args[three], 1, &asyncContext->callbackRef);

    sampleInfo.playDoneCallback = &Callback;
    sampleInfo.playDoneCallbackData = asyncContext;
    int32_t ret = Decoder::GetInstance().Init(sampleInfo);
    if (ret == AVCODEC_SAMPLE_ERR_OK) {
        Decoder::GetInstance().Start();
    }
    return nullptr;
}

// Add new Release implementation
napi_value DecoderNative::Release(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    // Get channel ID parameter
    int32_t channelId = -1;
    if (argc >= 1) {
        napi_get_value_int32(env, args[0], &channelId);
    }

    auto instance = GetInstance();
    if (instance && instance->m_pVideoDataSink) {
        // If channelId is provided, remove specific decoder
        if (channelId >= 0) {
            instance->m_pVideoDataSink->RemoveDecoder(channelId);
        } else {
            // If no channelId provided, clear all decoders
            instance->m_pVideoDataSink->ClearAllDecoders();
        }
    }

    return nullptr;
}

// Modify Init function
EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports) {
    // Create videoDataSink instance
    auto videoDataSink = new CVideoDataSink();
    
    // Get callback function
    napi_value callback = nullptr;
    napi_get_named_property(env, exports, "onPlayDone", &callback);
    if (callback != nullptr) {
        videoDataSink->SetCallback(env, callback);
    }
    
    // Set videoDataSink to DecoderNative instance
    DecoderNative::GetInstance()->SetVideoDataSink(videoDataSink);
    
    // Register videoDataSink
    registerVideoDataSink(videoDataSink);

    // Create property descriptor for Release method
    napi_property_descriptor desc[] = {
        { "release", nullptr, DecoderNative::Release, nullptr, nullptr, nullptr, napi_default, nullptr }
    };

    // Register the Release method
    napi_define_properties(env, exports, 1, desc);

    NativeXComponentSample::PluginManager::GetInstance()->Export(env, exports);
    return exports;
}
EXTERN_C_END

static napi_module DecoderModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "player",
    .nm_priv = ((void *)0),
    .reserved = {0},
};

extern "C" __attribute__((constructor)) void RegisterDecoderModule(void) { napi_module_register(&DecoderModule); }
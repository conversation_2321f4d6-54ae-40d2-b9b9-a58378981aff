#ifndef HARMONYSDK_VIDEODATASINK_H
#define HARMONYSDK_VIDEODATASINK_H

#include "node_api.h"
#include "VideoData.h"
#include "Decoder.h"
#include <map>

struct CallbackContext {
    napi_env env = nullptr;
    napi_ref callbackRef = nullptr;
};

class CVideoDataSink : public IVideoDataSink {
public:
    CVideoDataSink();
    ~CVideoDataSink();

    void SetCallback(napi_env env, napi_value callback);
    void OnVideoData(void* pData, int nDataLength, int nChannelId);
    void RemoveDecoder(int nChannelId);
    void ClearAllDecoders();
    
    // 添加友元类声明
    friend class DecoderNative;

private:
    bool InitDecoder(Decoder* decoder, int channelId);

    std::mutex decoderMutex_;
    napi_env env_;              
    napi_ref callbackRef_;      
    std::map<int, Decoder*> mapDecoder_;
};

#endif //HARMONYSDK_VIDEODATASINK_H
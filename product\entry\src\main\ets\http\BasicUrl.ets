import { serverConfig } from 'basic';

// 获取服务器地址的函数
function getServerUrl(): string {
  try {
    // 使用ServerConfig获取服务器地址，确保数据一致性
    return serverConfig.getServerUrl();
  } catch (error) {
    console.error('获取服务器地址失败:', error);
    return ''; // 移除硬编码的qa27地址
  }
}

// 动态获取服务器地址
export const getBaseUrl = (): string => `${getServerUrl()}/meeting/remoteServlet?funcName=joinConf&`;
export const getGetConfListUrl = (): string => getServerUrl();

// 兼容性导出（保持向后兼容）
export const BaseUrl: string = `${getServerUrl()}/meeting/remoteServlet?funcName=joinConf&`;
export const GetConfListUrl: string = getServerUrl();
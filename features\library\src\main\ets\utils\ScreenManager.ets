import { display, KeyboardAvoidMode, window } from '@kit.ArkUI'

class ScreenManager {
  sysBarProps: window.SystemBarProperties | null = null
  isImmersive: boolean = false //在进入会议前，用户是否已经自行开启的沉浸式
  orientation: window.Orientation | null = null

  // 全屏
  async full(ctx?: Context) {
    const win = await window.getLastWindow(ctx || getContext())
    //查询原应用是否开启了沉浸式，如果开启了，退出时无需关闭沉浸式，否则则要关闭沉浸式
    this.isImmersive = win.getImmersiveModeEnabledState()
    console.log('qq是否开启沉浸式', this.isImmersive)
    if (!this.isImmersive) {
      await win.setWindowLayoutFullScreen(true)
    }
    this.sysBarProps = win.getWindowSystemBarProperties()
    //设置状态栏字体颜色
    let sysBarProps: window.SystemBarProperties = {
      statusBarColor: '#000000',
      statusBarContentColor: '#ffffff'
    };
    win.setWindowSystemBarProperties(sysBarProps)
  }

  //获取顶部安全区高度
  async getTopHeight(ctx?: Context) {
    const win = await window.getLastWindow(ctx || getContext())
    return px2vp(win.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM).topRect.height)
  }

  //获取底部安全区高度
  async getBottomHeight(ctx?: Context) {
    const win = await window.getLastWindow(ctx || getContext())
    return px2vp(win.getWindowAvoidArea(window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR).bottomRect.height)
  }

  // 退出全屏
  async exitFull(ctx?: Context) {
    //用户在进入会议前未开启沉浸式，则退出会议时取消沉浸式
    const win = await window.getLastWindow(ctx || getContext())
    if (!this.isImmersive) {
      win.setWindowLayoutFullScreen(false)
    }
    //设置恢复状态栏字体颜色
    win.setWindowSystemBarProperties(this.sysBarProps)
  }

  // 设置键盘压缩
  async setKeyBoardMode(ctx: Context, isResize: boolean) {
    const win = await window.getLastWindow(ctx || getContext())
    win.getUIContext().setKeyboardAvoidMode(isResize ? KeyboardAvoidMode.RESIZE : KeyboardAvoidMode.NONE)
    const keyboardArea = win.getWindowAvoidArea(window.AvoidAreaType.TYPE_KEYBOARD);
    console.log('cccq', JSON.stringify(keyboardArea))
  }

  //设置屏幕常亮
  async setWindowKeepScreenOn(ctx: Context, isKeep: boolean) {
    const win = await window.getLastWindow(ctx || getContext())
    win.setWindowKeepScreenOn(isKeep, () => {
      console.log('kkk 设置屏幕常亮 ' + isKeep + ' success')
    })
  }

  //设置屏幕旋转
  async setWindowRotation(ctx: Context, rotationCallback: (rotation: number) => void) {

    // 获取最上层窗口的方式
    const mainWin = await window.getLastWindow(ctx || getContext());
    this.orientation = mainWin.getPreferredOrientation()
    mainWin.setPreferredOrientation(window.Orientation.LANDSCAPE, () => {
      // 使用display接口获取当前旋转方向，可以放置在监听中持续获取
      const rotation: number = display.getDefaultDisplaySync().rotation;
      console.log('旋转了：' + rotation.toString())
      rotationCallback(rotation)
    });
    //注册屏幕旋转监听
    display.on('change', () => {
      const rotation = display.getDefaultDisplaySync().rotation;
      console.log('旋转了：' + rotation.toString())
      rotationCallback(rotation)
    })
  }

  async setRotationOrientation(ctx: Context, orientation: window.Orientation) {
    const mainWin = await window.getLastWindow(ctx || getContext());
    mainWin.setPreferredOrientation(orientation)
  }

  async unSetWindowRotation(ctx: Context) {
    const mainWin = await window.getLastWindow(ctx || getContext());
    mainWin.setPreferredOrientation(this.orientation, () => {
      console.log('恢复旋转模式')
    });
    display.off('change')
  }
}


export const screenManager = new ScreenManager()
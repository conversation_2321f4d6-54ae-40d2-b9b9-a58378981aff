# 服务器地址持久化测试说明

## 修改内容

1. **ServerConfig类改进**：
   - 添加了Preferences持久化存储支持
   - 使用`@kit.ArkData`的preferences API
   - 添加了降级机制，如果Preferences初始化失败会使用AppStorage作为备选
   - 所有存储操作改为异步，避免阻塞UI线程

2. **权限添加**：
   - 在`module.json5`中添加了`ohos.permission.STORE_PERSISTENT_DATA`权限

3. **初始化改进**：
   - 在EntryAbility中初始化ServerConfig
   - 设置页面中确保ServerConfig已初始化

4. **代码统一**：
   - 修改所有直接使用AppStorage获取serverUrl的地方，改为使用ServerConfig
   - 包括JoinMeetingPage、MeetingService、BasicUrl等文件
   - 确保整个应用使用统一的服务器地址管理机制

## 测试步骤

1. **设置服务器地址**：
   - 打开应用
   - 进入设置页面
   - 点击"服务器地址"
   - 输入新的服务器地址并保存

2. **验证持久化**：
   - 完全退出应用（从后台清除）
   - 重新启动应用
   - 检查服务器地址是否保持之前设置的值

3. **验证功能**：
   - 确认网络请求使用正确的服务器地址
   - 测试登录、会议列表等功能

## 技术细节

- **存储机制**：使用HarmonyOS的Preferences API进行持久化存储
- **备选方案**：如果Preferences初始化失败，降级使用AppStorage
- **同步机制**：Preferences和AppStorage保持同步，确保UI组件能正常访问数据
- **异步处理**：所有存储操作都是异步的，避免阻塞UI线程

## 预期结果

修改后，服务器地址设置应该能够在应用退出后保持，不会因为清理内存而丢失。

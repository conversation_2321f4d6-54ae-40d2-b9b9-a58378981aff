import testNapi from 'libhstinterface.so'

function SendData(){

}
//打开音频
/**
 * num1 为1 打开音频
 * 为 0 关闭音频
 * */
export function OpenAudio(num1:number , num2:number):number{
  return testNapi.audio_OpenAudio(num1, num2);
}

//静音
function StopReceive(){
  testNapi.audio_StopReceive()
}

//取消静音
function StartReceive(){
  testNapi.audio_StartReceive();
}

//设置音频状态
function SetStatus(){

}
//开始发送音频
function StartSend(){

}
//停止发送音频
function StopSend(){

}

export function registerCallback(){
  //testNapi.registerCallback('audio_onOpenAudioConfirm',AudioCallBackService.OpenAudio)
}

export function unregisterCallback(){
  //testNapi.unregisterCallback('audio_onOpenAudioConfirm',AudioCallBackService.OpenAudio)
}
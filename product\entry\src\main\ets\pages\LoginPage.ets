import router from '@ohos.router';

@Entry
@Component
struct LoginPage {
  @State username: string = '';
  @State password: string = '';

  build() {
    Column() {
      // 返回按钮
      Row() {
        Image($r('app.media.back'))
          .width(24)
          .height(24)
          .margin({ left: 16 })
          .onClick(() => {
            router.back();
          })
        Blank()
      }
      .width('100%')
      .height(56)
      .alignItems(VerticalAlign.Center)
      
      // Logo
      Image($r('app.media.hs_logo'))
        .width(120)
        .height(120)
        .margin({ top: 60, bottom: 40 })
      
      // 用户名输入框
      Column() {
        Text('用户名')
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)

        TextInput({ placeholder: '请输入用户名' })
          .width('100%')
          .height(48)
          .margin({ top: 4 })
          .borderRadius(4)
          .backgroundColor(Color.White)
          .onChange((value: string) => {
            this.username = value;
          })
      }
      .width('90%')
      .margin({ top: 12 })

      // 密码输入框
      Column() {
        Text('密码')
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)

        TextInput({ placeholder: '请输入密码' })
          .width('100%')
          .height(48)
          .margin({ top: 4 })
          .borderRadius(4)
          .backgroundColor(Color.White)
          .type(InputType.Password)
          .onChange((value: string) => {
            this.password = value;
          })
      }
      .width('90%')
      .margin({ top: 12 })
      
      // 登录按钮
      Button('登录', { type: ButtonType.Normal })
        .width('90%')
        .height(50)
        .fontSize(16)
        .backgroundColor('#007AFF')
        .borderRadius(25)
        .margin({ top: 40 })
        .onClick(() => {
          // 处理登录逻辑
          console.info(`用户登录: ${this.username}, ${this.password}`);
          
          // 登录成功后返回设置页面
          router.back();
        })
      
      // 匿名登录选项
      Row() {
        Text('匿名登录 >')
          .fontSize(14)
          .fontColor('#007AFF')
      }
      .margin({ top: 20 })
      .onClick(() => {
        // 处理匿名登录逻辑
        console.info('用户选择匿名登录');

        // 这个页面似乎不是主要使用的登录页面，但为了一致性也添加匿名登录逻辑
        // 创建匿名用户信息（需要先导入loginApi）
        // 暂时只是返回，主要逻辑在settings模块的LoginPage中
        router.back();
      })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.White)
    .padding({ left: 16, right: 16 })
  }
}
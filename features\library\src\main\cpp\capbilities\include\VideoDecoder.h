#ifndef VIDEODECODER_H
#define VIDEODECODER_H

#include "common/SampleInfo.h"
#include "multimedia/player_framework/native_avcodec_videodecoder.h"
#include "multimedia/player_framework/native_avbuffer_info.h"

class VideoDecoder {
public:
    enum class Type {
        NORMAL,
        SCREEN_SHARE
    };

    VideoDecoder(Type type = Type::NORMAL) : type_(type), isAVBufferMode_(false), decoder_(nullptr) {}
    ~VideoDecoder();

    int32_t Create(const std::string &videoCodecMime);
    int32_t Config(const SampleInfo &sampleInfo, CodecUserData *codecUserData);
    int32_t PushInputBuffer(CodecBufferInfo &info);
    int32_t FreeOutputBuffer(uint32_t bufferIndex, bool render);
    int32_t Start();
    int32_t Release();

private:
    int32_t SetCallback(CodecUserData *codecUserData);
    int32_t Configure(const SampleInfo &sampleInfo);

    Type type_;
    bool isAVBufferMode_;
    OH_AVCodec *decoder_;
};
#endif // VIDEODECODER_H
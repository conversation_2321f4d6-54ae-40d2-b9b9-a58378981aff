import { BuilderNameConstants, builderRouterModel, RouterModule, RouterNameConstants } from "routermoudel"
import router from '@ohos.router';
import emitter from '@ohos.events.emitter';
import { loginApi } from 'basic';

// 定义设置项接口
interface SettingItem {
  title: string;
  value?: string;
  arrow?: boolean;
}

// 用户信息接口
interface UserInfoData {
  userId: number;
  userName: string;
  realName: string;
  nickName: string;
  roles: string;
  token: string;
  email: string;
}

@Component
struct SettingsPage {
  @StorageLink('isLoggedIn') isLoggedIn: boolean = false;
  @StorageLink('userInfo') userInfoStr: string = '';
  @State settingsItems: SettingItem[] = [
    { title: '账户', value: '未登录', arrow: true },
    { title: '服务器地址', arrow: true },
    { title: '视频设置', arrow: true },
    { title: '修改密码', arrow: true },
    { title: '清除缓存', arrow: true },
    { title: '关于我们', arrow: true }
  ]

  aboutToAppear() {
    // 从AppStorage获取用户信息
    this.updateLoginStatus();

    // 注册登录状态变化监听
    emitter.on('loginSuccess', () => {
      console.info('设置页面收到登录状态变化事件');
      this.updateLoginStatus();
    });
  }

  onPageShow() {
    // 页面显示时更新登录状态
    console.info('设置页面显示，更新登录状态');
    this.updateLoginStatus();
  }

  aboutToDisappear() {
    // 发送导航栏重置事件
    emitter.emit('resetNavigation');

    // 取消事件监听
    emitter.off('loginSuccess');
  }

  updateLoginStatus() {
    try {
      console.info('更新登录状态，isLoggedIn:', this.isLoggedIn, 'userInfoStr:', this.userInfoStr);

      // 获取当前用户信息
      const currentUser = loginApi.getCurrentUser();

      // 检查是否为正式登录用户（排除匿名用户）
      if (currentUser.isLoggedIn() && this.isLoggedIn && this.userInfoStr) {
        // 解析用户信息
        const userInfo: UserInfoData = JSON.parse(this.userInfoStr);

        // 更新账户设置项显示用户名
        const newValue = userInfo.userName || userInfo.realName || '已登录';
        console.info('设置账户显示为:', newValue);

        // 创建新的数组来触发UI更新
        this.settingsItems = [
          { title: '账户', value: newValue, arrow: true },
          { title: '服务器地址', arrow: true },
          { title: '视频设置', arrow: true },
          { title: '修改密码', arrow: true },
          { title: '清除缓存', arrow: true },
          { title: '关于我们', arrow: true }
        ];
      } else {
        // 未登录状态或匿名用户状态
        console.info('设置账户显示为: 未登录');

        // 创建新的数组来触发UI更新
        this.settingsItems = [
          { title: '账户', value: '未登录', arrow: true },
          { title: '服务器地址', arrow: true },
          { title: '视频设置', arrow: true },
          { title: '修改密码', arrow: true },
          { title: '清除缓存', arrow: true },
          { title: '关于我们', arrow: true }
        ];
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);

      // 创建新的数组来触发UI更新
      this.settingsItems = [
        { title: '账户', value: '未登录', arrow: true },
        { title: '服务器地址', arrow: true },
        { title: '视频设置', arrow: true },
        { title: '修改密码', arrow: true },
        { title: '清除缓存', arrow: true },
        { title: '关于我们', arrow: true }
      ];
    }
  }

  build() {
    NavDestination() {
      Column() {
      // 顶部标题栏
      // Row() {
      //   Text('设置')
      //     .fontSize(20)
      //     .fontWeight(FontWeight.Medium)
      //     .fontColor('#333333')
      // }
      // .width('100%')
      // .justifyContent(FlexAlign.Center)
      // .padding({ left: 16, right: 16, top: 16, bottom: 16 })
      // .backgroundColor(Color.White)

      // 设置项列表
      List() {
        // 直接在List中使用ListItem，而不是ForEach
        ForEach(this.settingsItems, (item: SettingItem, index: number) => {
          ListItem() {
            Column() {
              Row() {
                Text(item.title)
                  .fontSize(16)
                  .fontColor($r('app.color.text_input_color'))

                Blank()

                if (item.value) {
                  Text(item.value)
                    .fontSize(16)
                    .fontColor('#007AFF')
                }

                if (item.arrow) {
                  Text('>')
                    .fontSize(16)
                    .fontColor($r('app.color.arrow_color'))
                    .margin({ left: 8 })
                }
              }
              .width('100%')
              .padding({ left: 16, right: 16, top: 16, bottom: 16 })

              if (index < this.settingsItems.length - 1) {
                Divider()
                  .width('100%')
                  .height(1)
                  .color($r('app.color.line_color'))
              }
            }
            .backgroundColor($r('app.color.back_grand_color'))
          }
          .onClick(() => {
            // 处理设置项点击事件
            console.info(`点击了设置项: ${item.title}`);

            // 根据点击的设置项执行不同的操作
            if (item.title === '账户') {
              // 获取当前用户信息
              const currentUser = loginApi.getCurrentUser();

              // 检查是否为正式登录用户（排除匿名用户）
              if (currentUser.isLoggedIn()) {
                // 正式登录用户，跳转到退出账户页面
                builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.LogoutPage, new Object({
                  origin: 'www'
                }));
              } else {
                // 未登录或匿名用户，跳转到登录页面
                builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.LoginPage, new Object({
                  origin: 'www'
                }));
              }
            } else if (item.title === '服务器地址') {
              // 跳转到服务器地址设置页面
              builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.ServerSettingsPage, new Object({
                origin: 'www'
              }));
            }
          })
        })
      }
      .backgroundColor($r('app.color.back_grand_color'))
      .margin({ top: 12 })


    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.back_grand_color2'))
  }
    .title('设置')
    .onBackPressed(() => {
      // 在返回前立即发送重置事件
      emitter.emit('resetNavigation');
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }
}

@Builder
export function AppSetting_Page(value: object) {
  SettingsPage()
}

const builderName = BuilderNameConstants.SettingsPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(AppSetting_Page);
  RouterModule.registerBuilder(builderName, builder);
}






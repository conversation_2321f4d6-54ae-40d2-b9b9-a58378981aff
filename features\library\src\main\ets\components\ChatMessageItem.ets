import { ChatMessage } from '../models'

// 聊天消息项组件
@Component
export struct ChatMessageItem {
  @Prop message: ChatMessage = {
    id: 0,
    uid: 0,
    username: '',
    message: '',
    date: '',
    isPublic: false
  }
  @Prop isSelf: boolean = false

  build() {
    Row() {
      Column({ space: 4 }) {
        Text(this.message.username)
          .fontSize(14)
          .fontColor(this.isSelf ? '#666666' : '#007DFF')
          .constraintSize({ maxWidth: 90 })
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .alignSelf(this.isSelf ? ItemAlign.End : ItemAlign.Start)

        Text(this.message.message)
          .fontSize(14)
          .fontColor('#000000')
          .fontColor(this.isSelf ? '#ff2f2f2f' : '#FFFFFF')
          .backgroundColor(this.isSelf ? '#ffeeeeee' : '#007DFF')
          .padding(12)
          .borderRadius(8)
      }
      .alignItems(this.isSelf ? HorizontalAlign.End : HorizontalAlign.Start)
      .width('80%')
    }
    .width('100%')
    .justifyContent(this.isSelf ? FlexAlign.End : FlexAlign.Start)
    .padding({
      left: 16,
      right: 16,
      top: 8,
      bottom: 8
    })
  }
}
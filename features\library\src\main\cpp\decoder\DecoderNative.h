#ifndef VIDEO_CODEC_SAMPLE_Decoder_NATIVE_H
#define VIDEO_CODEC_SAMPLE_Decoder_NATIVE_H

#include <js_native_api.h>
#include <js_native_api_types.h>
#include <memory>
#include <uv.h>
#include "napi/native_api.h"
#include "Decoder.h"
#include "videoDataSink.h"

class DecoderNative {
public:
    DecoderNative();
    ~DecoderNative();
    
    static napi_value Play(napi_env env, napi_callback_info info);
    static napi_value Release(napi_env env, napi_callback_info info);
    
    void SetVideoDataSink(CVideoDataSink* pSink);
    static DecoderNative* GetInstance();

private:
    static DecoderNative* instance_;
    CVideoDataSink* m_pVideoDataSink;
};

#endif // VIDEO_CODEC_SAMPLE_Decoder_NATIVE_H
import { BuilderNameConstants, RouterModule, RouterNameConstants } from "routermoudel"
import { loginApi } from "basic";
import { promptAction } from '@kit.ArkUI';
import emitter from '@ohos.events.emitter';

// 用户信息接口
interface UserInfoData {
  userId: number;
  userName: string;
  realName: string;
  nickName: string;
  roles: string;
  token: string;
  email: string;
}

@Component
struct LogoutPage {
  @StorageLink('isLoggedIn') isLoggedIn: boolean = false;
  @StorageLink('userInfo') userInfoStr: string = '';
  @State userInfo: UserInfoData | null = null;
  @State isLoggingOut: boolean = false;

  aboutToAppear() {
    this.loadUserInfo();
  }

  // 监听用户信息变化
  onPageShow() {
    console.info('[LogoutPage] 页面显示，重新加载用户信息');
    this.loadUserInfo();
  }

  aboutToDisappear() {
    // 发送导航栏重置事件
    emitter.emit('resetNavigation');
  }

  // 加载用户信息
  loadUserInfo() {
    try {
      console.info('[LogoutPage] 加载用户信息，isLoggedIn:', this.isLoggedIn, 'userInfoStr:', this.userInfoStr);
      if (this.isLoggedIn && this.userInfoStr) {
        this.userInfo = JSON.parse(this.userInfoStr);
        console.info('[LogoutPage] 解析用户信息成功:', this.userInfo);
      } else {
        this.userInfo = null;
        console.info('[LogoutPage] 用户未登录或用户信息为空');
      }
    } catch (error) {
      console.error('[LogoutPage] 解析用户信息失败:', error);
      this.userInfo = null;
    }
  }



  // 处理退出账户操作
  async handleExitAccount() {
    if (this.isLoggingOut) return;

    try {
      // 显示确认对话框
      const result = await promptAction.showDialog({
        title: '退出账户',
        message: '确定要退出当前账户吗？',
        buttons: [
          { text: '取消', color: '#666666' },
          { text: '退出', color: '#FF3B30' }
        ]
      });

      if (result.index === 1) {
        this.isLoggingOut = true;

        // 执行退出操作
        await loginApi.logout();

        // 发送登录状态变化事件
        emitter.emit('loginSuccess');

        // 返回到设置页面
        RouterModule.pop(RouterNameConstants.HomeIndex);
      }
    } catch (error) {
      console.error('退出账户失败:', error);
      promptAction.showToast({ message: '退出失败，请重试' });
    } finally {
      this.isLoggingOut = false;
    }
  }

  build() {
    NavDestination() {
      Column() {
        // 账户信息区域
        Column() {
          Row() {
            Text('账户')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Normal)

            Blank()

            Text(this.userInfo?.userName || this.userInfo?.realName || '用户名')
              .fontSize(16)
              .fontColor('#666666')
              .fontWeight(FontWeight.Normal)
          }
          .width('100%')
          .height(56)
          .padding({ left: 16, right: 16 })
          .alignItems(VerticalAlign.Center)
        }
        .backgroundColor(Color.White)
        .margin({ top: 12 })

        Blank()

        // 退出账户按钮
        Column() {
          Button() {
            Row() {
              if (this.isLoggingOut) {
                LoadingProgress()
                  .width(20)
                  .height(20)
                  .color(Color.White)
                  .margin({ right: 8 })
              }
              Text(this.isLoggingOut ? '退出中...' : '退出账户')
                .fontSize(18)
                .fontColor(Color.White)
                .fontWeight(FontWeight.Medium)
            }
            .justifyContent(FlexAlign.Center)
            .alignItems(VerticalAlign.Center)
          }
          .width('100%')
          .height(50)
          .backgroundColor('#FF3B30')
          .borderRadius(12)
          .enabled(!this.isLoggingOut)
          .onClick(() => {
            this.handleExitAccount();
          })
        }
        .width('100%')
        .padding({ left: 16, right: 16, bottom: 34 })
      }
      .width('100%')
      .height('100%')
      .backgroundColor($r('app.color.back_grand_color'))
    }
    .title('账户')
    .onBackPressed(() => {
      // 在返回前立即发送重置事件
      emitter.emit('resetNavigation');
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }
}

@Builder
export function Logout_Page(value: object) {
  LogoutPage()
}

const builderName = BuilderNameConstants.LogoutPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(Logout_Page);
  RouterModule.registerBuilder(builderName, builder);
}

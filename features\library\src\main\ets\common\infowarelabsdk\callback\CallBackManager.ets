import testNapi from 'libhstinterface.so';
import { ParticipantClass } from '../../../models';
import { BusinessType } from '../../Constant';
import { audio_registerCallback, audio_unregisterCallback } from '../audio'
import { chat_registerChatCallback, chat_unregisterChatCallback } from '../chat'
import { conference_registerCallback, conference_unregisterCallback } from '../conference'
import { confAs_registerCallback } from '../shareAs';
import { ds_registerCallback, ds_unregisterCallback } from '../shareDs';
import { video_registerCallback, video_unregisterCallback } from '../video'

export class CallBackManager {
  private static instance: CallBackManager;
  private registeredTypes: Set<BusinessType> = new Set();
  private callbacks: Map<string, Function> = new Map();
  channelIdArr: Array<number> = []
  static IS_LEAVED: boolean = true
  static IS_RECORDING: boolean = false
  static IS_FOECE_LEAVED: boolean = false
  initIsFailed: boolean = false
  httpReqIsFailed: boolean = false
  joinFailed: boolean = false

  init() {
    //testNapi.initSdk();
    console.log('init2222')
    chat_registerChatCallback();
    audio_registerCallback()
    video_registerCallback()
    conference_registerCallback()
    confAs_registerCallback()
    ds_registerCallback()


    if (this.initIsFailed) {
      testNapi.startCallbackThread();
      testNapi.initSdk();
    }

  }

  public static getInstance(): CallBackManager {
    if (!CallBackManager.instance) {
      CallBackManager.instance = new CallBackManager();
    }
    return CallBackManager.instance;
  }

  // 注册回调
  async register(businessType: BusinessType, callback: Function) {
    if (this.callbacks.has(businessType)) {
      return;
    }
    // 存储回调引用
    this.callbacks.set(businessType, callback);

  }

  // 触发回调
  trigger(businessType: BusinessType) {
    const handlers = this.callbacks.get(businessType);
    return handlers
  }

  // 卸载回调
  unregister(businessType: BusinessType) {
    this.callbacks.delete(businessType);
    this.registeredTypes.delete(businessType);
  }

  setChannelId(channelId: number) {
    this.channelIdArr.push(channelId)
  }

  getChannelId(){
    return this.channelIdArr
  }

  unregisterAllChatCallbacks() {
    audio_unregisterCallback()
    chat_unregisterChatCallback()
    video_unregisterCallback()
    conference_unregisterCallback()
    ds_unregisterCallback()

    this.initIsFailed = false
    this.joinFailed = false
    this.httpReqIsFailed = false
  }
}

export let callbackManager = new CallBackManager()
#include "DecoderNative.h"
#include "VideoData.h"
#include "common/SampleInfo.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "common/dfx/log/AVCodecSampleLog.h"
#include "encoder/Encorder.h"
#include "render/include/PluginManager.h"  // 包含PluginManager的头文件

#undef LOG_DOMAIN
#undef LOG_TAG
#define LOG_DOMAIN 0xFF00
#define LOG_TAG "Decoder"

//struct CallbackContext {
//    napi_env env = nullptr;
//    napi_ref callbackRef = nullptr;
//};

void Callback(void *asyncContext) {
    uv_loop_s *loop = nullptr;
    CallbackContext *context = (CallbackContext *)asyncContext;
    napi_get_uv_event_loop(context->env, &loop);
    uv_work_t *work = new uv_work_t;
    work->data = context;
    uv_queue_work(
        loop, work, [](uv_work_t *work) {},
        [](uv_work_t *work, int status) {
            CallbackContext *context = (CallbackContext *)work->data;
            napi_handle_scope scope = nullptr;
            // Manage the lifecycle of napi_value to prevent memory leaks.
            napi_open_handle_scope(context->env, &scope);
            napi_value callback = nullptr;
            napi_get_reference_value(context->env, context->callbackRef, &callback);
            // Callback to UI side.
            napi_call_function(context->env, nullptr, callback, 0, nullptr, nullptr);
            napi_close_handle_scope(context->env, scope);
            delete context;
            delete work;
        });
}

napi_value DecoderNative::Play(napi_env env, napi_callback_info info) {
    SampleInfo sampleInfo;
    size_t argc = 4;
    napi_value args[4] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
    
    int32_t two = 2;
    int32_t three = 3;
    napi_get_value_int32(env, args[0], &sampleInfo.inputFd);
    napi_get_value_int64(env, args[1], &sampleInfo.inputFileOffset);
    napi_get_value_int64(env, args[two], &sampleInfo.inputFileSize);

    auto asyncContext = new CallbackContext();
    asyncContext->env = env;
    napi_create_reference(env, args[three], 1, &asyncContext->callbackRef);

    sampleInfo.playDoneCallback = &Callback;
    sampleInfo.playDoneCallbackData = asyncContext;
    int32_t ret = Decoder::GetInstance().Init(sampleInfo);
    if (ret == AVCODEC_SAMPLE_ERR_OK) {
        Decoder::GetInstance().Start();
    }
    return nullptr;
}

class CDeskTopShareDataSink : public IDsDataSink {
public:
    CDeskTopShareDataSink() {
        screenShareDecoder_ = nullptr;  // 重命名 desktopShareDecoder_ 为 screenShareDecoder_
        isVideoFirstFrame_ = true;  // 为视频和屏幕共享分别添加首帧标志
        isScreenShareFirstFrame_ = true;
        env_ = nullptr;
        callbackRef_ = nullptr;
    }

    void SetCallback(napi_env env, napi_value callback) {
        if (env && callback) {
            env_ = env;
            napi_create_reference(env, callback, 1, &callbackRef_);
        }
    }

  
    
    void OnAsData(void* pData, int nDataLength, SendDataType type, int nWidth, int nHeight) override {
        if(!pData) {
            return;
        }
        
        if (!screenShareDecoder_) {
            screenShareDecoder_ = &Decoder::GetInstance();
        }

        // 第一帧屏幕共享数据时初始化解码器
        if (isScreenShareFirstFrame_) {
            if (!InitScreenShareDecoder()) {
                AVCODEC_SAMPLE_LOGE("Init screen share decoder failed");
                return;
            }
            isScreenShareFirstFrame_ = false;
        }

        // 检查屏幕共享解码器和context是否有效
        if (!screenShareDecoder_->IsScreenShareInitialized() || !screenShareDecoder_->GetScreenShareDecContext()) {
            AVCODEC_SAMPLE_LOGE("Screen share decoder not ready");
            return;
        }

        CodecUserData* codecUserData = screenShareDecoder_->GetScreenShareDecContext();
        
        try {
            uint8_t *_pData = new uint8_t[nDataLength];
            if(_pData) {
                memcpy(_pData, pData, nDataLength);
            } else {
                AVCODEC_SAMPLE_LOGE("Screen share decoder new buffer error");
                return;
            }
            
            CodecBufferInfo bufferInfo(_pData, nDataLength);

            {
                std::unique_lock<std::mutex> lock(codecUserData->inputDataMutex);            
                codecUserData->inputDataBufferInfoQueue.push(bufferInfo);
            }
            codecUserData->inputDataCond.notify_all();
        } catch (const std::exception& e) {
            AVCODEC_SAMPLE_LOGE("Exception in OnAsData: %{public}s", e.what());
        }
    }

    ~CDeskTopShareDataSink() {
        if (videoDecoder_) {
            videoDecoder_ = nullptr;
        }
        if (screenShareDecoder_) {
            screenShareDecoder_ = nullptr;
        }
    }

private:
    bool InitScreenShareDecoder() {
        SampleInfo sampleInfo;
        // 设置必要的参数
        sampleInfo.videoCodecMime = MIME_VIDEO_AVC;
        sampleInfo.videoWidth = 1920;
        sampleInfo.videoHeight = 1080;
        sampleInfo.frameRate = 30.0;
        sampleInfo.pixelFormat = AV_PIXEL_FORMAT_NV12;
        
        // 使用成员变量中保存的回调
        if (env_ && callbackRef_) {
            auto asyncContext = new CallbackContext();
            asyncContext->env = env_;
            asyncContext->callbackRef = callbackRef_;
            sampleInfo.playDoneCallback = &Callback;
            sampleInfo.playDoneCallbackData = asyncContext;
        }

        // 初始化屏幕共享解码器
        int32_t ret = screenShareDecoder_->InitScreenShare(sampleInfo);
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Screen share decoder init failed");
            return false;
        }

        // 启动屏幕共享解码器
        ret = screenShareDecoder_->StartScreenShare();
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            AVCODEC_SAMPLE_LOGE("Screen share decoder start failed");
            return false;
        }

        AVCODEC_SAMPLE_LOGI("Screen share decoder initialized successfully");
        return true;
    }

    Decoder* videoDecoder_;
    Decoder* screenShareDecoder_;
    bool isVideoFirstFrame_;
    bool isScreenShareFirstFrame_;
    napi_env env_;
    napi_ref callbackRef_;
};

static CDeskTopShareDataSink g_desktopShareSink;

// 修改Init函数，添加回调设置
EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports) {
    // 获取回调函数
    napi_value callback = nullptr;
    napi_get_named_property(env, exports, "onPlayDone", &callback);
    if (callback != nullptr) {
        g_desktopShareSink.SetCallback(env, callback);
    }
    
    registerDsDataSink(&g_desktopShareSink);
    NativeXComponentSample::PluginManager::GetInstance()->Export(env, exports);
    return exports;
}
EXTERN_C_END

static napi_module DesktopShareModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "desktopshare",
    .nm_priv = ((void *)0),
    .reserved = {0},
};

extern "C" __attribute__((constructor)) void RegisterDecoderModule(void) { napi_module_register(&DesktopShareModule); }
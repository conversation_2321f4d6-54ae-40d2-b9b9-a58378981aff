import testNapi from 'libhstinterface.so'
import { BusinessType } from '../../Constant';
import { callbackManager } from '../callback/CallBackManager';
import capture from 'libcapture.so'
import { hilog } from '@kit.PerformanceAnalysisKit';
import { screenManager } from '../../../utils';
import { window } from '@kit.ArkUI';

function as_onStartDesktop_Browser(): void {
  let handlers = callbackManager.trigger(BusinessType.AS_ON_START_DESKTOP_BROWSER)
  console.log('share_dt')
  if (handlers) {
    handlers()
  }
}

function as_onStopDesktop_Browser(): void {
  let handlers = callbackManager.trigger(BusinessType.AS_ON_STOP_DESKTOP_BROWSER)
  if (handlers) {
    handlers()
  }
}

function as_onInitialize_Browser() {

}

function as_onAschannelStateChange(nState: number): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onAschannelStateChange");
  testNapi.as_SubscribeWithMode(1);
}

function as_onRecieveData_Browser(data: ArrayBuffer, nType: number, nWidth: number, nHeight: number): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onRecieveData_Browser");
}

function as_onApplyDesktopControl(bRes: boolean): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onApplyDesktopControl");
}

function as_onApplyDesktopControlRqst(nUserId: number): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onApplyDesktopControlRqst");
}

function as_onApplyDeskTopGrantCtrlRqst(): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onApplyDeskTopGrantCtrlRqst");
}

function as_onCancleApplyDeskTopCtrlRqst(): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onCancleApplyDeskTopCtrlRqst");
}

function as_onApplyCancleCtrl(): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onApplyCancleCtrl");
}

function as_onAdmissionControl(nDragFlag: number): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onAdmissionControl");
}

function as_onAsShareFailure(nError: number): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onAsShareFailure");
}

function as_onAsShareChangeDecodemMethod(bSoft: boolean): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onAsShareChangeDecodemMethod");
}

function as_onReceiveData_Audio(data: ArrayBuffer): void {
  hilog.info(0, "hongshantong", "hongshantong callback as_onReceiveData_Audio");
}

export function addStopDesktop_BrowserCallback(callback: Function) {
  callbackManager.register(BusinessType.AS_ON_STOP_DESKTOP_BROWSER, callback)
}

export function addaStartDesktop_BrowserCallback(callback: Function) {
  callbackManager.register(BusinessType.AS_ON_START_DESKTOP_BROWSER, callback)
}

export function removeStartDesktop_BrowserCallback(callback: Function) {
  callbackManager.register(BusinessType.AS_ON_START_DESKTOP_BROWSER, callback)
}

export function removeStopDesktop_BrowserCallback(callback: Function) {
  callbackManager.register(BusinessType.AS_ON_STOP_DESKTOP_BROWSER, callback)
}


export function confAs_registerCallback() {
  testNapi.registerCallback("as_onStartDesktop_Browser", as_onStartDesktop_Browser);
  testNapi.registerCallback("as_onStopDesktop_Browser", as_onStopDesktop_Browser);
  testNapi.registerCallback("as_onInitialize_Browser", as_onInitialize_Browser);
  testNapi.registerCallback("as_onAschannelStateChange", as_onAschannelStateChange);
  testNapi.registerCallback("as_onRecieveData_Browser", as_onRecieveData_Browser);
  testNapi.registerCallback("as_onApplyDesktopControl", as_onApplyDesktopControl);
  testNapi.registerCallback("as_onApplyDesktopControlRqst", as_onApplyDesktopControlRqst);
  testNapi.registerCallback("as_onApplyDeskTopGrantCtrlRqst", as_onApplyDeskTopGrantCtrlRqst);
  testNapi.registerCallback("as_onCancleApplyDeskTopCtrlRqst", as_onCancleApplyDeskTopCtrlRqst);
  testNapi.registerCallback("as_onApplyCancleCtrl", as_onApplyCancleCtrl);
  testNapi.registerCallback("as_onAdmissionControl", as_onAdmissionControl);
  testNapi.registerCallback("as_onAsShareFailure", as_onAsShareFailure);
  testNapi.registerCallback("as_onAsShareChangeDecodemMethod", as_onAsShareChangeDecodemMethod);
  testNapi.registerCallback("as_onReceiveData_Audio", as_onReceiveData_Audio);
  console.log('share_qaqqq')

  callbackManager.initIsFailed = true
}

export function confAs_unregisterCallback() {
  testNapi.unregisterCallback("as_onStartDesktop_Browser", as_onStartDesktop_Browser);
  testNapi.unregisterCallback("as_onStopDesktop_Browser", as_onStopDesktop_Browser);
  testNapi.unregisterCallback("as_onInitialize_Browser", as_onInitialize_Browser);
  testNapi.registerCallback("as_onAschannelStateChange", as_onAschannelStateChange);
  testNapi.registerCallback("as_onRecieveData_Browser", as_onRecieveData_Browser);
  testNapi.registerCallback("as_onApplyDesktopControl", as_onApplyDesktopControl);
  testNapi.registerCallback("as_onApplyDesktopControlRqst", as_onApplyDesktopControlRqst);
  testNapi.registerCallback("as_onApplyDeskTopGrantCtrlRqst", as_onApplyDeskTopGrantCtrlRqst);
  testNapi.registerCallback("as_onCancleApplyDeskTopCtrlRqst", as_onCancleApplyDeskTopCtrlRqst);
  testNapi.registerCallback("as_onApplyCancleCtrl", as_onApplyCancleCtrl);
  testNapi.registerCallback("as_onAdmissionControl", as_onAdmissionControl);
  testNapi.registerCallback("as_onAsShareFailure", as_onAsShareFailure);
  testNapi.registerCallback("as_onAsShareChangeDecodemMethod", as_onAsShareChangeDecodemMethod);
  testNapi.registerCallback("as_onReceiveData_Audio", as_onReceiveData_Audio);
}


//桌面共享
export function StartDesktopShare(num: number): number {
  try {
    screenManager.setRotationOrientation(getContext(), window.Orientation.PORTRAIT) //竖屏
    // Register callback before starting
    registerStopCallback();

    // Initialize the native capture module
    const initResult = capture.initNative();
    if (!initResult) {
      console.error('Failed to initialize screen capture');
      return -1;
    }

    // Start desktop sharing
    return testNapi.as_StartDesktopShare(num)
  } catch (error) {
    console.error(`StartDesktopShare error: ${error}`);
    return -1;
  }
}

//停止共享
export function StopDesktopShare(): number {
  try {
    screenManager.setRotationOrientation(getContext(), window.Orientation.LANDSCAPE) //竖屏
    return testNapi.as_StopDesktopShare()
  } catch (error) {
    console.error(`StopDesktopShare error: ${error}`);
    return -1;
  }
}

// Register callback for screen sharing state changes
function registerStopCallback(): void {
  try {
    capture.registerCallback((state: number) => {
      console.info(`Screen sharing state changed: ${state}`);

      switch (state) {
        case 1:
          console.info('Screen sharing started');
          break;
        case 2:
          console.info('Screen sharing stopped');
          StopDesktopShare(); // 自动调用停止共享
          capture.release()
          break;
        case 3:
          console.info('Screen sharing interrupted');
          break;
        default:
          console.info('Unknown screen sharing state');
      }
    });
    console.info('Screen sharing callback registered successfully');
  } catch (error) {
    console.error(`Failed to register screen sharing callback: ${error}`);
  }
}

// Export callback registration if needed externally
export function RegisterStopCallback(): void {
  registerStopCallback();
}





import { BuilderNameConstants, RouterModule, RouterNameConstants } from "routermoudel"
// import { joinConf } from 'hsmeeting_hmos_sdk'
import { promptAction } from "@kit.ArkUI"
import { serverConfig, loginApi } from 'basic'

// 路由传递的会议数据接口
interface MeetingData {
  meetingId: string;
  title: string;
  time: string;
  password: string;
  startTime?: Date;
  endTime?: Date;
  status: string;
}

// 路由参数接口
interface RouteParams {
  origin?: string;
  meetingData?: MeetingData;
}

// 详情页使用的会议信息接口
interface MeetingInfo {
  meetingId: string
  displayName: string
  password: string
  title: string
  startTime: string
  endTime: string
  duration: string
  host: string
  status: string
}

// import("hsmeeting_hmos_sdk/src/main/ets/pages/MeetingPage")

@Component
struct MeetingDetail {
  // 接收路由参数
  @Prop meetingData?: MeetingData;

  // 会议信息状态
  @State meeting: MeetingInfo = {
    meetingId: '80389143',
    displayName: '',
    password: '332981',
    title: 'administas_SubscribeWithModerator的会议室',
    startTime: '2024-03-20 14:30',
    endTime: '2024-03-20 15:30',
    duration: '1小时',
    host: '测试1',
    status: ''
  };

  aboutToAppear() {
    // 如果有传递的会议数据，则使用传递的数据
    if (this.meetingData) {
      console.info('[会议详情] 接收到会议数据:', JSON.stringify(this.meetingData));
      this.initMeetingFromData(this.meetingData);
    } else {
      console.info('[会议详情] 未接收到会议数据，使用默认数据');
    }
  }

  // 获取当前用户的默认显示名
  private getCurrentUserDisplayName(): string {
    try {
      const currentUser = loginApi.getCurrentUser();

      // 如果是匿名用户，返回空字符串（不显示）
      if (currentUser.isAnonymousUser()) {
        return '';
      }

      // 优先使用真实姓名，其次是昵称，最后是用户名
      if (currentUser.realName && currentUser.realName.trim() !== '') {
        return currentUser.realName.trim();
      }

      if (currentUser.nickName && currentUser.nickName.trim() !== '') {
        return currentUser.nickName.trim();
      }

      if (currentUser.userName && currentUser.userName.trim() !== '') {
        return currentUser.userName.trim();
      }

      // 如果都没有，返回默认值
      return '';
    } catch (error) {
      console.error('获取用户显示名失败:', error);
      return '';
    }
  }

  // 根据传递的数据初始化会议信息
  private initMeetingFromData(data: MeetingData) {
    this.meeting = {
      meetingId: data.meetingId || '无会议号',
      displayName: this.getCurrentUserDisplayName(), // 使用当前用户的默认显示名
      password: data.password || '',
      title: data.title || '未命名会议',
      startTime: data.startTime ? this.formatDateTime(data.startTime) : '时间未指定',
      endTime: data.endTime ? this.formatDateTime(data.endTime) : '时间未指定',
      duration: this.calculateDuration(data.startTime, data.endTime),
      host: '主持人', // 暂时使用默认值，后续可从数据中获取
      status: data.status || ''
    };
  }

  // 格式化日期时间
  private formatDateTime(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // 计算会议时长
  private calculateDuration(startTime?: Date, endTime?: Date): string {
    if (!startTime || !endTime) {
      return '时长未知';
    }

    const diffMs = endTime.getTime() - startTime.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return diffMinutes > 0 ? `${diffHours}小时${diffMinutes}分钟` : `${diffHours}小时`;
    } else {
      return `${diffMinutes}分钟`;
    }
  }

  // 格式化显示时间（仅时:分）
  private formatTimeOnly(dateStr: string): string {
    try {
      const date = new Date(dateStr);
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    } catch (error) {
      return '00:00';
    }
  }

  // 格式化显示日期（年月日）
  private formatDateOnly(dateStr: string): string {
    try {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}年${month}月${day}日`;
    } catch (error) {
      return '日期未知';
    }
  }

  // 格式化会议号（四个数字一组）
  private formatMeetingId(meetingId: string): string {
    if (!meetingId) return '';
    // 移除所有非数字字符
    const numbers = meetingId.replace(/\D/g, '');
    // 每四个数字一组，用空格分隔
    return numbers.replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  // 生成会议链接
  private generateMeetingLink(): string {
    // 使用动态服务器地址生成会议链接
    const serverUrl = serverConfig.getServerUrl();
    if (!serverUrl) {
      return ''; // 如果没有配置服务器地址，返回空字符串
    }
    return `${serverUrl}/meeting/app/simpleJoin/index.action?confKey=${this.meeting.meetingId}&site=box`;
  }

  build() {
    NavDestination() {
      Column() {
        // 会议标题
        Row() {
          Text(this.meeting.title)
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333333')
            .textAlign(TextAlign.Start)
        }
        .width('100%')
        .padding({ left: 16, right: 16, top: 16, bottom: 16 })
        .backgroundColor('#F5F5F5')

        // 会议时间显示区域
        Column() {
          Row() {
            // 开始时间
            Column() {
              Text(this.formatTimeOnly(this.meeting.startTime))
                .fontSize(32)
                .fontWeight(FontWeight.Bold)
                .fontColor('#333333')
              Text(this.formatDateOnly(this.meeting.startTime))
                .fontSize(14)
                .fontColor('#666666')
                .margin({ top: 4 })
            }
            .alignItems(HorizontalAlign.Center)
            .layoutWeight(1)

            // 时长
            Column() {
              Text(this.meeting.duration)
                .fontSize(14)
                .fontColor('#666666')
                .backgroundColor('#E8E8E8')
                .padding({ left: 12, right: 12, top: 6, bottom: 6 })
                .borderRadius(12)
            }
            .margin({ left: 20, right: 20 })

            // 结束时间
            Column() {
              Text(this.formatTimeOnly(this.meeting.endTime))
                .fontSize(32)
                .fontWeight(FontWeight.Bold)
                .fontColor('#333333')
              Text(this.formatDateOnly(this.meeting.endTime))
                .fontSize(14)
                .fontColor('#666666')
                .margin({ top: 4 })
            }
            .alignItems(HorizontalAlign.Center)
            .layoutWeight(1)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding({ top: 24, bottom: 24, left: 16, right: 16 })
        }
        .backgroundColor(Color.White)
        .margin({ top: 8 })

        // 会议信息列表
        Column() {
          // 会议号
          this.InfoItem('会议号', this.formatMeetingId(this.meeting.meetingId))

          // 显示名输入
          Row() {
            Text('显示名')
              .fontSize(16)
              .fontColor('#333333')
            TextInput({
              text: $$this.meeting.displayName,
              placeholder: '请输入显示名'
            })
              .width(150)
              .backgroundColor('#F5F5F5')
              .border({ width: 0 })
              .borderRadius(8)
              .fontSize(16)
              .fontColor('#333333')
              .placeholderColor('#999999')
              .padding({ left: 12, right: 12, top: 8, bottom: 8 })
              .textAlign(TextAlign.End)
          }
          .width('100%')
          .padding({ left: 16, right: 16, top: 16, bottom: 16 })
          .justifyContent(FlexAlign.SpaceBetween)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#F0F0F0')
            .strokeWidth(1)

          // 会议链接
          Column() {
            Row() {
              Text('会议链接')
                .fontSize(16)
                .fontColor('#333333')
              Text('复制')
                .fontSize(16)
                .fontColor('#4285F4')
                .onClick(() => {
                  // 复制会议链接功能
                  promptAction.showToast({ message: '会议链接已复制' });
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceBetween)
            .alignItems(VerticalAlign.Center)
            .padding({ left: 16, right: 16, top: 16, bottom: 8 })

            Text(this.generateMeetingLink())
              .fontSize(14)
              .fontColor('#666666')
              .padding({ left: 16, right: 16, bottom: 16 })
              .width('100%')
              .textAlign(TextAlign.Start)
          }
        }
        .backgroundColor(Color.White)
        .margin({ top: 8 })

        Blank()

        // 底部按钮
        Button('加入会议')
          .width('calc(100% - 32vp)')
          .height(48)
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .backgroundColor('#4285F4')
          .borderRadius(24)
          .fontColor(Color.White)
          .margin({ left: 16, right: 16, bottom: 32 })
          .onClick(async () => {
            console.log('加入会议')

            try {
              const serverUrl = serverConfig.getServerUrl();

              if (!serverUrl || serverUrl.trim() === '') {
                promptAction.showToast({
                  message: '请先在设置中配置服务器地址'
                });
                return;
              }

              if (!this.meeting.displayName || this.meeting.displayName.trim() === '') {
                promptAction.showToast({
                  message: '请输入显示名'
                });
                return;
              }

              console.info(`加入会议: 会议号=${this.meeting.meetingId}, 显示名=${this.meeting.displayName}, 服务器=${serverUrl}`);

              // 调用加入会议函数
              // await joinConf(
              //   this.meeting.meetingId,      // confId - 会议号
              //   this.meeting.password,       // confPwd - 会议密码
              //   this.meeting.displayName,    // joinName - 显示名
              //   2,                          // confType - 会议类型，2表示视频会议
              //   1,                          // role - 角色，1表示参加者，0表示主持人
              //   serverUrl                   // siteUrl - 服务器地址
              // );

              promptAction.showToast({
                message: '正在加入会议...'
              });

            } catch (error) {
              console.error('加入会议失败:', error);
              promptAction.showToast({
                message: '加入会议失败，请检查网络连接'
              });
            }
          })
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F0F0F0')
    }
    .title('会议详情')
    .onBackPressed(() => {
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }

  @Builder
  InfoItem(title: string, content: string) {
    Column() {
      Row() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
        Text(content)
          .fontSize(16)
          .fontColor('#666666')
          .textAlign(TextAlign.End)
          .layoutWeight(1)
          .margin({ left: 16 })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 16 })
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)

      Divider()
        .color('#F0F0F0')
        .strokeWidth(1)
    }
  }
}

@Builder
export function MeetingDetail_Page(value: object) {
  MeetingDetail({ meetingData: (value as RouteParams)?.meetingData })
}


const builderName = BuilderNameConstants.MeetingDetail;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(MeetingDetail_Page);
  RouterModule.registerBuilder(builderName, builder);
}
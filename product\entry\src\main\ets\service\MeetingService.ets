import { Request } from '../http/request';
import { getGetConfListUrl } from '../http/BasicUrl';
import { MeetingListParams } from '../model/MeetingModel';
import { Config } from '../config/Config';

// 用户信息接口定义
interface UserInfo {
  userId: number;
  userName: string;
  realName?: string;
  nickName?: string;
  roles?: string;
  token?: string;
  email?: string;
}

// 扩展MeetingListParams接口，添加时间范围参数和confListType
interface ExtendedMeetingListParams extends MeetingListParams {
  startTime?: string;
  endTime?: string;
  confListType?: number;  // 添加confListType属性
}

// 定义查询参数接口
interface QueryParams {
  siteName: string;
  siteId: string;
  userId: string;
  userName: string;
  os: string;
  pageNum: string;
  pageSize: string;
  confListType: string;
}

// 定义会议信息接口
export interface ConferenceInfo {
  id: string;
  name: string;
  confPassword: string;
  startTime: string;
  endTime: string;
  status: string;
  confType: string;
  fromType: string;
  hostName: string;
  creatorName: string;
  hostDisplayName: string;
  hostID: string;
  creatorID: string;
  attendeeJoinUrl: string;
  needLogin: string;
  duration: string;
  token: string;
  preJoinTime: string;
  attendeeAmount: string;
  conferencePattern: string;
  hostPassword: string;
  listType: string;
  hvtype: string;
  jvtype: string;
  autoJoinType: string;
}

// 定义解析结果接口
export interface ParsedMeetingData {
  progressConfs: ConferenceInfo[];
  confs: ConferenceInfo[];
}

                                                                                          export class MeetingService {
  /**
   * 获取会议列表
   * @param pageNum 页码，默认为1
   * @param pageSize 每页显示数量，默认为20
   * @param confListType 会议列表类型，默认为1（未来7天内的会议）
   * @returns 会议列表响应
   */
  static async getConfList(pageNum: number = 1, pageSize: number = 20, confListType: number = 1): Promise<string> {
    // 使用动态服务器地址
    let fullUrl = `${getGetConfListUrl()}/meeting/remoteServlet?funcName=getConfListByCondition`;
    
    // 构建查询参数
    const queryParams: QueryParams = {
      siteName: 'box',
      siteId: 'hsmeeting',
      userId: '0',
      userName: '',
      os: 'android',
      pageNum: pageNum.toString(),
      pageSize: pageSize.toString(),
      confListType: confListType.toString()
    };
    
    // 添加参数到URL - 避免使用索引访问和in操作符
    let i = 0;
    const keys: string[] = Object.keys(queryParams);
    const values: string[] = Object.values(queryParams);
    const keysLength: number = keys.length;
    
    while (i < keysLength) {
      const key: string = keys[i];
      const value: string = values[i];
      fullUrl += `&${key}=${encodeURIComponent(value)}`;
      i++;
    }
    
    console.info('完整请求URL:', fullUrl);
    
    // 使用完整URL发送请求
    return await Request.get<string>(fullUrl);
  }
  
  /**
   * 使用正则表达式解析XML格式的会议列表
   * @param xmlData XML格式的会议数据
   * @returns 解析后的会议列表，包含进行中和未开始的会议
   */
  static parseXmlMeetingList(xmlData: string): ParsedMeetingData {
    const result: ParsedMeetingData = {
      progressConfs: [],
      confs: []
    };
    
    try {
      // 解析进行中的会议
      const progressRegex = /<progressConfs>(.*?)<\/progressConfs>/s;
      const progressMatch = xmlData.match(progressRegex);
      if (progressMatch && progressMatch[1]) {
        result.progressConfs = MeetingService.parseConferences(progressMatch[1]);
      }
      
      // 解析未开始的会议
      const confsRegex = /<confs>(.*?)<\/confs>/s;
      const confsMatch = xmlData.match(confsRegex);
      if (confsMatch && confsMatch[1]) {
        result.confs = MeetingService.parseConferences(confsMatch[1]);
      }
      
      console.info('解析会议列表成功，进行中会议数量:', result.progressConfs.length, '未开始会议数量:', result.confs.length);
    } catch (error) {
      console.error('解析XML会议列表失败:', error);
    }
    
    return result;
  }
  
  /**
   * 解析会议数据
   * @param xmlData 会议XML数据片段
   * @returns 会议信息数组
   */
  private static parseConferences(xmlData: string): ConferenceInfo[] {
    const conferences: ConferenceInfo[] = [];
    const conferenceRegex = /<conference>(.*?)<\/conference>/gs;
    
    let match: RegExpExecArray | null;
    while ((match = conferenceRegex.exec(xmlData)) !== null) {
      const conferenceXml = match[1];
      
      // 创建一个符合ConferenceInfo接口的对象
      const conference: ConferenceInfo = {
        id: '',
        name: '',
        confPassword: '',
        startTime: '',
        endTime: '',
        status: '',
        confType: '',
        fromType: '',
        hostName: '',
        creatorName: '',
        hostDisplayName: '',
        hostID: '',
        creatorID: '',
        attendeeJoinUrl: '',
        needLogin: '',
        duration: '',
        token: '',
        preJoinTime: '',
        attendeeAmount: '',
        conferencePattern: '',
        hostPassword: '',
        listType: '',
        hvtype: '',
        jvtype: '',
        autoJoinType: ''
      };
      
      // 解析会议各个字段
      const fields: string[] = [
        'id', 'name', 'confPassword', 'startTime', 'agenda', 'realityStartTime', 
        'endTime', 'status', 'confType', 'fromType', 'hostName', 'creatorName', 
        'hostDisplayName', 'hostID', 'creatorID', 'attendeeJoinUrl', 'needLogin', 
        'duration', 'token', 'preJoinTime', 'attendeeAmount', 'conferencePattern', 
        'hostPassword', 'listType', 'hvtype', 'jvtype', 'autoJoinType'
      ];
      
      for (let i = 0; i < fields.length; i++) {
        const field = fields[i];
        const fieldRegex = new RegExp(`<${field}>(.*?)<\/${field}>`, 's');
        const fieldMatch = conferenceXml.match(fieldRegex);
        
        // 只处理ConferenceInfo接口中定义的字段
        if (fieldMatch) {
          // 检查字段是否是ConferenceInfo的属性
          if (field === 'id') conference.id = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'name') conference.name = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'confPassword') conference.confPassword = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'startTime') conference.startTime = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'endTime') conference.endTime = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'status') conference.status = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'confType') conference.confType = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'fromType') conference.fromType = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'hostName') conference.hostName = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'creatorName') conference.creatorName = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'hostDisplayName') conference.hostDisplayName = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'hostID') conference.hostID = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'creatorID') conference.creatorID = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'attendeeJoinUrl') conference.attendeeJoinUrl = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'needLogin') conference.needLogin = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'duration') conference.duration = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'token') conference.token = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'preJoinTime') conference.preJoinTime = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'attendeeAmount') conference.attendeeAmount = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'conferencePattern') conference.conferencePattern = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'hostPassword') conference.hostPassword = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'listType') conference.listType = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'hvtype') conference.hvtype = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'jvtype') conference.jvtype = MeetingService.decodeXmlEntities(fieldMatch[1]);
          else if (field === 'autoJoinType') conference.autoJoinType = MeetingService.decodeXmlEntities(fieldMatch[1]);
        }
      }
      
      conferences.push(conference);
    }
    
    return conferences;
  }
  
  /**
   * 解码XML实体
   * @param text 包含XML实体的文本
   * @returns 解码后的文本
   */
  private static decodeXmlEntities(text: string): string {
    let result = text.replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");
      
    // 处理数字实体
    const numericRegex = /&#(\d+);/g;
    let numMatch: RegExpExecArray | null;
    while ((numMatch = numericRegex.exec(text)) !== null) {
      const dec = parseInt(numMatch[1], 10);
      result = result.replace(numMatch[0], String.fromCharCode(dec));
    }
    
    return result;
  }
  
  /**
   * 格式化日期为 yyyy-MM-dd HH:mm:ss
   * @param date 日期对象
   * @returns 格式化后的日期字符串
   */
  private static formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}
#include "videoDataSink.h"
#include "common/SampleInfo.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "common/dfx/log/AVCodecSampleLog.h"
#include "encoder/Encorder.h"
#include "render/include/PluginManager.h"  // 包含PluginManager的头文件
#include "render/include/PluginRender.h"

extern void Callback(void *asyncContext);

CVideoDataSink::CVideoDataSink() {
    env_ = nullptr;
    callbackRef_ = nullptr;
}
    
CVideoDataSink::~CVideoDataSink() {
    ClearAllDecoders();
}

void CVideoDataSink::SetCallback(napi_env env, napi_value callback) {
    if (env && callback) {
        env_ = env;
        napi_create_reference(env, callback, 1, &callbackRef_);
    }
}

void CVideoDataSink::OnVideoData(void* pData, int nDataLength, int nChannelId) {

    if(!pData) {
        return;
    }
    
    std::unique_lock<std::mutex> lock(decoderMutex_);
    if(!mapDecoder_[nChannelId]) {
        // 创建新的解码器实例
        mapDecoder_[nChannelId] = new Decoder();
        if(!mapDecoder_[nChannelId]) {
            return;
        }
        char szChannelId[100];
        sprintf(szChannelId, "%d", nChannelId);
        std::string strChannelId = szChannelId;
        NativeXComponentSample::PluginRender *Prender = NativeXComponentSample::PluginManager::GetInstance()->GetRender(strChannelId);
        if(Prender) {
            Prender->SetVideoDataSink(this);
        }
        
        
        if (!InitDecoder(mapDecoder_[nChannelId], nChannelId)) {
            AVCODEC_SAMPLE_LOGE("Init decoder failed for channel %{public}d", nChannelId);
            return;
        }
    }

    // 检查decoder和context是否有效
    if (!mapDecoder_[nChannelId]->IsInitialized() || 
        !mapDecoder_[nChannelId]->GetVideoDecContext()) {
        AVCODEC_SAMPLE_LOGE("Decoder not ready for channel %{public}d", nChannelId);
        return;
    }

    CodecUserData* codecUserData = mapDecoder_[nChannelId]->GetVideoDecContext();
    
    try {
        uint8_t *_pData = new uint8_t[nDataLength];
        if(_pData) {
            memcpy(_pData, pData, nDataLength);
        } else {
            AVCODEC_SAMPLE_LOGE("Decoder new buffer error");
            return;
        }
        
        CodecBufferInfo bufferInfo(_pData, nDataLength);

        {
            std::unique_lock<std::mutex> lock(codecUserData->inputDataMutex);            
            codecUserData->inputDataBufferInfoQueue.push(bufferInfo);
        }
        codecUserData->inputDataCond.notify_all();
    } catch (const std::exception& e) {
        AVCODEC_SAMPLE_LOGE("Exception in OnVideoData: %{public}s", e.what());
    }
}

void CVideoDataSink::RemoveDecoder(int nChannelId) {
    std::unique_lock<std::mutex> lock(decoderMutex_);    
    if (mapDecoder_.count(nChannelId)) {
        delete mapDecoder_[nChannelId];
        mapDecoder_.erase(nChannelId);
    }
}

void CVideoDataSink::ClearAllDecoders() {
    std::unique_lock<std::mutex> lock(decoderMutex_);    
    for (auto& pair : mapDecoder_) {
        if(pair.second) {
            delete pair.second;
        }
    }
    mapDecoder_.clear();
}

bool CVideoDataSink::InitDecoder(Decoder* decoder, int channelId) {
    SampleInfo sampleInfo;
    // 设置必要的参数
    sampleInfo.videoCodecMime = MIME_VIDEO_AVC;
    sampleInfo.videoWidth = 1920;
    sampleInfo.videoHeight = 1080;
    sampleInfo.frameRate = 30.0;
    sampleInfo.pixelFormat = AV_PIXEL_FORMAT_NV12;
    sampleInfo.channelId = channelId; // 添加通道ID
    
    // 使用成员变量中保存的回调
    if (env_ && callbackRef_) {
        auto asyncContext = new CallbackContext();
        asyncContext->env = env_;
        asyncContext->callbackRef = callbackRef_;
        sampleInfo.playDoneCallback = &Callback;
        sampleInfo.playDoneCallbackData = asyncContext;
        auto context = NativeXComponentSample::PluginManager::GetInstance();
        sampleInfo.window = context->GetWindowForChannel(channelId);
    }
    

    // 初始化解码器
    int32_t ret = decoder->Init(sampleInfo);
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGE("Decoder init failed");
        return false;
    }

    // 启动解码器
    ret = decoder->Start();
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        AVCODEC_SAMPLE_LOGE("Decoder start failed");
        return false;
    }

    AVCODEC_SAMPLE_LOGI("Decoder initialized successfully for channel %{public}d", channelId);
    return true;
}
# 匿名登录功能实现说明

## 修改内容

### 1. 登录页面改进 (`features/settings/src/main/ets/components/LoginPage.ets`)

- **添加了`handleAnonymousLogin`方法**：
  - 创建匿名用户信息，包括特殊的用户ID(-1)和用户名('anonymous_user')
  - 生成临时token以标识匿名会话
  - 保存匿名用户信息到UserInfo单例
  - 发送登录成功事件
  - 清空路由栈并跳转到首页

- **匿名用户信息结构**：
  ```typescript
  {
    userId: -1,                           // 特殊ID标识匿名用户
    userName: 'anonymous_user',           // 匿名用户标识
    realName: '匿名用户',                 // 显示名称
    nickName: '匿名用户',                 // 昵称
    roles: 'guest',                       // 访客角色
    token: 'anonymous_token_' + Date.now(), // 临时token
    email: ''                             // 空邮箱
  }
  ```

### 2. 主页登录状态检查改进 (`product/entry/src/main/ets/pages/Index.ets`)

- **修改了用户ID验证逻辑**：
  - 原来：`currentUser.userId > 0`
  - 现在：`currentUser.userId > 0 || currentUser.userId === -1`
  - 支持匿名用户的特殊ID(-1)

- **添加了匿名用户检查**：
  - 检查`userId === -1`和`userName === 'anonymous_user'`
  - 在登录状态判断中包含匿名用户检查

- **改进了登录状态判断逻辑**：
  - 有效token OR 有效用户ID OR 有效用户名 OR 匿名用户 = 显示主页
  - 增强了日志输出，便于调试

### 3. 用户信息类改进 (`commons/basic/src/main/ets/api/LoginApi.ets`)

- **改进了`isLoggedIn()`方法**：
  - 原来：只检查token是否为空
  - 现在：检查token不为空且userId不为-1（排除匿名用户）

- **添加了新的用户状态检查方法**：
  - `isAnonymousUser()`：检查是否为匿名用户
  - `isValidUser()`：检查是否为有效用户（包括匿名用户）

### 4. 主页权限控制改进 (`product/entry/src/main/ets/views/MainHome.ets`)

- **改进了`handleLoginRequiredAction`方法**：
  - 区分正式登录用户、匿名用户和未登录用户
  - 为不同用户类型显示不同的提示信息
  - 只有正式登录用户才能执行创建会议和预约会议操作

### 5. 兼容性处理

- **保持了原有的登录逻辑**：正常用户登录流程不受影响
- **向后兼容**：现有的用户数据和登录状态检查仍然有效
- **简单登录页面**：`product/entry`中的登录页面也添加了注释说明
- **权限分级**：匿名用户可以查看首页和加入会议，但不能创建或预约会议

## 功能流程

### 匿名登录流程：
1. 用户点击"匿名登录"按钮
2. 调用`handleAnonymousLogin()`方法
3. 创建匿名用户信息并保存到UserInfo
4. 发送登录成功事件
5. 清空路由栈并跳转到首页
6. 主页检查登录状态，识别为有效的匿名用户
7. 显示主页内容

### 登录状态检查流程：
1. 获取当前用户信息
2. 检查是否为默认用户（所有字段为空/0）
3. 检查是否有有效的token、用户ID或用户名
4. 特别检查是否为匿名用户（userId=-1, userName='anonymous_user'）
5. 如果满足任一条件，显示主页；否则跳转到登录页面

## 测试步骤

1. **启动应用**：应用启动时会检查登录状态
2. **进入登录页面**：如果未登录，会自动跳转到登录页面
3. **点击匿名登录**：点击"匿名登录"按钮
4. **验证跳转**：应该看到"匿名登录成功"提示，然后跳转到首页
5. **验证状态**：首页应该正常显示，不会再次跳转到登录页面
6. **重启测试**：重启应用，检查匿名登录状态是否保持

## 用户权限分级

### 匿名用户权限：
- ✅ 查看首页和会议列表
- ✅ 加入会议（如果服务器支持）
- ✅ 查看设置页面
- ❌ 创建会议
- ❌ 预约会议
- ❌ 修改服务器设置（需要正式登录）

### 正式登录用户权限：
- ✅ 所有匿名用户权限
- ✅ 创建会议
- ✅ 预约会议
- ✅ 修改个人设置
- ✅ 查看个人会议历史

## 注意事项

- **匿名用户限制**：匿名用户在创建会议、预约会议等功能上有限制
- **会话管理**：匿名用户的token是临时生成的，重启应用后需要重新匿名登录
- **数据持久化**：匿名用户信息会保存在UserInfo中，但不会持久化到服务器
- **安全考虑**：匿名用户只能访问基本功能，敏感操作需要正式登录
- **用户体验**：匿名用户尝试执行受限操作时会看到友好的提示信息

## 预期结果

修改后，用户点击匿名登录应该能够：
1. 成功跳转到首页
2. 在首页看到会议列表和基本功能
3. 能够加入会议（如果服务器支持匿名用户）
4. 不会被重定向回登录页面

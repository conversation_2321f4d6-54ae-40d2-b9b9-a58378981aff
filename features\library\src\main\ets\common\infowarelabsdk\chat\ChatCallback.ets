import { callback<PERSON>ana<PERSON>, CallBackManager } from '../callback/CallBackManager'
import { MessageBean } from "../confctrl/domain/MessageBean";
import testNapi from 'libhstinterface.so';
import { ChatMessage, MessageModel } from '../../model';
import { BusinessType } from '../../Constant';

export function getCurrentUserName(): string {
  return testNapi.getCurrentUserName()
}

export type ReceiveCallbackFun = (params: MessageModel) => void;

export type ReceivePermissionCallbackFun = (params: number) => void;

let onReceiveDataCallbacks: ReceiveCallbackFun[] = [];
let onReceivePermissionCallbacks: ReceivePermissionCallbackFun[] = [];


function handleChannelReady() {

}


function handleReceiveData(nSrcId: number, strSrcName: string, strMsg: string, bPublic: boolean) {
  let message: MessageModel = {
    uid: nSrcId,
    username: strSrcName,
    message: strMsg,
    isPublic: bPublic
  }
  try {
    //onReceiveDataCallbacks.forEach((callbackFn) => callbackFn(message))
    let handlers = callbackManager.trigger(BusinessType.CHAT_ON_RECEIVE_DATA)
    if (handlers) {
      handlers(message)
    }
  } catch (e) {
    console.log('listen=== error', JSON.stringify(e))
  }
}

function handleReceivePermission(nState: number) {
  console.log('check chat permission ----', nState)
  //onReceivePermissionCallbacks.forEach((callbackFn) => callbackFn(nState))
  let handlers = callbackManager.trigger(BusinessType.CHAT_ON_RECEIVE_PERMISSION)
  if (handlers) {
    handlers(nState)
  }
}

export const addReceiveDataCallback = (callback: ReceiveCallbackFun) => {
  try {
    callbackManager.register(BusinessType.CHAT_ON_RECEIVE_DATA, callback)
    //onReceiveDataCallbacks.push(callback)
  } catch (e) {
    console.log('chat msg error ++ ' + JSON.stringify(e))
  }
}

export const addReceiveDataPermissionCallback = (callback: ReceivePermissionCallbackFun) => {
  callbackManager.register(BusinessType.CHAT_ON_RECEIVE_PERMISSION, callback)
  //onReceivePermissionCallbacks.push(callback)
  //callbackManager.register(BusinessType.CHAT_ON_RECEIVE_DATA,callback)
}

export const removeReceiveDataCallback = (callback: ReceiveCallbackFun) => {
  //onReceiveDataCallbacks = onReceiveDataCallbacks.filter(i => i !== callback)
  callbackManager.unregister(BusinessType.CHAT_ON_RECEIVE_DATA)
}

export const removeReceivePermissionCallback = (callback: ReceivePermissionCallbackFun) => {
  //onReceivePermissionCallbacks = onReceivePermissionCallbacks.filter(i => i !== callback)
  callbackManager.unregister(BusinessType.CHAT_ON_RECEIVE_PERMISSION)
}


// 1. register event by api
// 2. api emit join event
// 3. register join callback -> event not emit -> not join
// 3.1 add -> callfun zhixing

export function chat_registerChatCallback() {
  console.log('initeeeee')
  testNapi.registerCallback("chat_onChannelReady", handleChannelReady);
  console.log('init2222chat')
  testNapi.registerCallback("chat_onReceiveData", handleReceiveData);
  testNapi.registerCallback("chat_onReceivePermission", handleReceivePermission);
  callbackManager.initIsFailed = true
  // testNapi.registerCallback("chat_onJoin", handleJoin)
}

export function chat_unregisterChatCallback() {
  testNapi.unregisterCallback("chat_onChannelReady", handleChannelReady);
  testNapi.unregisterCallback("chat_onReceiveData", handleReceiveData);
  testNapi.unregisterCallback("chat_onReceivePermission", handleReceivePermission);
  onReceiveDataCallbacks = [];
  onReceivePermissionCallbacks = []
}


// registerCallback (join, (userName, userId) => void)


import router from '@ohos.router'
import promptAction from '@ohos.promptAction'
import { ChatMessage, MeetingConfig, Participant, ParticipantClass } from '../models'
import {
  ChatView,
  DocSharingView,
  InfoView,
  ParticipantsView,
  SettingView,
  VideoPreviewView
} from '../views'
import { NavBar, SettingBar, WhiteBoardBar } from '../components'
import { MeetingReqModel, MessageModel } from '../common/model'
import { BusinessError, emitter } from '@kit.BasicServicesKit'
import { callbackManager, } from '../common/infowarelabsdk/callback/CallBackManager'
import {
  addLeaveConferenceCallback,
  addRosterInfoUpdateCallback,
  addUserHandUpCallback,
  addUserRoleUpdateCallback,
  HandUp,
  JoinConference,
  LeaveConference,
  removeLeaveConferenceCallback,
  removeRosterInfoUpdateCallback,
  removeUserHandUpCallback,
  removeUserRoleUpdateCallback
} from '../common/infowarelabsdk/conference'
import { webview } from '@kit.ArkWeb'
import { abilityAccessCtrl, common } from '@kit.AbilityKit'
import {
  addAudioStatusChangeCallback,
  OpenAudio,
  removeAudioStatusChangeCallback
} from '../common/infowarelabsdk/audio'
import { screenManager } from '../utils'
import {
  addReceiveDataCallback,
  addReceiveDataPermissionCallback,
  removeReceiveDataCallback,
  removeReceivePermissionCallback
} from '../common/infowarelabsdk/chat'
import {
  addOpenVideoCallback,
  addVideoDeviceUpdateCallback,
  changeOpenVideo,
  removeOpenVideoCallback,
  removeVideoDeviceUpdateCallback
} from '../common/infowarelabsdk/video'
import { JoinHome } from '../common/api/HongSongJoinApi'
import { BackgroundUtil } from '../utils/BackgroundUtil'
import { audioStateChange, receiveMessage, userRole } from '../utils/DataUtil'
import { parseNameValuePairs } from '../utils/XmlManager'
import {
  addaStartDesktop_BrowserCallback,
  addStopDesktop_BrowserCallback,
  removeStartDesktop_BrowserCallback,
  removeStopDesktop_BrowserCallback
} from '../common/infowarelabsdk/shareAs'
import testNapi from 'libhstinterface.so'
import capture from 'libcapture.so'
import { ShareWebViewComponent } from '../components/ShareWebViewComponent'
import { VideoBit } from './VideoView'

interface MeetingModel {
  config: MeetingConfig,
  userinfo: MeetingReqModel,
  xmlData: string
}

let click: number = 1

@Entry({ routeName: 'hss_meeting_MeetingPage' })
@Component
struct MeetingPage {
  //当前用户信息
  @State user: Participant = {
    uid: 10001,
    nickname: '韩震',
    role: 0,
    audioOpen: false,
    videoOpen: false,
    handUp: false,
    channelIds: []
  }
  @State userInfo: Partial<MeetingReqModel> = {}
  // 添加会议配置属性
  @State title: string = '视频'
  @State rotation: number = 0 // 旋转角度 (0, 2, 竖屏) (1, 3, 横屏)
  @State meetingConfig: Partial<MeetingConfig> = {}
  @State showChatPage: boolean = false //是否显示聊天页面
  @State showNavBar: boolean = true //显示顶部导航栏
  @State showSettingBar: boolean = true //显示底部工具栏
  @State showWhiteBoardBar: boolean = false //是否显示白板设置
  @State @Watch('closeSettingBar') currentPageIndex: number = 0 // 当前页面索引
  @State topHeight: number = 0 //顶部安全区高度
  @State bottomHeight: number = 0 //底部安全区高度
  @State currentChatTarget: ParticipantClass | undefined = undefined // 当前聊天对象，明确初始化为undefined
  @State participants: ParticipantClass[] = []
  @State messages: Array<ChatMessage> = [] //聊天消息列表
  @Provide cameraIndex: number = 1 // 当前摄像头索引
  @State shareLink: string | undefined = ''
  @State canShowShare: boolean = false
  @State isPublicChatHaveMessage: boolean = false
  @State clickfn: number = 3
  @State timer: number = 0
  //分享选择弹窗倒计时ID
  closeSettingTimerId: number = -1
  private context: common.UIAbilityContext | undefined = AppStorage.get('context')

  receivePermissionCallback(data: number) {
    console.log('check chat permission ++++', data)
  }

  aboutToAppear(): void {
    let config = router.getParams() as MeetingModel

    let retJoin = JoinConference(config.xmlData)
    //如果加入会议成功开启长时
    if (retJoin === 0) {
      BackgroundUtil.startContinuousTask(this.context)
    }

    this.meetingConfig = config.config
    this.userInfo = config.userinfo
    if (config.config.InviteUrl) {
      this.shareLink = config.config.InviteUrl
    }

    //注册回调
    this.registerCallbacks()
    //设置沉浸式
    this.setScreenSafe()

    webview.WebviewController.setWebDebuggingAccess(true);

    let atManager = abilityAccessCtrl.createAtManager();

    atManager.requestPermissionsFromUser(getContext(this), ['ohos.permission.CAMERA', 'ohos.permission.MICROPHONE'])
      .then((data) => {
        console.info('data:' + JSON.stringify(data));
        console.info('data permissions:' + data.permissions);
        console.info('data authResults:' + data.authResults);
      })
      .catch((error: BusinessError) => {
        console.error(`Failed to request permissions from user. Code is ${error.code}, message is ${error.message}`);
      });

    // 初始化用户信息
    this.initUserInfo()

    // 设置开启屏幕旋转
    screenManager.setWindowRotation(getContext(this), (rotation: number) => {
      this.rotation = rotation
    })
    //进入关闭设置栏倒计时
    this.closeSettingBar()

    //设置不息屏
    screenManager.setWindowKeepScreenOn(getContext(this), true)

    AppStorage.setOrCreate('hss_video_storage', {
      bitRate: 512000,
      width: 1280,
      height: 720,
    } as VideoBit)
  }

  aboutToDisappear(): void {
    try {
      //todo:取消注册回调 - TypeError: is not callable
      callbackManager.unregisterAllChatCallbacks()
    } catch (E) {
      console.log('qq2' + E)
    }
    //关闭沉浸式
    this.unSetScreenSafe()
    //卸载回调
    this.removeCallbacks()
    //取消屏幕旋转
    screenManager.unSetWindowRotation(getContext(this))
    //关闭设置不息屏
    screenManager.setWindowKeepScreenOn(getContext(this), false)

    clearTimeout(this.timer)
  }

  initUserInfo() {
    this.user.videoOpen = this.meetingConfig.openVideo
    this.user.audioOpen = this.meetingConfig.openAudio
    this.user.nickname = this.meetingConfig.displayName
  }

  registerCallbacks() {
    addRosterInfoUpdateCallback(this.userRoleCallback) //用户人员流动监听
    addAudioStatusChangeCallback(this.userAudioStatusChange) //监听用户音频状态
    addReceiveDataPermissionCallback(this.receivePermissionCallback)
    addVideoDeviceUpdateCallback(this.onDeviceUpdate) //获取用户设备刷新
    addReceiveDataCallback(this.receiveMessageCallback) //接收消息回调
    addaStartDesktop_BrowserCallback(this.addShare)
    addStopDesktop_BrowserCallback(this.removeShare)
    addUserHandUpCallback(this.userHandUp) //举手回调
    addOpenVideoCallback(this.opeVideoCallback)
    addLeaveConferenceCallback(this.leaveConferenceCallback)
    addUserRoleUpdateCallback(this.userRoleUpdate)
  }

  removeCallbacks() {
    removeRosterInfoUpdateCallback(this.userRoleCallback)
    removeAudioStatusChangeCallback(this.userAudioStatusChange)
    removeReceivePermissionCallback(this.receivePermissionCallback)
    removeVideoDeviceUpdateCallback(this.onDeviceUpdate)
    removeReceiveDataCallback(this.receiveMessageCallback)
    removeStartDesktop_BrowserCallback(this.addShare)
    removeStopDesktop_BrowserCallback(this.removeShare)
    removeUserHandUpCallback()
    removeOpenVideoCallback()
    removeLeaveConferenceCallback()
    removeUserRoleUpdateCallback()
  }

  leaveConferenceCallback = () => {
    //退出会议成功，关闭长时
    console.log('退出会议')
    capture.release()
    if (this.context) {
      BackgroundUtil.stopContinuousTask(this.context)
    }
    router.back()
    console.log('退出卸载')
    testNapi.unRegisterCallback();
    testNapi.stopCallbackThread();
    testNapi.unInitSdk();
  }
  userHandUp = (nUserId: number, bHandUp: boolean) => {
    console.log('kkkk userHandUp qqqqqq', nUserId, bHandUp)
    const index = this.participants.findIndex(item => item.uid === nUserId)
    this.participants[index].handUp = bHandUp
  }
  opeVideoCallback = (isOpen: boolean) => {
    if (isOpen) {
      //开启视频
      console.log('开启视频')
      promptAction.showToast({ message: '本地摄像头已打开' })
      this.user.videoOpen = true
      if (this.user.channelId) {
        changeOpenVideo(this.user.channelId, true)
      }
    } else {
      //关闭视频
      console.log('关闭视频')
      promptAction.showToast({ message: '本地摄像头已关闭' })
      this.user.videoOpen = false
      try {
        if (this.user.channelId) {
          changeOpenVideo(this.user.channelId, false)
        }
      } catch (e) {
        console.error('openVideo', e)
      }
    }

  }
  @State isOpenShare: boolean = false
  addShare = () => {
    console.log('share 开启共享')
    this.isOpenShare = true
  }
  removeShare = () => {
    console.log('share 关闭共享')
    this.isOpenShare = false
  }
  onDeviceUpdate =
    (nUserId: number, nChannelId: number, bRemove: boolean, strName: string, nCodeType: number, userId?: number) => {
      if (userId) {
        console.warn('kkk 我自己的id', userId)
        this.user.uid = userId
        this.user.channelId = nChannelId
        //排序
        const index = this.participants.findIndex(item => item.uid === userId)
        if (index !== -1) {
          const user = this.participants[index]
          this.user.role = user.role
          this.participants.splice(index, 1)
          const i = this.participants.findIndex(item => item.role !== 1)
          this.participants.splice(i, 0, user)
        }
      }
    }

  // 显示退出对话框
  private async showExitDialog() {
    const data = await promptAction.showDialog({
      title: '确定要退出会议吗?',
      buttons: [
        { text: '确定', color: '#007DFF' },
        { text: '取消', color: '#007DFF' }
      ]
    })
    if (data.index === 0) {
      //LeaveConference(0)
      this.exitErrFn()
    }
  }

  //返回操作
  onBackPress(): boolean | void {
    console.log('退出页面操作')
    //用户滑动返回时仅退出聊天页面
    if (this.showChatPage) {
      this.showChatPage = false
      //返回到参会者页面
      this.currentPageIndex = 2
      return true
    }
    this.showExitDialog()
    return true
  }

  //倒计时关闭设置栏
  closeSettingBar() {
    clearTimeout(this.closeSettingTimerId)
    if (this.currentPageIndex === 0) {
      this.closeSettingTimerId = setTimeout(() => {
        this.showSettingBar = false
        this.showNavBar = false
      }, 5000)
    } else {
      this.showSettingBar = true
      this.showNavBar = true
    }
  }

  build() {
    RelativeContainer() {
      // 主体内容区
      Stack({ alignContent: Alignment.Center }) {

        // 视频预览页
        // if (this.currentPageIndex === 0) {
        VideoPreviewView({
          userData: this.user,
          rotation: this.rotation,
          participants: this.participants,
          onVideoPreviewClick: () => {
            if (this.showSettingBar) {
              this.showSettingBar = false
              this.showNavBar = false
            } else {
              this.showSettingBar = true
              this.showNavBar = true
              this.closeSettingBar()
            }
          },
          videoOpenClick: (index: number, isOpen: boolean) => {
            if (index !== -1) {
              this.participants[index].videoOpen = isOpen
            }
          }
        })
          .zIndex(this.currentPageIndex === 0 ? 3 : 1)
        // }

        // 文档共享页
        // if (this.currentPageIndex === 1) {
        //不销毁，只是被其他组件覆盖
        DocSharingView({
          rotation: this.rotation,
          isOpenSetting: this.showWhiteBoardBar,
          isOpenShare: this.isOpenShare,
          userData: this.user,
          participants: this.participants,
          openSettingBarClick: (isOpen: boolean) => {
            if (isOpen) {
              //开启
              this.showWhiteBoardBar = true
              this.showSettingBar = false
            } else {
              //关闭
              this.showWhiteBoardBar = false
              this.showSettingBar = true
            }
          }
        })
          .zIndex(2)
        // }


        // 参会者列表页
        if (this.currentPageIndex === 2) {
          ParticipantsView({
            userInfo: this.user,
            participants: this.participants,
            currentParticipant: this.currentChatTarget,
            isPublicChatHaveMessage: this.isPublicChatHaveMessage,
            onChatClick: (index, participant) => {
              //index->0开启/静音， 1打开/关闭视频， 2设为/取消主持人， 3私/公聊， 4移出会议
              if (index === 0) {
                if (participant?.uid === this.user.uid) {
                  this.audioClick()
                }
              }
              if (index === 1) {
                if (participant?.uid === this.user.uid) {
                  this.videoClick()
                }
              }
              if (index === 3) {
                //打开聊天页面
                this.currentChatTarget = participant
                this.showChatPage = true
                //消息已读
                if (participant) {
                  const index = this.participants.findIndex(item => item.uid === participant?.uid)
                  if (index !== -1) {
                    this.participants[index].isReadedMsg = true
                  }
                } else {
                  this.isPublicChatHaveMessage = false
                }
              }
              if (index === 4) {
                //todo:踢出会议
                console.log('移出会议' + participant?.nickname)
              }
            }
          })
            .zIndex(4)
        }

        // 信息页面
        if (this.currentPageIndex === 3) {
          InfoView({ participants: this.participants }).zIndex(4)
        }

        //设置页面
        if (this.currentPageIndex === 4) {
          SettingView({
            userData: this.user,
            nickNameChange: (name: string) => {
              this.user.nickname = name
              const index = this.participants.findIndex(item => item.uid === this.user.uid)
              if (index !== -1) {
                this.participants[index].nickname = name
              }
            }
          }).zIndex(4)
        }

      }
      .width('100%')
      .height('100%')
      .backgroundColor('#1A1B1F')
      .padding({
        top: this.currentPageIndex === 0 ? (this.showNavBar ? 48 : 0) : 48,
        bottom: this.currentPageIndex === 0 ? (this.showNavBar ? 64 : 0) : 64
      })
      .animation({ duration: 200 })


      // 顶部导航栏
      NavBar({
        title: this.title,
        leftText: this.currentPageIndex === 0 ? '' : '返回视频',
        rightText: '退出',
        isShowBar: this.showNavBar,
        currentPageIndex: this.currentPageIndex,
        leftClick: () => {
          //返回视频
          this.currentPageIndex = 0
          if (this.showWhiteBoardBar) {
            this.showWhiteBoardBar = false
            this.showSettingBar = true
          }
        },
        rightClick: () => {
          this.showExitDialog()
        },
        leftIcon: (): void => {
          this.cameraChange()
        },
        shareClick: (): void => {
          //this.canShowShare = true
          emitter.emit('inviteAttendee', { data: {} })
        },
        leftIconClick: () => {
          this.cameraIndex = this.cameraIndex === 1 ? 0 : 1
        },
      })
      //显示分享webView
      if (this.canShowShare) {
        ShareWebViewComponent({
          shareUrl: this.shareLink,
          CloseClick: () => {
            this.canShowShare = false
          }
        })
      }
      //底部设置功能栏
      SettingBar({
        userData: this.user,
        isShowBar: this.showSettingBar,
        currentPageIndex: this.currentPageIndex,
        //点击语音
        audioClick: () => {
          this.audioClick()
        },
        //点击视频
        videoClick: () => {
          if (this.currentPageIndex !== 0) {
            this.currentPageIndex = 0
            this.title = '视频'
            return
          }
          this.closeSettingBar()
          this.videoClick()
        },
        //点击分享导航
        shareClick: () => {
          //点击触发弹窗开启emitter
          emitter.emit('hss_share_dialog_open', { data: { index: this.currentPageIndex } })
          if (this.currentPageIndex !== 1) {
            this.currentPageIndex = 1
            this.title = '文档共享'
          }
        },
        //点击参加者导航
        participantClick: () => {
          if (this.currentPageIndex !== 2) {
            this.currentPageIndex = 2
            this.title = '参会者'
          }
        },
        infoClick: () => {
          //点击 更多-信息，将信息组件渲染到当前页面
          promptAction.showToast({ message: '信息' })
          console.log('点击了信息')
          this.currentPageIndex = 3
          this.title = '信息'
        },
        settingClick: () => {
          console.log('点击了设置')
          this.currentPageIndex = 4
          this.title = '设置'
        },
        chatClick: () => {
          //打开聊天页面
          this.showChatPage = true
          this.currentChatTarget = undefined
        },
        handUpClick: () => {
          //举手
          console.log('举手')
          if (this.user.uid) {
            HandUp(this.user.uid, true)
            const index = this.participants.findIndex(item => item.uid === this.user.uid)
            this.participants[index].handUp = true
          } else {
            promptAction.showToast({
              message: '未获取到uid'
            })
          }
        },
        recordClick: () => {
          this.participants[0].nickname = '张三'
        }
      })

      WhiteBoardBar({
        rotation: this.rotation,
        isShow: this.showWhiteBoardBar,
      })

      // 聊天页面 - 使用全屏覆盖显示
      if (this.showChatPage) {
        ChatView({
          UserInfo: this.user,
          currentChatTarget: this.currentChatTarget,
          messages: this.messages,
          onBack: () => {
            this.showChatPage = false
            //返回到参会者页面
            this.currentPageIndex = 2
          }
        })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#1A1B1F')
    .padding({
      top: this.topHeight,
      bottom: this.bottomHeight
    })

  }

  @Builder
  cameraChange() {
    if (this.user.videoOpen && this.currentPageIndex === 0) {
      Image($r('app.media.camera_rotate_on'))
        .height('100%')
    }
  }

  //本地点击音频关闭
  audioClick() {
    if (this.user.audioOpen) {
      if (!OpenAudio(0, 0)) {
        //关闭音频
        console.log('关闭音频')
        promptAction.showToast({ message: '音频已关闭' })
        this.user.audioOpen = false
        this.closeSettingBar()
      }
    } else {
      if (!OpenAudio(1, 0)) {
        //开启音频
        console.log('开启音频')
        promptAction.showToast({ message: '音频已打开' })
        this.user.audioOpen = true
        this.closeSettingBar()
      }
    }
  }

  //本地点击视频关闭
  videoClick() {
    if (this.user.videoOpen) {
      //关闭视频
      console.log('关闭视频')
      promptAction.showToast({ message: '本地摄像头已关闭' })
      this.user.videoOpen = false
      try {
        if (this.user.channelId) {
          changeOpenVideo(this.user.channelId, false)
        }
      } catch (e) {
        console.error('openVideo', e)
      }

    } else {
      //开启视频
      console.log('开启视频')
      promptAction.showToast({ message: '本地摄像头已打开' })
      this.user.videoOpen = true
      if (this.user.channelId) {
        changeOpenVideo(this.user.channelId, true)
      }
    }
  }

  //设置屏幕安全区
  private async setScreenSafe() {
    const context = getContext(this)
    await screenManager.full(context)
    this.topHeight = await screenManager.getTopHeight(context)
    this.bottomHeight = await screenManager.getBottomHeight(context)
  }

  //退出全屏
  private async unSetScreenSafe() {
    await screenManager.exitFull(getContext(this))
  }

  //音频状态监听修改
  userAudioStatusChange = (nUserId: number, bHaveDev: boolean, bOpen: boolean) => {
    audioStateChange(this.participants, nUserId, bOpen)
    if (this.user.uid === nUserId) {
      this.user.audioOpen = bOpen
    }
  }
  //参会者人员流动监听
  userRoleCallback = (user: Participant) => {
    userRole(this.participants, user) //对user内数据进行一些初始化
  }
  //接收聊天消息
  receiveMessageCallback = (data: MessageModel) => {
    receiveMessage(this.messages, data, this.showChatPage, this.currentChatTarget, this.participants)
    if (data.isPublic && (!this.showChatPage || this.currentChatTarget !== undefined)) {
      this.isPublicChatHaveMessage = true
    }
  }
  userRoleUpdate = (uid: number, role: number) => {
    console.log('kkk 用户身份变动', uid, role)
    const index = this.participants.findIndex(item => item.uid === uid)
    if (index === -1) {
      return
    }
    this.participants[index].role = role
    if (this.participants[index].uid === this.user.uid) {
      this.user.role = role
    }
    if (role === 1) {
      const user = this.participants[index]
      this.participants.splice(index, 1)
      this.participants.unshift(user)
    }
  }

  //退出报错强制退出
  exitErrFn() {
    if (this.clickfn < 3) {
      return
    }
    LeaveConference(0)
    this.timer = setTimeout(() => {
      this.clickfn--
      if (this.clickfn === 0) {
        this.clickfn = 3
        clearTimeout(this.timer)
        router.back()
        promptAction.showToast({ message: '服务链接超时' })
      }
    }, 1000)
  }
}

// 修改加入会议接口
export async function joinConf(confId: string, confPwd: string, joinName: string, confType: number, role: number,
  siteUrl: string, InviteUrl?: string) {
  click++
  if (click % 2) {
    return
  }
  // 创建新的配置对象
  const config: MeetingConfig = {
    meetingId: confId,
    displayName: joinName,
    password: confPwd,
    confType: confType,
    role: role,
    siteUrl: siteUrl,
    // 保持其他默认值
    openAudio: false,
    openVideo: false,
    useSSL: false,
    InviteUrl: InviteUrl ? InviteUrl : ''
  }
  // 验证必需参数
  if (!config.meetingId || !config.displayName) {
    promptAction.showToast({
      message: '会议号和显示名称不能为空'
    })
    return
  }
  let data: MeetingReqModel = {
    userId: 0,
    siteId: "1",
    userName: joinName,
    email: '',
    confId: confId,
    confType: 302,
    serviceType: 24,
    confPassword: confPwd,
    os: 17,
    token: '',
    confSiteName: ''
  }
  siteUrl += '/meeting/remoteServlet?funcName=joinConf&'
  callbackManager.init()
  try {
    const res = await JoinHome(data, siteUrl)
    const xmlObj = parseNameValuePairs(res)
    if (xmlObj.return !== '0') {
      promptAction.showToast({ message: '会议号输入错误' })
      return
    }
    //onJoinConference为加入会议成功的回调
    if (callbackManager.initIsFailed && callbackManager.httpReqIsFailed) {
      router.pushNamedRoute({
        name: 'hss_meeting_MeetingPage',
        params: {
          'config': config,
          'userinfo': data,
          'xmlData': res,
          'xmlObj': xmlObj
        }
      })
      let timer = setTimeout(() => {
        clearTimeout(timer)
        click = 1
      }, 1000)
    } else {
      promptAction.showToast({ message: '加入会议失败' })
    }
  } catch (error) {
    console.error('eee +++' + JSON.stringify(error))
    promptAction.showToast({ message: JSON.stringify(error) })
  }
}

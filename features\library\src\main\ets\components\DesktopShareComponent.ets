import desktopshare from 'libdesktopshare.so'
@Component
export struct DesktopShareComponent {
  // 可选的外部传入surfaceId
  @Prop surfaceId?: string

  // 组件宽度和高度，支持外部配置，使用Length类型
  @State private componentWidth: Length = '100%'
  @State private componentHeight: Length = '100%'

  private innerSurfaceId: string = ''

  aboutToAppear() {
    // 如果没有外部传入surfaceId，则生成一个唯一ID
    if (!this.surfaceId) {
      this.innerSurfaceId = `surface_${Date.now()}_${Math.random().toString(36).slice(2)}`
    } else {
      this.innerSurfaceId = this.surfaceId
    }
  }

  build() {
    Column() {
      XComponent({
        id: this.innerSurfaceId,
        type: XComponentType.SURFACE,
        libraryname: 'desktopshare'
      })
        // .width(this.componentWidth)
        // .height(this.componentHeight)
        .constraintSize({ maxWidth: '100%', maxHeight: '100%' })
        .aspectRatio(16 / 9)
        .onLoad(() => {
          // Surface创建完成后的回调
          console.info(`Surface created with id: ${this.innerSurfaceId}`)
        })
        .onDestroy(() => {
          // Surface销毁时的回调
          console.info(`Surface destroyed: ${this.innerSurfaceId}`)
        })
    }
  }
}

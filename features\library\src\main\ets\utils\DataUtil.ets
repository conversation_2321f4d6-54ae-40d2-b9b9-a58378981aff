/****
 * 拆分MeetingPage逻辑，缓解主页代码压力
 */
import { UserCommon } from "../common/infowarelabsdk/confctrl/UserCommon";
import { MessageModel } from "../common/model";
import { ChatMessage, Participant, ParticipantClass } from "../models";
import { emitter } from "@kit.BasicServicesKit";

export function userRole(userList: ParticipantClass[], user: Participant) {
  console.log('kkk userData', JSON.stringify(user))
  //action 0进入 1退出
  if (user.action === 0) {
    const isCZ = userList.find(item => item.uid === user.uid)
    if (isCZ) {
      console.log('kkk 该用户已存在')
      return
    }
    user.audioOpen = false
    user.videoOpen = false
    user.nickname = user.username
    user.isReadedMsg = true
    user.device = user.role
    user.role = UserCommon.getUserRole(user.role!)
    user.channelId = 0
    // userList.push(new ParticipantClass(user))
    //todo:排序
    if (user.role === 1) {
      userList.unshift(new ParticipantClass(user))
    } else {
      userList.push(new ParticipantClass(user))
    }
  } else if (user.action === 1) {
    const index = userList.findIndex(item => item.uid === user.uid)
    userList.splice(index, 1)
  }
}

export function receiveMessage(messageList: ChatMessage[], data: MessageModel,
  isShowChatPage: boolean, currentChatTarget: ParticipantClass | undefined, userList: ParticipantClass[]) {
  console.log('check message +++', JSON.stringify(data))
  let newMessage: ChatMessage = {
    id: messageList.length + 1,
    uid: data.uid,
    username: data.username,
    message: data.message,
    date: new Date().toLocaleTimeString(),
    isPublic: data.isPublic,
    isReaded: false
  }
  messageList.push(newMessage) //向数组中添加聊天信息

  if (isShowChatPage) {
    if (currentChatTarget?.uid === newMessage.uid || newMessage.isPublic) {
      emitter.emit('hss_chat_message_receive', { data: newMessage })
    }else {
      if (!newMessage.isPublic) {
        //未读消息
        const index = userList.findIndex(item => item.uid === data.uid)
        userList[index].isReadedMsg = false
      }
    }
  } else {
    if (!newMessage.isPublic) {
      //未读消息
      const index = userList.findIndex(item => item.uid === data.uid)
      userList[index].isReadedMsg = false
    }
  }
}

export function audioStateChange(userList: ParticipantClass[], userId: number, isOpen: boolean) {
  console.log('kkk audioChange', userId, isOpen)
  const index = userList.findIndex(item => item.uid === userId)
  if (index === -1) {
    console.log('kkk audio 该用户不存在')
    return
  }
  if (userList[index].audioOpen === isOpen) {
    console.log('kkk audio 重复设置音频')
    return
  }
  userList[index].audioOpen = isOpen
}



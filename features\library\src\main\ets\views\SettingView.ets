import { SetUserName } from "../common/infowarelabsdk/conference"
import { Participant } from "../models"
import { VideoBit } from "../pages/VideoView"

@Component
export struct SettingView {
  @Prop userData: Participant
  resolutionRatios: VideoBit[] = [
  // {
  //   bitRate: 1024000,
  //   // bitRate: 512000,
  //   width: 1920,
  //   height: 1080,
  //   title: '超清'
  // },
    {
      bitRate: 512000,
      width: 1280,
      height: 720,
      title: '高清'
    },
  // {
  //   bitRate: 256000,
  //   width: 640,
  //   height: 480,
  //   title: '清晰'
  // },
  // {
  //   bitRate: 128000,
  //   width: 352,
  //   height: 288,
  //   title: '标准'
  // },
  ]
  @State currentIndex: number = 0
  @State nickName: string = ''
  nickNameChange = (name: string) => {
  }

  aboutToAppear(): void {
    const data = AppStorage.get<VideoBit>('hss_video_storage') as VideoBit | undefined;
    if (data) {
      this.currentIndex = this.resolutionRatios.findIndex(ration => ration.bitRate === data.bitRate)
    }
    this.nickName = this.userData.nickname || ''
  }

  build() {
    Column() {
      Text('视频分辨率')
        .width('100%')
        .padding(10)
        .fontColor(Color.Gray)

      List({ space: 20 }) {
        ForEach(this.resolutionRatios, (ration: VideoBit, index) => {
          ListItem() {
            Row() {
              Text(`${ration.title}（${ration.width}*${ration.height}）`)
                .fontSize(18)
                .layoutWeight(1)

              if (this.currentIndex === index) {
                Image($r('app.media.a6_icon_checked'))
                  .width(18)
              }
            }
            .width('100%')
            .padding({ left: 10, right: 10 })
            .onClick(() => {
              if (this.currentIndex !== index) {
                AppStorage.setOrCreate('hss_video_storage', ration)
                this.currentIndex = index
              }
            })
          }
        })
      }
      .width('100%')
      .padding({ top: 10, bottom: 10 })
      .divider({ strokeWidth: 1 })
      .backgroundColor(Color.White)


      Text('显示名')
        .width('100%')
        .padding(10)
        .fontColor(Color.Gray)
      Column() {
        TextInput({ text: $$this.nickName })
          .padding(0)
          .border({ width: 0 })
          .borderRadius(0)
          .backgroundColor(Color.White)
          .fontSize(18)
          .placeholderFont({ size: 18 })
          .onSubmit(() => {
            const xx = SetUserName(this.userData.uid || 0, this.nickName)
            this.nickNameChange(this.nickName)
          })
      }
      .width('100%')
      .padding(10)
      .backgroundColor(Color.White)
    }
    .backgroundColor('#ffefefef')
    .width('100%')
    .height('100%')
  }
}
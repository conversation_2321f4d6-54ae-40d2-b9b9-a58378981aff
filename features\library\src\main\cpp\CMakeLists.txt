# the minimum version of CMake.
cmake_minimum_required(VERSION 3.5.0)
project(library)


set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
set(BASE_LIBRARY
    libace_napi.z.so libEGL.so libGLESv3.so libace_ndk.z.so libuv.so libhilog_ndk.z.so
    libnative_media_codecbase.so libnative_media_core.so libnative_media_vdec.so libnative_window.so
    libnative_media_venc.so libnative_media_acodec.so libnative_media_avdemuxer.so libnative_media_avsource.so libnative_media_avmuxer.so
    libohaudio.so
)
# Add camera library with correct name
add_library(encoder SHARED encoder/EncoderNative.cpp
                           capbilities/VideoDecoder.cpp
                            encoder/Encoder.cpp
                            capbilities/VideoDecoder.cpp
                            common/SampleCallback.cpp
                            capbilities/VideoEncoder.cpp

)


add_library(capture SHARED screencapture/CaptureNative.cpp
                            screencapture/CaptureNative.h
                            capbilities/VideoEncoder.cpp
                            common/SampleCallback.cpp
                            screencapture/Capture.cpp
                            screencapture/Capture.h
)

add_library(player SHARED decoder/DecoderNative.cpp
                          decoder/Decoder.cpp
                          decoder/videoDataSink.cpp
                          capbilities/VideoDecoder.cpp
                          render/EglCore.cpp
                          render/PluginRender.cpp
                          render/PluginManager.cpp
                          common/SampleCallback.cpp
)


add_library(desktopshare SHARED desktopshare/DecoderNative.cpp
                          desktopshare/Decoder.cpp
                          capbilities/VideoDecoder.cpp
                          render/EglCore.cpp
                          render/PluginRender.cpp
                          render/PluginManager.cpp
                          common/SampleCallback.cpp
                          decoder/videoDataSink.cpp
)

target_link_libraries(player PUBLIC ${BASE_LIBRARY} lib_hstinterface lib_hstsdk lib_hstwebrtc)


# Link required libraries
target_link_libraries(encoder PUBLIC ${BASE_LIBRARY} lib_yuv)


target_link_libraries(capture PUBLIC ${BASE_LIBRARY}
    libnative_avscreen_capture.so
    libnative_buffer.so
    libnative_media_core.so
    lib_hstinterface
    lib_hstsdk
    lib_hstwebrtc
    lib_yuv
)

target_link_libraries(desktopshare PUBLIC ${BASE_LIBRARY} lib_hstinterface lib_hstsdk lib_hstwebrtc)



# Set output name to match oh-package.json5
set_target_properties(encoder PROPERTIES
    OUTPUT_NAME "encoder"
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}
)
set_target_properties(player PROPERTIES
    OUTPUT_NAME "player"
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}
)
set_target_properties(capture PROPERTIES
    OUTPUT_NAME "capture"
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}
)
set_target_properties(desktopshare PROPERTIES
    OUTPUT_NAME "desktopshare"
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}
)
if(DEFINED PACKAGE_FIND_FILE)
    include(${PACKAGE_FIND_FILE})
endif()

include_directories(${NATIVERENDER_ROOT_PATH}
                    ${NATIVERENDER_ROOT_PATH}/include)

add_library(entry SHARED napi_init.cpp)
target_link_libraries(entry PUBLIC libace_napi.z.so)
target_link_libraries(encoder PUBLIC ${BASE_LIBRARY} lib_hstinterface)

add_library(lib_hstinterface SHARED IMPORTED)
set_target_properties(lib_hstinterface PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}/libhstinterface.so)
target_link_libraries(entry PUBLIC lib_hstinterface)

add_library(lib_hstsdk SHARED IMPORTED)
set_target_properties(lib_hstsdk PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}/libhstsdk.so)
target_link_libraries(entry PUBLIC lib_hstsdk)

add_library(lib_hstwebrtc SHARED IMPORTED)
set_target_properties(lib_hstwebrtc PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}/libhstwebrtc.so)
target_link_libraries(entry PUBLIC lib_hstwebrtc)


add_library(lib_yuv SHARED IMPORTED)
set_target_properties(lib_yuv PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../libs/${OHOS_ARCH}/libyuv.so)
target_link_libraries(entry PUBLIC lib_yuv)

target_link_libraries(entry PUBLIC libhilog_ndk.z.so)



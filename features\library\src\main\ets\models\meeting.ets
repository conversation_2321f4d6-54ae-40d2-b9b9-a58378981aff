// 会议参数接口
export interface MeetingConfig {
  // 基本参数
  meetingId: string      // 会议号(必需)
  displayName: string    // 显示名称(必需)
  password?: string      // 会议密码(可选)
  confType?: number
  role?: number
  siteUrl?: string
  // 音视频参数
  openAudio?: boolean    // 是否开启音频
  openVideo?: boolean    // 是否开启视频

  // 用户信息
  userId?: number        // 用户ID
  siteId?: string       // 站点ID
  userName?: string     // 用户名

  // 服务器配置
  serverIP?: string     // 服务器IP
  serverPort?: number   // 服务器端口
  useSSL?: boolean     // 是否使用SSL
  InviteUrl:string
}


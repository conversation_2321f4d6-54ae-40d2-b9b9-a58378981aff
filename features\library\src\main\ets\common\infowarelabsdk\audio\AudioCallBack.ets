import testNapi from 'libhstinterface.so'
import { callback<PERSON>anager, CallBackManager } from '../callback/CallBackManager';
import { CurrentUserlist, RosterInfoUpdateCallbacks } from '../conference';

export type audioStatusChangeFun = (nUserId:number, bHaveDev:boolean, bOpen:boolean) => void

let audioStatusChangeCallback: audioStatusChangeFun[] = []


//设置是否开始音频
export function audio_onOpenAudioConfirm(bEnable: boolean) {
  if (bEnable) {
    testNapi.audio_StartSend(0);
  } else {
    testNapi.audio_StopSend();
  }
}

export function audio_onUpdateDeviceStatus(nUserId:number, bHaveDev:boolean, bOpen:boolean) {
  //let index = CurrentUserlist.findIndex( user => user.uid === nUserId)
  // if(CurrentUserlist[index]){
  //   CurrentUserlist[index].isHaveMic = bOpen
  //   console.log('audio++++++',RosterInfoUpdateCallbacks.length)
  //   RosterInfoUpdateCallbacks.forEach(callback =>{
  //     console.log('audio++++++___(((')
  //     callback(CurrentUserlist)
  //   })
  // }
  audioStatusChangeCallback.forEach(callback => {
    if( typeof callback === 'function'){
      callback(nUserId, bHaveDev, bOpen)
    }
  })
  //console.log('audiolist',JSON.stringify(CurrentUserlist),index)
}

export const addAudioStatusChangeCallback = (callback:audioStatusChangeFun) => {
  audioStatusChangeCallback.push(callback)
}

export const removeAudioStatusChangeCallback = (callback:audioStatusChangeFun) => {
  audioStatusChangeCallback = audioStatusChangeCallback.filter( i => i !== callback)
}

export function audio_onConferenceSupportH323() {

}

export function audio_onSyncVoipCodec() {

}

export function audio_onInviteH323Response() {

}

export function audio_onAudioParamChanged() {

}

export function audio_onMaxVoice() {

}

export function audio_onActiveSpeaker() {

}

export function audio_onLocalPcmData() {

}


export function audio_registerCallback() {
  testNapi.registerCallback("audio_onUpdateDeviceStatus", audio_onUpdateDeviceStatus);
  testNapi.registerCallback('audio_onOpenAudioConfirm', audio_onOpenAudioConfirm)
  testNapi.registerCallback("audio_onConferenceSupportH323", audio_onConferenceSupportH323);
  testNapi.registerCallback("audio_onSyncVoipCodec", audio_onSyncVoipCodec);
  testNapi.registerCallback("audio_onInviteH323Response", audio_onInviteH323Response);
  testNapi.registerCallback("audio_onAudioParamChanged", audio_onAudioParamChanged);
  testNapi.registerCallback("audio_onMaxVoice", audio_onMaxVoice);
  testNapi.registerCallback("audio_onActiveSpeaker", audio_onActiveSpeaker);
  testNapi.registerCallback("audio_onLocalPcmData", audio_onLocalPcmData);
  callbackManager.initIsFailed = true
}

export function audio_unregisterCallback() {
  testNapi.unregisterCallback("audio_onUpdateDeviceStatus", audio_onUpdateDeviceStatus);
  testNapi.unregisterCallback('audio_onOpenAudioConfirm', audio_onOpenAudioConfirm)
  testNapi.unregisterCallback('audio_onConferenceSupportH323', audio_onConferenceSupportH323);
  testNapi.unregisterCallback('audio_onSyncVoipCodec', audio_onSyncVoipCodec)
  testNapi.unregisterCallback("audio_onInviteH323Response", audio_onInviteH323Response);
  testNapi.unregisterCallback("audio_onAudioParamChanged", audio_onAudioParamChanged);
  testNapi.unregisterCallback("audio_onMaxVoice", audio_onMaxVoice);
  testNapi.unregisterCallback("audio_onActiveSpeaker", audio_onActiveSpeaker);
  testNapi.unregisterCallback("audio_onLocalPcmData", audio_onLocalPcmData);
}
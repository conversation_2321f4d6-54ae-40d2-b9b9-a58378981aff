#include <napi/native_api.h>
#include "common/SampleInfo.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "ohcamera/capture_session.h"
#include "ohcamera/camera_manager.h"
#include "EncoderNative.h"
#include "Encorder.h"

struct AsyncCallbackInfo {
    napi_env env;
    napi_async_work asyncWork;
    napi_deferred deferred;
    int32_t resultCode = 0;
    std::string surfaceId = "";
    SampleInfo sampleInfo;
};
void SetCallBackResult(AsyncCallbackInfo *asyncCallbackInfo, int32_t code) { asyncCallbackInfo->resultCode = code; }

void SurfaceIdCallBack(AsyncCallbackInfo *asyncCallbackInfo, std::string surfaceId) {
    asyncCallbackInfo->surfaceId = surfaceId;
}
void NativeInit(napi_env env, void *data) {
    AsyncCallbackInfo *asyncCallbackInfo = static_cast<AsyncCallbackInfo *>(data);
    int32_t ret = Encoder::GetInstance().Init(asyncCallbackInfo->sampleInfo);
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        SetCallBackResult(asyncCallbackInfo, -1);
    }   
    

    uint64_t id = 0;
    ret = OH_NativeWindow_GetSurfaceId(asyncCallbackInfo->sampleInfo.window, &id);
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        SetCallBackResult(asyncCallbackInfo, -1);
    }
    asyncCallbackInfo->surfaceId = std::to_string(id);
    SurfaceIdCallBack(asyncCallbackInfo, asyncCallbackInfo->surfaceId);
}

void DealCallBack(napi_env env, void *data) {
    AsyncCallbackInfo *asyncCallbackInfo = static_cast<AsyncCallbackInfo *>(data);
    napi_value code;
    napi_create_int32(env, asyncCallbackInfo->resultCode, &code);
    napi_value surfaceId;
    napi_create_string_utf8(env, asyncCallbackInfo->surfaceId.data(), NAPI_AUTO_LENGTH, &surfaceId);
    napi_value obj;
    napi_create_object(env, &obj);

    napi_set_named_property(env, obj, "code", code);
    napi_set_named_property(env, obj, "surfaceId", surfaceId);
    napi_resolve_deferred(asyncCallbackInfo->env, asyncCallbackInfo->deferred, obj);
    napi_delete_async_work(env, asyncCallbackInfo->asyncWork);
    delete asyncCallbackInfo;
}



napi_value EncoderNative::Init(napi_env env, napi_callback_info info) {
    int32_t two = 2;
    int32_t three = 3;
    int32_t four = 4;
    int32_t five = 5;
    int32_t six = 6;
    SampleInfo sampleInfo;
    size_t argc = 7;
    napi_value args[7] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    napi_get_value_int32(env, args[0], &sampleInfo.outputFd);
    char videoCodecMime[20] = {0};
    size_t videoCodecMimeStrlen = 0;
    size_t len = 20;
    napi_get_value_string_utf8(env, args[1], videoCodecMime, len, &videoCodecMimeStrlen);
    napi_get_value_int32(env, args[two], &sampleInfo.videoWidth);
    napi_get_value_int32(env, args[three], &sampleInfo.videoHeight);
    napi_get_value_double(env, args[four], &sampleInfo.frameRate);
    napi_get_value_int32(env, args[five], &sampleInfo.isHDRVivid);
    napi_get_value_int64(env, args[six], &sampleInfo.bitrate);
    sampleInfo.videoCodecMime = videoCodecMime;

    if (sampleInfo.isHDRVivid) {
        sampleInfo.hevcProfile = HEVC_PROFILE_MAIN_10;
    }

    napi_value promise;
    napi_deferred deferred;
    napi_create_promise(env, &deferred, &promise);

    AsyncCallbackInfo *asyncCallbackInfo = new AsyncCallbackInfo();

    asyncCallbackInfo->env = env;
    asyncCallbackInfo->asyncWork = nullptr;
    asyncCallbackInfo->deferred = deferred;
    asyncCallbackInfo->resultCode = -1;
    asyncCallbackInfo->sampleInfo = sampleInfo;

    napi_value resourceName;
    napi_create_string_latin1(env, "encoder", NAPI_AUTO_LENGTH, &resourceName);
    napi_create_async_work(
        env, nullptr, resourceName, [](napi_env env, void *data) { NativeInit(env, data); },
        [](napi_env env, napi_status status, void *data) { DealCallBack(env, data); }, (void *)asyncCallbackInfo,
        &asyncCallbackInfo->asyncWork);
    napi_queue_async_work(env, asyncCallbackInfo->asyncWork);
    return promise;
}

napi_value EncoderNative::Start(napi_env env, napi_callback_info info) {
    int32_t ret = Encoder::GetInstance().Start();
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        napi_value error;
        return error;
    }
    return nullptr;
}

napi_value EncoderNative::Stop(napi_env env, napi_callback_info info) {
    int32_t ret = Encoder::GetInstance().Stop();
    if (ret != AVCODEC_SAMPLE_ERR_OK) {
        napi_value error;
        return error;
    }
    return nullptr;
}

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports) {
    napi_property_descriptor desc[] = {
        {"initNative", nullptr, EncoderNative::Init, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"startNative", nullptr, EncoderNative::Start, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"stopNative", nullptr, EncoderNative::Stop, nullptr, nullptr, nullptr, napi_default, nullptr}
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module encoderModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "encoder",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

extern "C" __attribute__((constructor)) void RegisterEncoderModule(void) {
    napi_module_register(&encoderModule);
} 
import testNapi from "libhstinterface.so"
import { BusinessType } from "../../Constant"
import { callbackManager } from "../callback/CallBackManager"

function CloseVideo() {

}

function SetVideoSync() {

}

function SetVideoSyncAll() {

}

function EXchange() {

}

function SetLiveVideo() {

}

function SetHardDecoder() {

}

function CreateLocalChannel() {

}

function RemoveLocalChannel() {

}

function EnableSvc() {

}

function IsSvcEnable() {

}

function SetChannelSvc() {

}

function GetChannelSvc() {

}

function EnableCloudRecordAll() {

}

function DisableCloudRecordALL() {

}

function EnableCloudRecord() {

}

function DisableCloudRecord() {

}

function SetVideoLayout() {

}

function DisableSVC() {

}



export function video_registerCallback() {
}

export function video_unregisterCallback() {
}
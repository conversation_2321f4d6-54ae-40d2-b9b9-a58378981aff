import { BuilderNameConstants, RouterModule, RouterNameConstants } from "routermoudel"
import { promptAction } from '@kit.ArkUI';
import emitter from '@ohos.events.emitter';
import { serverConfig, ServerConfig } from "basic";
import { http } from '@kit.NetworkKit';

// 服务器验证响应接口
interface ServerValidationResponse {
  isValid: boolean;
  siteName?: string;
  siteId?: string;
  siteType?: number;
  liveServer?: boolean;
  errorMessage?: string;
}

@Component
struct ServerSettingsPage {
  @State serverUrl: string = '';
  @State isLoading: boolean = false;

  async aboutToAppear() {
    // 确保ServerConfig已初始化
    await serverConfig.ensureInitialized();
    // 从ServerConfig获取当前服务器地址
    this.loadServerUrl();
  }

  aboutToDisappear() {
    // 发送导航栏重置事件
    emitter.emit('resetNavigation');
  }

  // 加载服务器地址
  loadServerUrl() {
    try {
      // 从ServerConfig获取当前服务器地址
      this.serverUrl = serverConfig.getServerUrl();
      console.info('[ServerSettingsPage] 加载服务器地址:', this.serverUrl);
    } catch (error) {
      console.error('加载服务器地址失败:', error);
      this.serverUrl = ''; // 移除硬编码的qa27地址
    }
  }

  // 验证服务器地址
  async validateServerUrl(url: string): Promise<ServerValidationResponse> {
    try {
      // 构建验证URL
      const validationUrl = `${url}/?from=ET`;
      console.info('[服务器验证] 验证URL:', validationUrl);

      // 创建HTTP请求
      const httpRequest = http.createHttp();

      const response = await httpRequest.request(validationUrl, {
        method: http.RequestMethod.GET,
        readTimeout: 10000,
        connectTimeout: 5000,
        header: {
          'Content-Type': 'application/xml'
        }
      });

      // 销毁HTTP请求实例
      httpRequest.destroy();

      if (response.responseCode === 200 && response.result) {
        const xmlData = response.result.toString();
        console.info('[服务器验证] 响应内容:', xmlData);

        // 解析XML响应
        return this.parseServerValidationXml(xmlData);
      } else {
        console.error('[服务器验证] 请求失败，状态码:', response.responseCode);
        return {
          isValid: false,
          errorMessage: '服务器响应异常'
        };
      }
    } catch (error) {
      console.error('[服务器验证] 验证失败:', error);
      return {
        isValid: false,
        errorMessage: '无法连接到服务器'
      };
    }
  }

  // 解析服务器验证XML响应
  parseServerValidationXml(xmlData: string): ServerValidationResponse {
    try {
      // 检查根元素是否存在
      const responseMatch = xmlData.match(/<root>([\s\S]*?)<\/root>/i);
      if (!responseMatch) {
        console.error('[XML解析] 未找到根元素response');
        return {
          isValid: false,
          errorMessage: '站点地址不存在'
        };
      }

      // 检查return元素，必须为"0"表示成功
      const returnMatch = xmlData.match(/<return>\s*([^<]+)\s*<\/return>/i);
      if (!returnMatch || returnMatch[1].trim() !== '0') {
        console.error('[XML解析] return元素不存在或值不为0:', returnMatch?.[1]);
        return {
          isValid: false,
          errorMessage: '站点地址不存在'
        };
      }

      // 检查siteName元素，必须存在
      const siteNameMatch = xmlData.match(/<siteName>\s*([^<]+)\s*<\/siteName>/i);
      if (!siteNameMatch) {
        console.error('[XML解析] siteName元素不存在');
        return {
          isValid: false,
          errorMessage: '站点地址不存在'
        };
      }

      // 提取可选字段
      const siteIdMatch = xmlData.match(/<siteId>\s*([^<]+)\s*<\/siteId>/i);
      const siteTypeMatch = xmlData.match(/<siteType>\s*([^<]+)\s*<\/siteType>/i);
      const liveServerMatch = xmlData.match(/<liveServer>\s*([^<]+)\s*<\/liveServer>/i);

      console.info('[XML解析] 解析成功:', {
        siteName: siteNameMatch[1].trim(),
        siteId: siteIdMatch?.[1]?.trim() || '0',
        siteType: siteTypeMatch?.[1]?.trim() || '0',
        liveServer: liveServerMatch?.[1]?.trim() || '0'
      });

      return {
        isValid: true,
        siteName: siteNameMatch[1].trim(),
        siteId: siteIdMatch?.[1]?.trim() || '0',
        siteType: parseInt(siteTypeMatch?.[1]?.trim() || '0'),
        liveServer: liveServerMatch?.[1]?.trim() === '1'
      };
    } catch (error) {
      console.error('[XML解析] 解析失败:', error);
      return {
        isValid: false,
        errorMessage: '站点地址不存在'
      };
    }
  }

  // 保存服务器地址
  async saveServerUrl() {
    try {
      // 验证URL格式
      if (!ServerConfig.validateUrl(this.serverUrl)) {
        promptAction.showToast({ message: '请输入有效的服务器地址' });
        return;
      }

      this.isLoading = true;

      // 验证服务器地址
      console.info('[ServerSettingsPage] 开始验证服务器地址:', this.serverUrl);
      const validationResult = await this.validateServerUrl(this.serverUrl);

      if (validationResult.isValid) {
        // 验证成功，保存服务器地址
        await serverConfig.setServerUrl(this.serverUrl);
        console.info('[ServerSettingsPage] 服务器地址已更新:', this.serverUrl);

        // 显示成功提示，包含站点名称
        const successMessage = validationResult.siteName ?
          `配置成功 - ${validationResult.siteName}` : '配置成功';
        promptAction.showToast({ message: successMessage });

        // 返回到设置页面
        setTimeout(() => {
          RouterModule.pop(RouterNameConstants.HomeIndex);
        }, 1000);
      } else {
        // 验证失败，显示错误信息
        const errorMessage = validationResult.errorMessage || '站点地址不存在';
        promptAction.showToast({ message: errorMessage });
        console.error('[ServerSettingsPage] 服务器验证失败:', errorMessage);
      }

    } catch (error) {
      console.error('保存服务器地址失败:', error);
      promptAction.showToast({ message: '保存失败，请重试' });
    } finally {
      this.isLoading = false;
    }
  }

  // 重置为默认地址
  async resetToDefault() {
    await serverConfig.resetToDefault();
    this.serverUrl = serverConfig.getServerUrl();
  }

  build() {
    NavDestination() {
      Column() {
        // 服务器地址输入区域
        Column() {
          TextInput({ text: this.serverUrl, placeholder: '请输入服务器地址' })
            .type(InputType.URL)
            .enterKeyType(EnterKeyType.Done)
            .defaultFocus(true)
            .width('100%')
            .height(40)
            .backgroundColor($r('app.color.back_grand_color'))
            .border({ width: 0 })
            .padding({ left: 16, right: 16 })
            .fontSize(16)
            .fontColor($r('app.color.text_input_color'))
            .placeholderColor('#CCCCCC')
            .cancelButton({style: CancelButtonStyle.INPUT})
            .onSubmit(
              () => {
                this.saveServerUrl();
              }
            )
            .onChange((value: string) => {
              this.serverUrl = value;
            })
        }
        .width('100%')
        .backgroundColor($r('app.color.back_grand_color2'))
        .padding({ left: 16, right: 16, top: 12, bottom: 12 })
        .margin({ top: 20 })
        Blank().height(12)
        
        // 确定按钮
        Column() {
          Button() {
            Row() {
              if (this.isLoading) {
                LoadingProgress()
                  .width(20)
                  .height(20)
                  .color(Color.White)
                  .margin({ right: 8 })
              }
              Text(this.isLoading ? '保存中...' : '确定')
                .fontSize(18)
                .fontColor(Color.White)
                .fontWeight(FontWeight.Medium)
            }
            .justifyContent(FlexAlign.Center)
            .alignItems(VerticalAlign.Center)
          }
          .width('100%')
          .height(50)
          .backgroundColor('#4285F4')
          .borderRadius(8)
          .enabled(!this.isLoading)
          .onClick(() => {
            this.saveServerUrl();
          })
        }
        .width('100%')
        .padding({ left: 16, right: 16, bottom: 34 })
      }
      .width('100%')
      .height('100%')
      .backgroundColor($r('app.color.back_grand_color2'))
    }
    .title('服务器地址')
    .onBackPressed(() => {
      // 在返回前立即发送重置事件
      emitter.emit('resetNavigation');
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }
}

@Builder
export function ServerSettings_Page(value: object) {
  ServerSettingsPage()
}

const builderName = BuilderNameConstants.ServerSettingsPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(ServerSettings_Page);
  RouterModule.registerBuilder(builderName, builder);
}

import testNapi from 'libhstinterface.so';
import { ChatMessage, MessageModel, SendChatMessage } from "../../model";

/**
 * 获得聊天数据,C回调方法
 *
 * @param sender
 *            发送人
 * @param message
 *            信息内容
 * @param isPubilc
 *            是否公聊
 */

export function chat_Send_ChatMsg(message: SendChatMessage): number {
  let targetUid: number = message.targetUid
  let content: string = message.content
  let myUsername: string = message.username
  let isPublic: boolean = message.isPublic
  return testNapi.chat_SendData(targetUid, myUsername, content, isPublic)
}
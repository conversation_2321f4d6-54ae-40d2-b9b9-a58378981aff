import { login<PERSON><PERSON>, UserInfoData } from "basic";
import { LoginReqModel } from "basic/src/main/ets/model/LoginReqModel";
import { BuilderNameConstants, builderRouterModel, RouterModule, RouterNameConstants } from "routermoudel";
import emitter from "@ohos.events.emitter";
import { promptAction } from '@kit.ArkUI';

@Component
struct LoginPage {
  @State username: string = '';
  @State password: string = '';
  @State isLoading: boolean = false;

  // 表单验证状态
  @State usernameError: string = '';
  @State passwordError: string = '';
  @State isFormValid: boolean = false;

  // 输入框焦点状态
  @State usernameFocused: boolean = false;
  @State passwordFocused: boolean = false;

  // 验证用户名
  private validateUsername(username: string): string {
    if (!username || username.trim().length === 0) {
      return '请输入用户名';
    }
    if (username.length < 3) {
      return '用户名至少需要3个字符';
    }
    if (username.length > 20) {
      return '用户名不能超过20个字符';
    }
    // 检查用户名格式：只允许字母、数字、下划线
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
      return '用户名只能包含字母、数字和下划线';
    }
    return '';
  }

  // 验证密码
  private validatePassword(password: string): string {
    if (!password || password.trim().length === 0) {
      return '请输入密码';
    }
    if (password.length < 6) {
      return '密码至少需要6个字符';
    }
    if (password.length > 20) {
      return '密码不能超过20个字符';
    }
    return '';
  }

  // 更新表单验证状态
  private updateFormValidation(): void {
    this.usernameError = this.validateUsername(this.username);
    this.passwordError = this.validatePassword(this.password);
    this.isFormValid = this.usernameError === '' && this.passwordError === '';
  }

  // 处理登录
  private async handleLogin(): Promise<void> {
    // 先进行表单验证
    this.updateFormValidation();

    if (!this.isFormValid) {
      // 优先显示用户名错误，如果用户名没有错误再显示密码错误
      if (this.usernameError) {
        promptAction.showToast({ message: this.usernameError });
      } else if (this.passwordError) {
        promptAction.showToast({ message: this.passwordError });
      }
      return;
    }

    this.isLoading = true;

    try {
      console.info(`用户登录: ${this.username}`);

      // 创建登录请求参数
      const loginParams = new LoginReqModel(
        'box', // 站点名称
        'meeting',      // 服务名称
        this.username,  // 用户名
        this.password   // 密码
      );

      // 调用登录API
      await loginApi.login(loginParams);
      console.log("登录成功");

      // 显示成功提示
      promptAction.showToast({ message: '登录成功' });


      // 登录成功后发送事件通知主页刷新
      emitter.emit('loginSuccess');

      // 登录成功后跳转到首页
      // 如果是从启动页面来的，需要清空路由栈并重新加载主页
      RouterModule.clear(RouterNameConstants.HomeIndex);

      // 发送事件通知应用重新检查登录状态并显示主页
      emitter.emit('loginStatusChanged');

    } catch (error) {
      console.error('登录失败:', error);
      let errorMessage = '登录失败，请检查用户名和密码';

      if (error instanceof Error) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络设置';
        } else if (error.message.includes('401') || error.message.includes('认证')) {
          errorMessage = '用户名或密码错误';
        } else if (error.message.includes('403')) {
          errorMessage = '账户被禁用，请联系管理员';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器错误，请稍后重试';
        }
      }

      promptAction.showToast({ message: errorMessage });
    } finally {
      this.isLoading = false;
    }
  }

  // 处理匿名登录
  async handleAnonymousLogin() {
    try {
      console.info('开始匿名登录流程');

      // 创建匿名用户信息
      const anonymousUserInfo: UserInfoData = {
        userId: -1, // 使用-1表示匿名用户
        userName: 'anonymous_user',
        realName: '匿名用户',
        nickName: '匿名用户',
        roles: 'guest',
        token: 'anonymous_token_' + Date.now(), // 生成一个临时token
        email: ''
      };

      // 保存匿名用户信息
      await loginApi.getCurrentUser().saveUserInfo(anonymousUserInfo);
      console.info('匿名用户信息已保存:', JSON.stringify(anonymousUserInfo));

      // 显示匿名登录成功提示
      promptAction.showToast({ message: '匿名登录成功' });

      // 发送登录成功事件通知主页刷新
      emitter.emit('loginSuccess');

      // 清空路由栈并跳转到首页
      RouterModule.clear(RouterNameConstants.HomeIndex);

      // 发送事件通知应用重新检查登录状态并显示主页
      emitter.emit('loginStatusChanged');

    } catch (error) {
      console.error('匿名登录失败:', error);
      promptAction.showToast({ message: '匿名登录失败，请重试' });
    }
  }

  build() {
    Row() {
      NavDestination() {
        Column() {
          // 顶部状态栏占位
          Row()
            .height(20)

          // 顶部设置图标区域
          Row() {
            Image($r('app.media.hs_setting'))
              .width(24)
              .height(24)
              .objectFit(ImageFit.Contain)
              .onClick(() => {
                // 跳转到服务器地址设置页面
                builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.ServerSettingsPage, new Object({
                  origin: 'www'
                }));
              })

            Blank()
          }
          .width('100%')
          .padding({ left: 5, right: 20, top: 5 })
          .alignItems(VerticalAlign.Center)

          // Logo和标题区域
          Column() {
            // Logo - 红杉会议图标
            Image($r('app.media.hs_logo'))
              .width(120)
              .height(120)
              .objectFit(ImageFit.Contain)
              .margin({ bottom: 20 })
          }
          .width('100%')
          .alignItems(HorizontalAlign.Center)
          .margin({ top: 40 })

          // 用户名输入框
          Column() {
            Text('用户名称：')
              .fontSize(16)
              .fontColor($r('app.color.sub_title_color'))
              .fontWeight(FontWeight.Normal)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 4 })

            Column() {
              TextInput({ placeholder: '请输入用户名', text: this.username })
                .width('100%')
                .height(48)
                .backgroundColor($r('app.color.back_grand_color'))
                .border({ width: 0 })
                .padding({ left: 0, right: 0, top: 4, bottom: 4 })
                .fontSize(16)
                .fontColor($r('app.color.text_input_color'))
                .type(InputType.USER_NAME)
                .placeholderColor('#CCCCCC')
                .onChange((value: string) => {
                  this.username = value.trim();
                  // 实时验证
                  this.usernameError = this.validateUsername(this.username);
                  this.updateFormValidation();
                })
                .onBlur(() => {
                  this.usernameFocused = false;
                  // 失去焦点时验证
                  this.usernameError = this.validateUsername(this.username);
                  this.updateFormValidation();
                })
                .onFocus(() => {
                  this.usernameFocused = true;
                  // 获得焦点时清除错误状态
                  if (this.usernameError) {
                    this.usernameError = '';
                    this.updateFormValidation();
                  }
                })

              // 底部线条
              Divider()
                .strokeWidth(1)
                .color(this.usernameFocused ? '#007AFF' : '#E0E0E0')
                .margin({ top: 2 })
            }
            .width('100%')
          }
          .width('90%')
          .margin({ bottom: 20 })
          .alignItems(HorizontalAlign.Start)

          // 密码输入框
          Column() {
            Text('密码')
              .fontSize(16)
              .fontColor($r('app.color.sub_title_color'))
              .fontWeight(FontWeight.Normal)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 4 })

            Column() {
              TextInput({ placeholder: '请输入密码', text: this.password })
                .width('100%')
                .height(48)
                .backgroundColor(Color.Transparent)
                .border({ width: 0 })
                .padding({ left: 0, right: 0, top: 4, bottom: 4 })
                .fontSize(16)
                .fontColor($r('app.color.text_input_color'))
                .placeholderColor('#CCCCCC')
                .type(InputType.Password)
                .onChange((value: string) => {
                  this.password = value;
                  // 实时验证
                  this.passwordError = this.validatePassword(this.password);
                  this.updateFormValidation();
                })
                .onBlur(() => {
                  this.passwordFocused = false;
                  // 失去焦点时验证
                  this.passwordError = this.validatePassword(this.password);
                  this.updateFormValidation();
                })
                .onFocus(() => {
                  this.passwordFocused = true;
                  // 获得焦点时清除错误状态
                  if (this.passwordError) {
                    this.passwordError = '';
                    this.updateFormValidation();
                  }
                })

              // 底部线条
              Divider()
                .strokeWidth(1)
                .color(this.passwordFocused ? '#007AFF' : '#E0E0E0')
                .margin({ top: 2 })
            }
            .width('100%')

            // 错误提示区域 - 每次只显示一个错误信息
            Column() {
              if (this.usernameError) {
                Text(this.usernameError)
                  .fontSize(12)
                  .fontColor('#FF4444')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 8 })
              } else if (this.passwordError) {
                Text(this.passwordError)
                  .fontSize(12)
                  .fontColor('#FF4444')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 8 })
              }
            }
            .width('100%')
            .alignItems(HorizontalAlign.Start)
          }
          .width('90%')
          .margin({ bottom: 30 })
          .alignItems(HorizontalAlign.Start)

          // 登录按钮
          Button() {
            Row() {
              if (this.isLoading) {
                LoadingProgress()
                  .width(20)
                  .height(20)
                  .color($r('app.color.button_background_color'))
                  .margin({ right: 8 })
              }
              Text(this.isLoading ? '登录中...' : '登录')
                .fontSize(18)
                .fontColor(Color.White)
                .fontWeight(FontWeight.Medium)
            }
            .justifyContent(FlexAlign.Center)
            .alignItems(VerticalAlign.Center)
          }
          .width('90%')
          .height(48)
          .backgroundColor(this.isFormValid && !this.isLoading ? '#007AFF' : '#CCCCCC')
          .borderRadius(24)
          .enabled(this.isFormValid && !this.isLoading)
          .onClick(() => {
            this.handleLogin();
          })

          // 匿名登录选项
          Row() {
            Text('匿名登录')
              .fontSize(16)
              .fontColor(this.isLoading ? '#CCCCCC' : $r('app.color.sub_title_color'))
              .fontWeight(FontWeight.Normal)

            Text(' >')
              .fontSize(16)
              .fontColor(this.isLoading ? '#CCCCCC' : $r('app.color.sub_title_color'))
              .margin({ left: 4 })
          }
          .margin({ top: 40 })
          .justifyContent(FlexAlign.Center)
          .enabled(!this.isLoading)
          .onClick(() => {
            if (this.isLoading) return;

            // 处理匿名登录逻辑
            console.info('用户选择匿名登录');

            this.handleAnonymousLogin();
          })

          Blank()
        }
        .width('100%')
        .height('100%')
        .backgroundColor($r('app.color.back_grand_color'))
        .padding({ left: 20, right: 20 })
        .justifyContent(FlexAlign.Start)
      }
      .hideTitleBar(true)
      .onBackPressed(() => {
        RouterModule.pop(RouterNameConstants.HomeIndex);
        return true;
      })
    }
    .height('100%')
    .backgroundColor(Color.White)
  }
}



@Builder
export function Login_Page(value: object) {
  LoginPage()
}


const builderName = BuilderNameConstants.LoginPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(Login_Page);
  RouterModule.registerBuilder(builderName, builder);
}
#ifndef VIDEO_CODEC_Decoder_H
#define VIDEO_CODEC_Decoder_H

#include "capbilities/include/VideoDecoder.h"
#include "common/SampleInfo.h"
#include <bits/alltypes.h>
#include <mutex>
#include <memory>
#include <atomic>
#include <thread>
#include <unistd.h>
#include <ohaudio/native_audiorenderer.h>
#include <ohaudio/native_audiostreambuilder.h>
#include <fstream>

class Decoder {
public:
    Decoder() : isInitialized_(false) {};
    ~Decoder();

    void StartRelease();

    static Decoder &GetInstance() {
        static Decoder decoder;
        return decoder;
    }

    int32_t Init(SampleInfo &sampleInfo);
    int32_t Start();
    CodecUserData* GetVideoDecContext() { return videoDecContext_; }
    bool IsInitialized() const { return isInitialized_; }
    VideoDecoder* GetVideoDecoder() { return videoDecoder_.get(); }

private:
    void VideoDecInputThread();
    void VideoDecOutputThread();
    void AudioDecInputThread();
    void AudioDecOutputThread();
    void Release();
    void ReleaseThread();
    int32_t CreateVideoDecoder();

    std::unique_ptr<VideoDecoder> videoDecoder_ = nullptr;
    std::mutex mutex_;
    std::atomic<bool> isStarted_{false};
    std::atomic<bool> isReleased_{false};
    std::unique_ptr<std::thread> videoDecInputThread_ = nullptr;
    std::unique_ptr<std::thread> videoDecOutputThread_ = nullptr;
    std::unique_ptr<std::thread> audioDecInputThread_ = nullptr;
    std::unique_ptr<std::thread> audioDecOutputThread_ = nullptr;
    std::condition_variable doneCond_;
    SampleInfo sampleInfo_;
    CodecUserData *videoDecContext_ = nullptr;
    CodecUserData *audioDecContext_ = nullptr;
    OH_AudioStreamBuilder *builder_ = nullptr;
    OH_AudioRenderer *audioRenderer_ = nullptr;
    static constexpr int64_t MICROSECOND = 1000000;
    std::atomic<bool> isInitialized_{false};
};

#endif // VIDEO_CODEC_Decoder_H
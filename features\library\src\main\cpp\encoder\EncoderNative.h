

#ifndef VIDEO_CODEC_SAMPLE_RECODER_NATIVE_H
#define VIDEO_CODEC_SAMPLE_RECODER_NATIVE_H

#include <js_native_api.h>
#include <js_native_api_types.h>
#include <memory>
#include <native_window/external_window.h>
#include "napi/native_api.h"

class EncoderNative {
public:
    static napi_value Init(napi_env env, napi_callback_info info);
    static napi_value Start(napi_env env, napi_callback_info info);
    static napi_value Stop(napi_env env, napi_callback_info info);
};

#endif // VIDEO_CODEC_SAMPLE_RECODER_NATIVE_H
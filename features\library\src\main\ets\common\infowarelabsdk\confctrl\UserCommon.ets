export class UserCommon {
  /*  static readonly RT_ROLE_HOST = (1 << 31) // host(1)
    static readonly RT_ROLE_PRESENTOR = (1 << 30) // presenter(1)
    static readonly RT_ROLE_ASSISTANT = (1 << 29) // assistant(4)
    static readonly RT_ROLE_ATTENDEE = (1 << 28) // attendee
    static readonly RT_ROLE_ANDROID = (1 << 27) // android
    static readonly RT_ROLE_AUDIENCE = (1 << 1) // audience
    static readonly RT_ROLE_H323 = (1 << 2) // H323 gateway
    static readonly RT_ROLE_TELEPHONE = (1 << 3) // pstn phone
    static readonly RT_ROLE_DVR = (1 << 4) // dvr user
    static readonly RT_ROLE_SIP = (1 << 5) // sip user
    static readonly RT_ROLE_HIDEATTENDEE = (1 << 6); // hide attendee

    static readonly RT_DEVICE_OTHER = (0)
    static readonly RT_DEVICE_HOST = (1) //mobile devices
    static readonly RT_DEVICE_SPEAKER = (1 << 2) //speaker
    static readonly RT_DEVICE_ASSISTANT = (1 << 4) //mobile devices*/

  static readonly RT_DEVICE_MOBILE = (1 << 8) //mobile devices
  static readonly RT_DEVICE_PC = (1 << 9) //PC
  static readonly RT_DEVICE_SUPERVISOR = (1 << 10) //supervisor
  static readonly RT_DEVICE_SITTERIN = (1 << 11) //sitter-in
  static readonly RT_DEVICE_FLASH = (1 << 12) //flash
  static readonly RT_DEVICE_AUDIENCE = (1 << 13) //audience
  static readonly RT_DEVICE_H323 = (1 << 14) //H323
  static readonly RT_DEVICE_TELEPHONE = (1 << 15) //pstn phone
  static readonly RT_DEVICE_DVR = (1 << 16) //dvr user
  static readonly RT_DEVICE_SIP = (1 << 17) //sip user
  static readonly RT_DEVICE_HIDEATTENDEE = (1 << 18) //hide attendee
  static readonly RT_DEVICE_MEETINGBOX = (1 << 20) //hide attendee
  static readonly RT_DEVICE_SUPERVISOR_MOBILEPC = (1 << 21) //hide attendee
  static readonly RT_DEVICE_CLOUDRECORD = (1 << 22) //hide attendee
  static readonly CONF_ROLE_APPLET = (1 << 23); // APPLET


  static readonly DEVICE_MOBILE = (1 << 8); //mobile devices
  static readonly DEVICE_PC = (1 << 9); //PC
  static readonly DEVICE_SUPERVISOR = (1 << 10); //supervisor
  static readonly DEVICE_SITTERIN = (1 << 11); //sitter-in
  static readonly DEVICE_FLASH = (1 << 12); //flash
  static readonly DEVICE_AUDIENCE = (1 << 13); //audience
  static readonly DEVICE_H323 = (1 << 14); //H323
  static readonly DEVICE_TELEPHONE = (1 << 15); //pstn phone
  static readonly DEVICE_DVR = (1 << 16); //dvr user
  static readonly DEVICE_SIP = (1 << 17); //sip user
  static readonly DEVICE_HIDEATTENDEE = (1 << 18); //hide attendee
  static readonly DEVICE_MEETINGBOX = (1 << 20); //hide attendee
  static readonly DEVICE_SUPERVISOR_MOBILEPC = (1 << 21); //hide attendee
  static readonly DEVICE_CLOUDRECORD = (1 << 22); //hide attendee
  static readonly DEVICE_APPLET = (1 << 23); //hide attendee

  //变换角色
  static readonly CHANGE_ROLE = 10010;
  // 联系人列表角色
  /**
   * 一般参会者
   */
  static readonly ROLE_OTHERS = 0;
  /**
   * 主持人
   */
  static readonly ROLE_HOST = 1;
  /**
   * 主讲人
   */
  static readonly ROLE_SPEAKER = 2;
  /**
   * 自己
   */
  static readonly ROLE_SELF = 3;
  /**
   * 助理
   */
  static readonly ROLE_ASSISTANT = 4;
  /**
   * 所有人
   */
  static readonly ALL_USER_ID = 0;

  /*// 聊天
  */
  /**
   * 接受消息
   */
  /*
    static readonly CHAT_RECEIVE = 3003;
    *//**
   * 打开关闭私聊
   */
  /*
    static readonly CHAT_PRIVATE_ON = 3100;
    *//**
   * 关闭私聊
   */
  /*
    static readonly CHAT_PRIVATE_OFF = 3101;
    *//**
   * 打开公聊
   */
  /*
    static readonly CHAT_PUBLIC_ON = 3102;
    *//**
   * 关闭公聊
   */
  /*
    static readonly CHAT_PUBLIC_OFF = 3103;
    static readonly AS_PRIVILIGE_ON = 3104;
    static readonly AS_PRIVILIGE_OFF = 3105;
    //公聊权限
    static readonly PUBLIC_CHAT_PRIVILIGE = 1;
    //私聊权限
    static readonly PRIVATE_CHAT_PRIVILIGE = 2;
    //音频权限
    static readonly AUDIO_PRIVILIGE = 3;
    //视频权限
    static readonly VIDEO_PRIVILIGE = 4;
    //注释权限
    static readonly ANNOTATION_PRIVILIGE = 5;
    //共享文档权限
    static readonly DOCSHARE_PRIVILIGE = 6;
    static readonly FILEUP_PRIVILIGE = 9;
    static readonly FILEDOWN_PRIVILIGE = 10;
    static readonly RENAME_PRIVILIGE = 11;
    static readonly AS_PRIVILIGE = 12;
    static readonly RECORD_PRIVILIGE = 13;
    // 用户
    static readonly ACTION_USER_ADD = 0;
    static readonly ACTION_USER_REMOVE = 1;
    static readonly ACTION_USER_MODIFY = 2;
    *//**
   * 修改角色
   */
  /*
    static readonly ROLEUPDATE = 3006; //
  */

  static getDeviceType(role: number): number {
    if ((role & UserCommon.RT_DEVICE_MOBILE) === UserCommon.RT_DEVICE_MOBILE) {
      return UserCommon.RT_DEVICE_MOBILE;
    } else if ((role & UserCommon.RT_DEVICE_PC) === UserCommon.RT_DEVICE_PC) {
      return UserCommon.RT_DEVICE_PC;
    } else if ((role & UserCommon.RT_DEVICE_SUPERVISOR) === UserCommon.RT_DEVICE_SUPERVISOR) {
      return UserCommon.RT_DEVICE_SUPERVISOR;
    } else if ((role & UserCommon.RT_DEVICE_SITTERIN) === UserCommon.RT_DEVICE_SITTERIN) {
      return UserCommon.RT_DEVICE_SITTERIN;
    } else if ((role & UserCommon.RT_DEVICE_FLASH) === UserCommon.RT_DEVICE_FLASH) {
      return UserCommon.RT_DEVICE_FLASH;
    } else if ((role & UserCommon.RT_DEVICE_AUDIENCE) === UserCommon.RT_DEVICE_AUDIENCE) {
      return UserCommon.RT_DEVICE_AUDIENCE;
    } else if ((role & UserCommon.RT_DEVICE_H323) === UserCommon.RT_DEVICE_H323) {
      return UserCommon.RT_DEVICE_H323;
    } else if ((role & UserCommon.RT_DEVICE_TELEPHONE) === UserCommon.RT_DEVICE_TELEPHONE) {
      return UserCommon.RT_DEVICE_TELEPHONE;
    } else if ((role & UserCommon.RT_DEVICE_DVR) === UserCommon.RT_DEVICE_DVR) {
      return UserCommon.RT_DEVICE_DVR;
    } else if ((role & UserCommon.RT_DEVICE_SIP) === UserCommon.RT_DEVICE_SIP) {
      return UserCommon.RT_DEVICE_SIP;
    } else if ((role & UserCommon.RT_DEVICE_HIDEATTENDEE) === UserCommon.RT_DEVICE_HIDEATTENDEE) {
      return UserCommon.RT_DEVICE_HIDEATTENDEE;
    } else if ((role & UserCommon.RT_DEVICE_MEETINGBOX) === UserCommon.RT_DEVICE_MEETINGBOX) {
      return UserCommon.RT_DEVICE_MEETINGBOX;
    } else if ((role & UserCommon.RT_DEVICE_SUPERVISOR_MOBILEPC) === UserCommon.RT_DEVICE_SUPERVISOR_MOBILEPC) {
      return UserCommon.RT_DEVICE_SUPERVISOR_MOBILEPC;
    } else if ((role & UserCommon.RT_DEVICE_CLOUDRECORD) === UserCommon.RT_DEVICE_CLOUDRECORD) {
      return UserCommon.RT_DEVICE_CLOUDRECORD;
    } else if ((role & UserCommon.CONF_ROLE_APPLET) === UserCommon.CONF_ROLE_APPLET) {
      return UserCommon.CONF_ROLE_APPLET;
    } else {
      return 0;
    }
  }

  static getUserRole(role: number): number {
    const deviceType = UserCommon.getDeviceType(role);
    const diff = role - deviceType;
    console.log('kkk getUserRole PC', deviceType, diff)
    if (diff === 0) {
      return UserCommon.ROLE_OTHERS;
    } else if (diff === 1) {
      return UserCommon.ROLE_HOST;
    } else if (diff === 2) {
      return UserCommon.ROLE_SPEAKER;
    } else if (diff === 3) {
      return UserCommon.ROLE_SELF;
    } else if (diff === 4) {
      return UserCommon.ROLE_ASSISTANT;
    } else {
      return UserCommon.ROLE_OTHERS;
    }
  }


  // static getDeviceType(role: number): string {
  //   if ((role & UserCommon.RT_DEVICE_MOBILE) === UserCommon.RT_DEVICE_MOBILE) {
  //     return "mobile";
  //   } else if ((role & UserCommon.RT_DEVICE_PC) === UserCommon.RT_DEVICE_PC) {
  //     return "pc";
  //   } else if ((role & UserCommon.RT_DEVICE_SUPERVISOR) === UserCommon.RT_DEVICE_SUPERVISOR) {
  //     return "supervisor";
  //   } else if ((role & UserCommon.RT_DEVICE_SITTERIN) === UserCommon.RT_DEVICE_SITTERIN) {
  //     return "sitter-in";
  //   } else if ((role & UserCommon.RT_DEVICE_FLASH) === UserCommon.RT_DEVICE_FLASH) {
  //     return "flash";
  //   } else if ((role & UserCommon.RT_DEVICE_AUDIENCE) === UserCommon.RT_DEVICE_AUDIENCE) {
  //     return "audience";
  //   } else if ((role & UserCommon.RT_DEVICE_H323) === UserCommon.RT_DEVICE_H323) {
  //     return "H323";
  //   } else if ((role & UserCommon.RT_DEVICE_TELEPHONE) === UserCommon.RT_DEVICE_TELEPHONE) {
  //     return "pstn phone";
  //   } else if ((role & UserCommon.RT_DEVICE_DVR) === UserCommon.RT_DEVICE_DVR) {
  //     return "dvr user";
  //   } else if ((role & UserCommon.RT_DEVICE_SIP) === UserCommon.RT_DEVICE_SIP) {
  //     return "sip user";
  //   } else if ((role & UserCommon.RT_DEVICE_HIDEATTENDEE) === UserCommon.RT_DEVICE_HIDEATTENDEE) {
  //     return "hide attendee";
  //   } else if ((role & UserCommon.RT_DEVICE_MEETINGBOX) === UserCommon.RT_DEVICE_MEETINGBOX) {
  //     return "meetingbox";
  //   } else if ((role & UserCommon.RT_DEVICE_SUPERVISOR_MOBILEPC) === UserCommon.RT_DEVICE_SUPERVISOR_MOBILEPC) {
  //     return "supervisor mobilepc";
  //   } else if ((role & UserCommon.RT_DEVICE_CLOUDRECORD) === UserCommon.RT_DEVICE_CLOUDRECORD) {
  //     return "cloud record";
  //   } else if ((role & UserCommon.CONF_ROLE_APPLET) === UserCommon.CONF_ROLE_APPLET) {
  //     return "APPLET";
  //   } else {
  //     return "unknown";
  //   }
  // }
}


// interface UserBean {
//   // 定义 UserBean 的属性和方法
// }

// export class UserConstants {
//   //设备类型
//   static readonly RT_DEVICE_MOBILE: number = 1 << 8;
//   static readonly RT_DEVICE_PC: number = 1 << 9;
//   static readonly RT_DEVICE_HIDEATTENDEE: number = 1 << 18;
// }
//
// export const RT_DEVICE_MOBILE: number = 1 << 8;
//
// export const RT_DEVICE_PC: number = 1 << 9;
//
// export const RT_DEVICE_HIDEATTENDEE: number = 1 << 18;
//
// export const RT_DEVICE_HOST: number = 1
//
// interface UserCommon {}

// export interface UserCommon {
//   // 角色常量
//   readonly RT_ROLE_HOST: number; // host(1)
//   readonly RT_ROLE_PRESENTOR: number; // presenter(1)
//   readonly RT_ROLE_ASSISTANT: number; // assistant(4)
//   readonly RT_ROLE_ATTENDEE: number; // attendee
//   readonly RT_ROLE_ANDROID: number; // android
//   readonly RT_ROLE_AUDIENCE: number; // audience
//   readonly RT_ROLE_H323: number; // H323 gateway
//   readonly RT_ROLE_TELEPHONE: number; // pstn phone
//   readonly RT_ROLE_DVR: number; // dvr user
//   readonly RT_ROLE_SIP: number; // sip user
//   readonly RT_ROLE_HIDEATTENDEE: number; // hide attendee
//
//   // 设备常量
//   readonly RT_DEVICE_OTHER: number;
//   readonly RT_DEVICE_HOST: number; // mobile devices
//   readonly RT_DEVICE_SPEAKER: number; // speaker
//   readonly RT_DEVICE_ASSISTANT: number; // mobile devices
//   readonly RT_DEVICE_MOBILE: number; // mobile devices
//   readonly RT_DEVICE_PC: number; // PC
//   readonly RT_DEVICE_SUPERVISOR: number; // supervisor
//   readonly RT_DEVICE_SITTERIN: number; // sitter-in
//   readonly RT_DEVICE_FLASH: number; // flash
//   readonly RT_DEVICE_AUDIENCE: number; // audience
//   readonly RT_DEVICE_H323: number; // H323
//   readonly RT_DEVICE_TELEPHONE: number; // pstn phone
//   readonly RT_DEVICE_DVR: number; // dvr user
//   readonly RT_DEVICE_SIP: number; // sip user
//   readonly RT_DEVICE_HIDEATTENDEE: number; // hide attendee
//   readonly RT_DEVICE_MEETINGBOX: number; // meeting box
//   readonly RT_DEVICE_SUPERVISOR_MOBILEPC: number; // supervisor mobile/pc
//   readonly RT_DEVICE_CLOUDRECORD: number; // cloud record
//   readonly CONF_ROLE_APPLET: number; // applet
//
//   // 设备常量（重复部分）
//   readonly DEVICE_MOBILE: number; // mobile devices
//   readonly DEVICE_PC: number; // PC
//   readonly DEVICE_SUPERVISOR: number; // supervisor
//   readonly DEVICE_SITTERIN: number; // sitter-in
//   readonly DEVICE_FLASH: number; // flash
//   readonly DEVICE_AUDIENCE: number; // audience
//   readonly DEVICE_H323: number; // H323
//   readonly DEVICE_TELEPHONE: number; // pstn phone
//   readonly DEVICE_DVR: number; // dvr user
//   readonly DEVICE_SIP: number; // sip user
//   readonly DEVICE_HIDEATTENDEE: number; // hide attendee
//   readonly DEVICE_MEETINGBOX: number; // meeting box
//   readonly DEVICE_SUPERVISOR_MOBILEPC: number; // supervisor mobile/pc
//   readonly DEVICE_CLOUDRECORD: number; // cloud record
//   readonly DEVICE_APPLET: number; // applet
//
//   // 角色常量
//   readonly CHANGE_ROLE: number; // 变换角色
//   readonly ROLE_OTHERS: number; // 一般参会者
//   readonly ROLE_HOST: number; // 主持人
//   readonly ROLE_SPEAKER: number; // 主讲人
//   readonly ROLE_SELF: number; // 自己
//   readonly ROLE_ASSISTANT: number; // 助理
//   readonly ALL_USER_ID: number; // 所有人
//
//   // 聊天常量
//   readonly CHAT_RECEIVE: number; // 接受消息
//   readonly CHAT_PRIVATE_ON: number; // 打开私聊
//   readonly CHAT_PRIVATE_OFF: number; // 关闭私聊
//   readonly CHAT_PUBLIC_ON: number; // 打开公聊
//   readonly CHAT_PUBLIC_OFF: number; // 关闭公聊
//   readonly AS_PRIVILIGE_ON: number; // 打开助理权限
//   readonly AS_PRIVILIGE_OFF: number; // 关闭助理权限
//
//   // 权限常量
//   readonly PUBLIC_CHAT_PRIVILIGE: number; // 公聊权限
//   readonly PRIVATE_CHAT_PRIVILIGE: number; // 私聊权限
//   readonly AUDIO_PRIVILIGE: number; // 音频权限
//   readonly VIDEO_PRIVILIGE: number; // 视频权限
//   readonly ANNOTATION_PRIVILIGE: number; // 注释权限
//   readonly DOCSHARE_PRIVILIGE: number; // 共享文档权限
//   readonly FILEUP_PRIVILIGE: number; // 文件上传权限
//   readonly FILEDOWN_PRIVILIGE: number; // 文件下载权限
//   readonly RENAME_PRIVILIGE: number; // 重命名权限
//   readonly AS_PRIVILIGE: number; // 助理权限
//   readonly RECORD_PRIVILIGE: number; // 录制权限
//
//   // 用户操作常量
//   readonly ACTION_USER_ADD: number; // 添加用户
//   readonly ACTION_USER_REMOVE: number; // 移除用户
//   readonly ACTION_USER_MODIFY: number; // 修改用户
//   readonly ROLEUPDATE: number; // 修改角色
//
//   // 方法定义
//   onRosterInfoUpdate(user: UserBean, action: number): void;
//   onChangeUserRole(uid: number, newRole: number): void;
//   onUpdateUserPriviledge(type: number, state: boolean): void;
//   onChangeAudioState(userID: number, isHaveDev: boolean, isOpenDev: boolean): void;
//   onChangeVideoState(uid: number, isOpen: boolean): void;
//   isHaveVideo(uid: number, isRemove: boolean): void;
//   getOwnID(): number;
//   setUser2Host(uid: number): void;
//   userRole2Assistant(uid: number): void;
//   userRole2Presentor(uid: number): void;
//   userRole2Attendee(uid: number): void;
//   handUp(uid: number, handUp: boolean): void;
// }

// 实现常量
// const userCommonConstants: UserCommon = {
//   RT_ROLE_HOST: 1 << 31,
//   RT_ROLE_PRESENTOR: 1 << 30,
//   RT_ROLE_ASSISTANT: 1 << 29,
//   RT_ROLE_ATTENDEE: 1 << 28,
//   RT_ROLE_ANDROID: 1 << 27,
//   RT_ROLE_AUDIENCE: 1 << 1,
//   RT_ROLE_H323: 1 << 2,
//   RT_ROLE_TELEPHONE: 1 << 3,
//   RT_ROLE_DVR: 1 << 4,
//   RT_ROLE_SIP: 1 << 5,
//   RT_ROLE_HIDEATTENDEE: 1 << 6,
//
//   RT_DEVICE_OTHER: 0,
//   RT_DEVICE_HOST: 1,
//   RT_DEVICE_SPEAKER: 1 << 2,
//   RT_DEVICE_ASSISTANT: 1 << 4,
//   RT_DEVICE_MOBILE: 1 << 8,
//   RT_DEVICE_PC: 1 << 9,
//   RT_DEVICE_SUPERVISOR: 1 << 10,
//   RT_DEVICE_SITTERIN: 1 << 11,
//   RT_DEVICE_FLASH: 1 << 12,
//   RT_DEVICE_AUDIENCE: 1 << 13,
//   RT_DEVICE_H323: 1 << 14,
//   RT_DEVICE_TELEPHONE: 1 << 15,
//   RT_DEVICE_DVR: 1 << 16,
//   RT_DEVICE_SIP: 1 << 17,
//   RT_DEVICE_HIDEATTENDEE: 1 << 18,
//   RT_DEVICE_MEETINGBOX: 1 << 20,
//   RT_DEVICE_SUPERVISOR_MOBILEPC: 1 << 21,
//   RT_DEVICE_CLOUDRECORD: 1 << 22,
//   CONF_ROLE_APPLET: 1 << 23,
//
//   DEVICE_MOBILE: 1 << 8,
//   DEVICE_PC: 1 << 9,
//   DEVICE_SUPERVISOR: 1 << 10,
//   DEVICE_SITTERIN: 1 << 11,
//   DEVICE_FLASH: 1 << 12,
//   DEVICE_AUDIENCE: 1 << 13,
//   DEVICE_H323: 1 << 14,
//   DEVICE_TELEPHONE: 1 << 15,
//   DEVICE_DVR: 1 << 16,
//   DEVICE_SIP: 1 << 17,
//   DEVICE_HIDEATTENDEE: 1 << 18,
//   DEVICE_MEETINGBOX: 1 << 20,
//   DEVICE_SUPERVISOR_MOBILEPC: 1 << 21,
//   DEVICE_CLOUDRECORD: 1 << 22,
//   DEVICE_APPLET: 1 << 23,
//
//   CHANGE_ROLE: 10010,
//   ROLE_OTHERS: 0,
//   ROLE_HOST: 1,
//   ROLE_SPEAKER: 2,
//   ROLE_SELF: 3,
//   ROLE_ASSISTANT: 4,
//   ALL_USER_ID: 0,
//
//   CHAT_RECEIVE: 3003,
//   CHAT_PRIVATE_ON: 3100,
//   CHAT_PRIVATE_OFF: 3101,
//   CHAT_PUBLIC_ON: 3102,
//   CHAT_PUBLIC_OFF: 3103,
//   AS_PRIVILIGE_ON: 3104,
//   AS_PRIVILIGE_OFF: 3105,
//
//   PUBLIC_CHAT_PRIVILIGE: 1,
//   PRIVATE_CHAT_PRIVILIGE: 2,
//   AUDIO_PRIVILIGE: 3,
//   VIDEO_PRIVILIGE: 4,
//   ANNOTATION_PRIVILIGE: 5,
//   DOCSHARE_PRIVILIGE: 6,
//   FILEUP_PRIVILIGE: 9,
//   FILEDOWN_PRIVILIGE: 10,
//   RENAME_PRIVILIGE: 11,
//   AS_PRIVILIGE: 12,
//   RECORD_PRIVILIGE: 13,
//
//   ACTION_USER_ADD: 0,
//   ACTION_USER_REMOVE: 1,
//   ACTION_USER_MODIFY: 2,
//   ROLEUPDATE: 3006,
//
//   // 方法实现（需要根据具体逻辑实现）
//   onRosterInfoUpdate(user: UserBean, action: number): void {
//     // 实现逻辑
//   },
//   onChangeUserRole(uid: number, newRole: number): void {
//     // 实现逻辑
//   },
//   onUpdateUserPriviledge(type: number, state: boolean): void {
//     // 实现逻辑
//   },
//   onChangeAudioState(userID: number, isHaveDev: boolean, isOpenDev: boolean): void {
//     // 实现逻辑
//   },
//   onChangeVideoState(uid: number, isOpen: boolean): void {
//     // 实现逻辑
//   },
//   isHaveVideo(uid: number, isRemove: boolean): void {
//     // 实现逻辑
//   },
//   getOwnID(): number {
//     // 实现逻辑
//     return 0;
//   },
//   setUser2Host(uid: number): void {
//     // 实现逻辑
//   },
//   userRole2Assistant(uid: number): void {
//     // 实现逻辑
//   },
//   userRole2Presentor(uid: number): void {
//     // 实现逻辑
//   },
//   userRole2Attendee(uid: number): void {
//     // 实现逻辑
//   },
//   handUp(uid: number, handUp: boolean): void {
//     // 实现逻辑
//   },
// };

// export default userCommonConstants;
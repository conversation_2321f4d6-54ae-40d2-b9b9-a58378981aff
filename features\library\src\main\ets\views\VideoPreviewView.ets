import { VideoComponent } from '../components/VideoComponent'
import { Participant, ParticipantClass } from '../models'
import { VideoView } from '../pages/VideoView'
import {
  addVideoClose,
  addVideoOnNewVideo,
  removeVideoClose,
  removeVideoOnNewVideo
} from '../common/infowarelabsdk/video'

interface OpenVideoParticipant {
  uid: number
  name: string
  channelId: number
}


// 视频预览组件
@Component
export struct VideoPreviewView {
  //用户个人信息
  @Prop userData: Participant
  @Prop rotation: number
  //当前会议的参会人员
  @Prop participants: ParticipantClass[]
  // 开启视频的参会人员
  @State openVideoUser: OpenVideoParticipant[] = []
  @State currentSwiperIndex: number = 0
  @Consume cameraIndex: number
  // @Prop currentCameraIndex: number // Add this state to track camera index
  onVideoPreviewClick: () => void = () => {
  }
  videoOpenClick: (index: number, isOpen: boolean) => void = () => {
  }

  aboutToAppear(): void {
    addVideoOnNewVideo(this.onNewVideo) //新增视频回调
    addVideoClose(this.videoClose) //视频关闭回调
  }

  aboutToDisappear() {
    // 清理参与者数组
    this.participants = [];
    removeVideoOnNewVideo(this.onNewVideo)
    removeVideoClose(this.videoClose)
  }

  // 获取标准化的用户ID（应用掩码，去除模块标识）
  getNormalizedUserId(userId: number | undefined): number {
    return userId !== undefined ? userId & 0xffffff00 : 0;
  }

  // 视频开启回调
  onNewVideo = (nUserId: number, nChannelId: number) => {
    console.log('kkkk onNewVideo', nUserId, nChannelId)
    const normalizedUserId = this.getNormalizedUserId(nUserId);
    const index = this.participants.findIndex((participant: Participant) =>
    this.getNormalizedUserId(participant.uid) === normalizedUserId
    )
    if (index !== -1) {
      this.videoOpenClick(index, true) //参会者列表视频开启
    }
    const index1 = this.openVideoUser.findIndex(participant => participant.channelId === nChannelId)

    if (index1 === -1) {
      try {
        this.openVideoUser.push({
          uid: nUserId,
          name: this.participants[index]?.nickname || '',
          channelId: nChannelId
        })
      } catch (error) {
        console.error('添加视频用户失败:', error)
      }
    }
  }
  // 视频关闭回调
  videoClose = (nChannelId: number, bReleaseDecoder: boolean) => {
    console.log('kkkk onCloseVideo', 'channelid', nChannelId)
    const index1 = this.participants.findIndex(participant => participant.channelId === nChannelId)
    this.videoOpenClick(index1, false) //会话列表视频关闭
    const index = this.openVideoUser.findIndex(participant => participant.channelId === nChannelId)
    this.openVideoUser.splice(index, 1)
  }

  build() {
    RelativeContainer() {
      if (this.openVideoUser.length === 0) {
        this.participantItemBuilder(this.userData.nickname!)
      } else {
        Grid() {
          ForEach(this.openVideoUser, (participant: OpenVideoParticipant, index) => {
            GridItem() {
              RelativeContainer() {
                if (this.getNormalizedUserId(participant.uid) === this.getNormalizedUserId(this.userData.uid)) {
                  if (this.cameraIndex === 1) {
                    this.cameraBuilder(1)
                  } else {
                    this.cameraBuilder(0)
                  }
                } else {
                  VideoComponent({ surfaceId: participant.channelId?.toString() })
                }

                Text(participant.name)
                  .fontSize(14)
                  .fontColor(Color.White)
                  .backgroundColor('#80000000')
                  .alignRules({
                    left: { anchor: "__container__", align: HorizontalAlign.Start },
                    bottom: { anchor: "__container__", align: VerticalAlign.Bottom }
                  })
              }
              .width(this.openVideoUser.length <= 1 ? '100%' : '50%')
              .backgroundColor('#ff222728')

              // .height('50%')
            }
          }, (participant: OpenVideoParticipant, index) => participant.channelId.toString())
        }
        .rowsTemplate(this.openVideoUser.length <= 2 ? '1fr' : '1fr 1fr')
        .width('100%')
        .height('100%')

      }


    }
    .width('100%')
    .height('100%')
    .backgroundColor('#1A1B1F')
    .onClick(() => {
      console.log('kkkk ', this.openVideoUser.length, JSON.stringify(this.openVideoUser))
      this.onVideoPreviewClick()
    })
  }

  @Builder
  participantItemBuilder(name: string) {
    Column({ space: 10 }) {
      Image($r('app.media.ic_vs_portrait'))
        .width(60)
        .aspectRatio(1)

      Text(name)
        .fontSize(16)
        .fontColor('#FFFFFFFF')
    }
    .alignRules({
      middle: { anchor: "__container__", align: HorizontalAlign.Center },
      center: { anchor: "__container__", align: VerticalAlign.Center }
    })
  }

  @Builder
  cameraBuilder(cameraIndex: number) {
    VideoView({ cameraIndex, rotation: this.rotation })
      .zIndex(2)
      .id(cameraIndex === 1 ? 'frontVideoView' : 'backVideoView')
      .alignRules({
        middle: { anchor: "__container__", align: HorizontalAlign.Center },
        center: { anchor: "__container__", align: VerticalAlign.Center }
      })
  }
}
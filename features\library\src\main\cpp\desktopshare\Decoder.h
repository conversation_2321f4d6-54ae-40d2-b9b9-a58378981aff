#ifndef VIDEO_CODEC_Decoder_H
#define VIDEO_CODEC_Decoder_H

#include "capbilities/include/VideoDecoder.h"
#include "common/SampleInfo.h"
#include <bits/alltypes.h>
#include <mutex>
#include <memory>
#include <atomic>
#include <thread>
#include <unistd.h>
#include <ohaudio/native_audiorenderer.h>
#include <ohaudio/native_audiostreambuilder.h>
#include <fstream>

class Decoder {
public:
    Decoder() : isInitialized_(false), isScreenShareInitialized_(false) {};
    ~Decoder();

    static Decoder &GetInstance() {
        static Decoder decoder;
        return decoder;
    }

    int32_t Init(SampleInfo &sampleInfo);
    int32_t InitScreenShare(SampleInfo &sampleInfo);
    int32_t Start();
    int32_t StartScreenShare();
    CodecUserData* GetVideoDecContext() { return videoDecContext_; }
    CodecUserData* GetScreenShareDecContext() { return screenShareDecContext_; }
    bool IsInitialized() const { return isInitialized_; }
    bool IsScreenShareInitialized() const { return isScreenShareInitialized_; }
    VideoDecoder* GetVideoDecoder() { return videoDecoder_.get(); }
    VideoDecoder* GetScreenShareDecoder() { return screenShareDecoder_.get(); }

private:
    void VideoDecInputThread();
    void VideoDecOutputThread();
    void AudioDecInputThread();
    void AudioDecOutputThread();
    void Release();
    void StartRelease();
    void ReleaseThread();
//    int32_t CreateAudioDecoder();
    int32_t CreateVideoDecoder();
    void ScreenShareDecInputThread();
    void ScreenShareDecOutputThread();
    int32_t CreateScreenShareDecoder();

    std::unique_ptr<VideoDecoder> videoDecoder_ = nullptr;
//    std::shared_ptr<AudioDecoder> audioDecoder_ = nullptr;
//    std::unique_ptr<Demuxer> demuxer_ = nullptr;

    std::mutex mutex_;
    std::atomic<bool> isStarted_{false};
    std::atomic<bool> isReleased_{false};
    std::unique_ptr<std::thread> videoDecInputThread_ = nullptr;
    std::unique_ptr<std::thread> videoDecOutputThread_ = nullptr;
    std::unique_ptr<std::thread> audioDecInputThread_ = nullptr;
    std::unique_ptr<std::thread> audioDecOutputThread_ = nullptr;
    std::condition_variable doneCond_;
    SampleInfo sampleInfo_;
    CodecUserData *videoDecContext_ = nullptr;
    CodecUserData *audioDecContext_ = nullptr;
    OH_AudioStreamBuilder *builder_ = nullptr;
    OH_AudioRenderer *audioRenderer_ = nullptr;
    static constexpr int64_t MICROSECOND = 1000000;
    std::atomic<bool> isInitialized_{false};
    std::unique_ptr<VideoDecoder> screenShareDecoder_ = nullptr;
    CodecUserData *screenShareDecContext_ = nullptr;
    std::unique_ptr<std::thread> screenShareDecInputThread_ = nullptr;
    std::unique_ptr<std::thread> screenShareDecOutputThread_ = nullptr;
    std::atomic<bool> isScreenShareInitialized_{false};
    SampleInfo screenShareSampleInfo_;
};

#endif // VIDEO_CODEC_Decoder_H
{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"basic@../../commons/basic": "basic@../../commons/basic", "libcapture.so@src/main/cpp/types/capture": "libcapture.so@src/main/cpp/types/capture", "libdesktopshare.so@src/main/cpp/types/desktopshare": "libdesktopshare.so@src/main/cpp/types/desktopshare", "libencoder.so@src/main/cpp/types/encoder": "libencoder.so@src/main/cpp/types/encoder", "libentry.so@src/main/cpp/types/libentry": "libentry.so@src/main/cpp/types/libentry", "libplayer.so@src/main/cpp/types/decoder": "libplayer.so@src/main/cpp/types/decoder"}, "packages": {"basic@../../commons/basic": {"name": "basic", "version": "1.0.0", "resolved": "../../commons/basic", "registryType": "local", "packageType": "InterfaceHar"}, "libcapture.so@src/main/cpp/types/capture": {"name": "libcapture.so", "version": "1.0.0", "resolved": "src/main/cpp/types/capture", "registryType": "local"}, "libdesktopshare.so@src/main/cpp/types/desktopshare": {"name": "libdesktopshare.so", "version": "1.0.0", "resolved": "src/main/cpp/types/desktopshare", "registryType": "local"}, "libencoder.so@src/main/cpp/types/encoder": {"name": "libencoder.so", "version": "1.0.0", "resolved": "src/main/cpp/types/encoder", "registryType": "local"}, "libentry.so@src/main/cpp/types/libentry": {"name": "libentry.so", "version": "1.0.0", "resolved": "src/main/cpp/types/libentry", "registryType": "local"}, "libplayer.so@src/main/cpp/types/decoder": {"name": "libplayer.so", "version": "1.0.0", "resolved": "src/main/cpp/types/decoder", "registryType": "local"}}}
import AVCastPicker from '@ohos.multimedia.avCastPicker';
import AVSessionManager from '@ohos.multimedia.avsession';

@Component
export struct NavBar {
  @Prop title: string = '参会者'
  @Prop leftText: string = ''
  @Prop rightText: string = ''
  @Prop isShowBar: boolean = true
  @Prop currentPageIndex: number = 1
  leftClick: () => void = () => {
  }
  rightClick: () => void = () => {

  }
  shareClick: () => void = () =>{

  }
  @BuilderParam leftIcon: () => void
  leftIconClick: () => void = () => {
  }

  async aboutToAppear() {
    // 创建session  -  激活接口要在元数据、控制命令注册完成之后再执行
    (await AVSessionManager.createAVSession(getContext(this), 'SESSION_NAME', 'video_call')).activate();
  }

  build() {
    // 顶部导航栏 - 在聊天页面时隐藏

    RelativeContainer() {
      Column() {
        AVCastPicker({ normalColor: Color.White })
      }
      .height('100%')
      .aspectRatio(1)
      .offset({ x: 96, y: 0 })
      .alignRules({
        top: { anchor: "__container__", align: VerticalAlign.Top },
        left: { anchor: "__container__", align: HorizontalAlign.Start }
      })

      if (this.leftText) {
        Text(this.leftText)
          .fontSize(16)
          .fontColor('#FFFFFF')
          .height('100%')
          .padding({ left: 16, right: 16 })//我的右上角对齐父组件的右上角
          .alignRules({
            top: { anchor: "__container__", align: VerticalAlign.Top },
            left: { anchor: "__container__", align: HorizontalAlign.Start }
          })
          .onClick(() => {
            this.leftClick()
          })
      }

      if (this.leftIcon) {
        Column() {
          this.leftIcon()
        }
        .padding({ left: 16, right: 16 })
        .onClick(() => {
          this.leftIconClick()
        })
      }
      Text(this.title)
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#FFFFFF')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)//我的中间横轴中间对齐父组件的中间，竖轴中间对齐父组件的中间
        .alignRules({
          center: { anchor: "__container__", align: VerticalAlign.Center },
          middle: { anchor: "__container__", align: HorizontalAlign.Center }
        })
      if (this.rightText) {
        Text(this.rightText)
          .fontSize(16)
          .fontColor('#FF0000')
          .height('100%')
          .padding({ left: 16, right: 16 })//我的左上角对齐父组件的右上角
          .alignRules(
            {
              top: { anchor: "__container__", align: VerticalAlign.Top },
              end: { anchor: "__container__", align: HorizontalAlign.End }
            }
          )
          .onClick(() => {
            this.rightClick()
          })
      }
      Image($r('app.media.ic_share'))
        .width(30)
        .fillColor(Color.White)
        .alignRules({
          center: { anchor: "__container__", align: VerticalAlign.Center },
          middle: { anchor: "__container__", align: HorizontalAlign.Center }
        })
        .offset({
          x:70
        })
        .onClick(()=>{
          this.shareClick()
        })
    }
    .width('100%')
    .height(48)
    .backgroundColor(this.currentPageIndex === 0 ? '#801A1B1F' : '#ff1A1B1F')
    .alignRules(
      this.isShowBar ?
        {
          top: { anchor: "__container__", align: VerticalAlign.Top },
          left: { anchor: "__container__", align: HorizontalAlign.Start }
        } :
        {
          top: { anchor: "__container__", align: VerticalAlign.Top },
          left: { anchor: "__container__", align: HorizontalAlign.End }
        }
    )
  }
}
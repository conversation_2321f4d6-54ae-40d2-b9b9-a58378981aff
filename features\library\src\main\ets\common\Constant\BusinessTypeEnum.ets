export enum BusinessType{
  // Conference 相关回调
  CONFERENCE_ON_CALL_ATT = "conference_onCallAtt",
  CONFERENCE_ON_CALLING_RESPONSE_ATT = "conference_onCallingResponseAtt",
  CONFERENCE_ON_USER_VIEW_STATE = "conference_onUserViewState",
  CONFERENCE_ON_USER_LAYOUT = "conference_onUserLayout",
  CONFERENCE_ON_AS_PRIVILEDGE = "conference_onAsPriviledge",
  CONFERENCE_ON_LIVE_STATE = "conference_onliveState",
  CONFERENCE_ON_CONF_MODE = "conference_onConfMode",
  CONFERENCE_ON_JOIN_CONFERENCE = "conference_onJoinConference",
  CONFERENCE_ON_LEAVE_CONFERENCE = "conference_onLeaveConference",
  CONFERENCE_ON_RECONNECT_SERVER = "conference_onReconnectServer",
  CONFERENCE_ON_ROSTER_INFO_UPDATE = "conference_onRosterInfoUpdate",
  CONFERENCE_ON_UPDATE_USER_PRIVILEDGE = "conference_onUpdateUserPriviledge",
  CONFERENCE_ON_BEGIN_RECORD_FAILED = "conference_onBeginRecordFailed",
  CONFERENCE_ON_RECORD_NOTIFY = "conference_onRecordNotify",
  CONFERENCE_ON_INVITE_PHONE_CONFIRM = "conference_onInvitePhoneConfirm",
  CONFERENCE_ON_INVITEH323_RESPONSE = "conference_onInviteh323Response",
  CONFERENCE_ON_CONFERENCE_SUPPORT_H323 = "conference_onConferenceSupportH323",
  CONFERENCE_ON_USER_ROLE_UPDATE = "conference_onUserRoleUpdate",
  CONFERENCE_ON_TRANSPARENT_RECEIVE_DATA = "conference_onTransparent_ReceiveData",
  CONFERENCE_ON_RECORD_STATE_RESPONSE = "conference_onRecordStateResponse",
  CONFERENCE_ON_RECORD_STOP_REQUEST = "conference_onRecordStopRequest",
  CONFERENCE_ON_SUBTITLES = "conference_onSubtitles",
  CONFERENCE_ON_FIRST_JOIN = "conference_onFirstJoin",
  CONFERENCE_ON_HAND_UP = "conference_onHandUp",

  // Chat 相关回调
  CHAT_ON_CHANNEL_READY = "chat_onChannelReady",
  CHAT_ON_RECEIVE_DATA = "chat_onReceiveData",
  CHAT_ON_RECEIVE_PERMISSION = "chat_onReceivePermission",

  // Audio 相关回调
  AUDIO_ON_OPEN_AUDIO_CONFIRM = "audio_onOpenAudioConfirm",
  AUDIO_ON_UPDATE_DEVICE_STATUS = "audio_onUpdateDeviceStatus",
  AUDIO_ON_CONFERENCE_SUPPORT_H323 = "audio_onConferenceSupportH323",
  AUDIO_ON_INVITE_H323_RESPONSE = "audio_onInviteH323Response",
  AUDIO_ON_SYNC_VOIP_CODEC = "audio_onSyncVoipCodec",
  AUDIO_ON_AUDIO_PARAM_CHANGED = "audio_onAudioParamChanged",
  AUDIO_ON_MAX_VOICE = "audio_onMaxVoice",
  AUDIO_ON_ACTIVE_SPEAKER = "audio_onActiveSpeaker",
  AUDIO_ON_LOCAL_PCM_DATA = "audio_onLocalPcmData",

  // Video 相关回调
  VIDEO_ON_VIDEO_DATA = "video_onVideoData",
  VIDEO_ON_OPEN_VIDEO = "video_onOpenVideo",
  VIDEO_ON_NEW_VIDEO = "video_onNewVideo",
  VIDEO_ON_VIDEO_CLOSE = "video_onVideoClose",
  VIDEO_ON_DECODER_RELEASE = "video_onDecoderRelease",
  VIDEO_ON_DEVICE_UPDATE = "video_onDeviceUpdate",
  VIDEO_ON_RESOLUTION_CHANGED = "video_onResolutionChanged",
  VIDEO_ON_DECODER_RESOLUTION_CHANGED = "video_onDecoderResolutionChanged",
  VIDEO_ON_CHANNEL_LIVE_UPDATE = "video_onChannelLiveUpdate",
  VIDEO_ON_SWITCH_TO_SOFT_DECODE = "video_onSwitchToSoftDecode",
  VIDEO_ON_SWITCH_TO_SOFT_ENCODE = "video_onSwitchToSoftEncode",
  VIDEO_ON_ENROLL_COMPLETED = "video_onEnrollCompleted",
  VIDEO_ON_NEED_CHANGE_SVC_LEVEL = "video_onNeedChangeSVClevel",
  VIDEO_ON_FORCE_KEY_FRAME = "video_onForceKeyFrame",
  VIDEO_ON_SYNC_LAYOUT = "video_onSyncLayout",
  VIDEO_ON_FULLSCREEN = "video_onFullscreen",
  VIDEO_ON_PTZ = "video_onPTZ",
  VIDEO_ON_LOOP_VIDEO = "video_onLoopVideo",

  // AS 相关回调
  AS_ON_START_DESKTOP_BROWSER = "as_onStartDesktop_Browser",
  AS_ON_STOP_DESKTOP_BROWSER = "as_onStopDesktop_Browser",
  AS_ON_INITIALIZE_BROWSER = "as_onInitialize_Browser",
  AS_ON_ASCHANNEL_STATE_CHANGE = "as_onAschannelStateChange",
  AS_ON_RECIEVE_DATA_BROWSER = "as_onRecieveData_Browser",
  AS_ON_APPLY_DESKTOP_CONTROL = "as_onApplyDesktopControl",
  AS_ON_APPLY_DESKTOP_CONTROL_RQST = "as_onApplyDesktopControlRqst",
  AS_ON_APPLY_DESKTOP_GRANT_CTRL_RQST = "as_onApplyDeskTopGrantCtrlRqst",
  AS_ON_CANCLE_APPLY_DESKTOP_CTRL_RQST = "as_onCancleApplyDeskTopCtrlRqst",
  AS_ON_APPLY_CANCLE_CTRL = "as_onApplyCancleCtrl",
  AS_ON_ADMISSION_CONTROL = "as_onAdmissionControl",
  AS_ON_AS_SHARE_FAILURE = "as_onAsShareFailure",
  AS_ON_AS_SHARE_CHANGE_DECODEM_METHOD = "as_onAsShareChangeDecodemMethod",
  AS_ON_RECEIVE_DATA_AUDIO = "as_onReceiveData_Audio",

  //DS 相关回调
  DS_ON_SHARE_DOC = "ds_onShareDoc",
  DS_ON_SWITCH_DOC = "ds_onSwitchDoc",
  DS_ON_CLOSE_DOC = "ds_onCloseDoc",
  DS_ON_NEW_PAGE = "ds_onNewPage",
  DS_ON_NEW_PAGE_STEP = "ds_onNewPageStep",
  DS_ON_SWITCH_PAGE = "ds_onSwitchPage",
  DS_ON_PAGE_DATA = "ds_onPageData",
  DS_ON_PAGE_STEP_DATA = "ds_onPageStepData",
  DS_ON_SWITCH_PAGE_STEP = "ds_onSwitchPageStep",
  DS_ON_MOVE_PAGE = "ds_onMovePage",
  DS_ON_NEW_ANT = "ds_onNewAnt",
  DS_ON_REMOVE_ANT = "ds_onRemoveAnt",
  DS_ON_REMOVE_MY_ANT = "ds_onRemoveMyAnt",
  DS_ON_REMOVE_ALL_ANT = "ds_onRemoveAllAnt",
}
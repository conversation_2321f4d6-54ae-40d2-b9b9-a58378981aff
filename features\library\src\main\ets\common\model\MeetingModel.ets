// 会议参数接口
export interface MeetingConfig {
  // 基本参数
  meetingId: string // 会议号(必需)
  displayName: string // 显示名称(必需)
  password?: string // 会议密码(可选)
  confType?: number
  role?: number
  siteUrl?: string
  // 音视频参数
  openAudio?: boolean // 是否开启音频
  openVideo?: boolean // 是否开启视频

  // 用户信息
  userId?: number // 用户ID
  siteId?: string // 站点ID
  userName?: string // 用户名

  // 服务器配置
  serverIP?: string // 服务器IP
  serverPort?: number // 服务器端口
  useSSL?: boolean // 是否使用SSL
}

// 参会者信息接口
export interface Participant {
  id: number
  name: string
  isHost: boolean
  isAudioOn: boolean
  isVideoOn: boolean
  isHandUp: boolean
}

// 聊天消息接口
export interface ChatMessage {
  id: number
  senderId: number
  senderName: string
  content: string
  time: string
  isPrivate: boolean
}

export interface MessageModel {
  uid: number // 发送者的ID (必需)
  username: string // 发送者的名字 (必需)
  message: string // 发送的消息的内容 (必需)
  isPublic: boolean // 是否公聊 (必需)
}

export interface SendChatMessage {
  targetUid: number,
  content: string,
  username: string,
  isPublic: boolean
}

export interface MeetingReqModel {
  userId: number,
  siteId: string,
  userName: string,
  email: string,
  confId: string,
  confType: number,
  serviceType: number,
  confPassword: string,
  os: number,
  token: string,
  confSiteName: string
}

export interface CurrentUserModel {
  action: number,
  uid: number,
  name: string,
  role: number,
  isMobile ?: boolean,
  isPc ?: boolean,
  isHide ?: boolean
}



#include <bits/alltypes.h>
#include <hilog/log.h>
#include <thread>
#include "capbilities/include/VideoEncoder.h"
#include "common/dfx/error/AVCodecSampleError.h"
#include "common/dfx/log//AVCodecSampleLog.h"
#include "encoder/Encorder.h"

#include "VideoData.h"

#undef LOG_TAG

namespace {
using namespace std::chrono_literals;
constexpr int64_t MICROSECOND = 1000000;
} // namespace

Encoder::~Encoder() { StartRelease(); }


int32_t Encoder::Init(SampleInfo &sampleInfo) {
    std::lock_guard<std::mutex> lock(mutex_);
   

    sampleInfo_ = sampleInfo;

    videoEncoder_ = std::make_unique<VideoEncoder>();

    int32_t ret = videoEncoder_->Create(sampleInfo_.videoCodecMime);
   

    encContext_ = new CodecUserData;
    ret = videoEncoder_->Config(sampleInfo_, encContext_);


    sampleInfo.window = sampleInfo_.window;

//    releaseThread_ = nullptr;
    return AVCODEC_SAMPLE_ERR_OK;
}

int32_t Encoder::Start() {
    std::lock_guard<std::mutex> lock(mutex_);
    


    int32_t ret = videoEncoder_->Start();

    isStarted_ = true;
    encOutputThread_ = std::make_unique<std::thread>(&Encoder::EncOutputThread, this);
    if (encOutputThread_ == nullptr) {
        StartRelease();
        return AVCODEC_SAMPLE_ERR_ERROR;
    }

    return AVCODEC_SAMPLE_ERR_OK;
}

//void Encoder::EncOutputThread() {
//    while (true) {
//        std::unique_lock<std::mutex> lock(encContext_->outputMutex);
//
//        if (encContext_ == nullptr) {
//            return;
//        }
//
//        bool condRet = encContext_->outputCond.wait_for(
//            lock, 5s, [this]() { 
//                return !isStarted_ || 
//                    (encContext_ != nullptr && !encContext_->outputBufferInfoQueue.empty()); 
//            });
//
//        if (!isStarted_) {
//            break;
//        }
//
//        if (encContext_->outputBufferInfoQueue.empty()) {
//            continue;
//        }
//
//        CodecBufferInfo bufferInfo = encContext_->outputBufferInfoQueue.front();
//        encContext_->outputBufferInfoQueue.pop();
//        lock.unlock();
//
//        if ((bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) ||
//            (bufferInfo.attr.flags == AVCODEC_BUFFER_FLAGS_NONE)) {
//            encContext_->outputFrameCount++;
//            bufferInfo.attr.pts = encContext_->outputFrameCount * MICROSECOND / sampleInfo_.frameRate;
//        } else {
//            bufferInfo.attr.pts = 0;
//        }
//
//        if (videoEncoder_ == nullptr) {
//            break;
//        }
//
//        uint8_t* encodedData = OH_AVBuffer_GetAddr((OH_AVBuffer *)bufferInfo.buffer) + bufferInfo.attr.offset;
// 
//        bool bKeyFrame = (bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) != 0;
//    
//       
//        // Send normal video frame
//        int nRet = sendVideoData(encodedData, bufferInfo.attr.size, 
//                     bKeyFrame, sampleInfo_.videoWidth, sampleInfo_.videoHeight, true);
///*
//        int nRet = sendAsData(encodedData, bufferInfo.attr.size, 
//                      bKeyFrame, sampleInfo_.videoWidth, 
//                      sampleInfo_.videoHeight, true, 32);        
//*/
//        int32_t ret = videoEncoder_->FreeOutputBuffer(bufferInfo.bufferIndex);
//        if (ret != 0) {
//            break;
//        }
//    }
//
//    StartRelease();
//}


void Encoder::EncOutputThread() {
    char *pFirstPack = nullptr;
    char *pKeyFramePack = nullptr;
    int nKeyFramePackLen = 0;
    int nFirstPackLen = 0;    
    while (true) {
        
        std::unique_lock<std::mutex> lock(encContext_->outputMutex);

        if (encContext_ == nullptr) {
            return;
        }

        bool condRet = encContext_->outputCond.wait_for(
            lock, 5s, [this]() { 
                return !isStarted_ || 
                    (encContext_ != nullptr && !encContext_->outputBufferInfoQueue.empty()); 
            });

        if (!isStarted_) {
            break;
        }

        if (encContext_->outputBufferInfoQueue.empty()) {
            continue;
        }

        CodecBufferInfo bufferInfo = encContext_->outputBufferInfoQueue.front();
        encContext_->outputBufferInfoQueue.pop();
        lock.unlock();

        if ((bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) ||
            (bufferInfo.attr.flags == AVCODEC_BUFFER_FLAGS_NONE)) {
            encContext_->outputFrameCount++;
            bufferInfo.attr.pts = encContext_->outputFrameCount * MICROSECOND / sampleInfo_.frameRate;
        } else {
            bufferInfo.attr.pts = 0;
        }

        if (videoEncoder_ == nullptr) {
            break;
        }

        uint8_t* encodedData = OH_AVBuffer_GetAddr((OH_AVBuffer *)bufferInfo.buffer) + bufferInfo.attr.offset;
 
        bool bKeyFrame = (bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) != 0;
    
        if(encodedData) {
            bool bKeyFrame = (bufferInfo.attr.flags & AVCODEC_BUFFER_FLAGS_SYNC_FRAME) != 0;
            
            if(!pFirstPack) {
                bKeyFrame = true;
                nFirstPackLen = bufferInfo.attr.size;
                pFirstPack = new char[nFirstPackLen + 1];
                memcpy(pFirstPack, encodedData, bufferInfo.attr.size);
            } else {
                if(bKeyFrame) {
                    if(nKeyFramePackLen < nFirstPackLen + bufferInfo.attr.size + 1) {
                        if(pKeyFramePack) {
                            delete pKeyFramePack;
                        }
                        pKeyFramePack = new char[nFirstPackLen + bufferInfo.attr.size + 1];
                    }
                    
                    memcpy(pKeyFramePack, pFirstPack, nFirstPackLen);
                    memcpy(pKeyFramePack + nFirstPackLen, encodedData, bufferInfo.attr.size);
                    int nRet = sendVideoData(pKeyFramePack, nFirstPackLen + bufferInfo.attr.size, 
                                 bKeyFrame, sampleInfo_.videoWidth, sampleInfo_.videoHeight, true);
                }
            }
            if(!bKeyFrame) {
                int nRet = sendVideoData(encodedData, bufferInfo.attr.size, 
                             bKeyFrame, sampleInfo_.videoWidth, sampleInfo_.videoHeight, true);
            }
        }

        int32_t ret = videoEncoder_->FreeOutputBuffer(bufferInfo.bufferIndex);
        if (ret != 0) {
            break;
        }
    }
    if(pFirstPack) {
        delete pFirstPack;
    }
    if(pKeyFramePack) {
        delete pKeyFramePack;
    }    
    StartRelease();
}

void Encoder::StartRelease() {
    if (releaseThread_ == nullptr) {
        releaseThread_ = std::make_unique<std::thread>(&Encoder::Release, this);
    }
}

void Encoder::Release() {
    std::lock_guard<std::mutex> lock(mutex_);
    isStarted_ = false;
    if (encOutputThread_ && encOutputThread_->joinable()) {
        encOutputThread_->join();
        encOutputThread_.reset();
    }

    if (videoEncoder_ != nullptr) {
        videoEncoder_->Stop();
        if (sampleInfo_.window != nullptr) {
            OH_NativeWindow_DestroyNativeWindow(sampleInfo_.window);
            sampleInfo_.window = nullptr;
        }
        videoEncoder_->Release();
        videoEncoder_.reset();
    }
    if (encContext_ != nullptr) {
        delete encContext_;
        encContext_ = nullptr;
    }
    doneCond_.notify_all();
}

int32_t Encoder::Stop() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!isStarted_) {
        return AVCODEC_SAMPLE_ERR_OK;
    }

    isStarted_ = false;

    // 通知输出线程退出
    if (encContext_) {
        encContext_->outputCond.notify_all();
    }

    // 等待输出线程结束
    if (encOutputThread_ && encOutputThread_->joinable()) {
        encOutputThread_->join();
        encOutputThread_.reset();
    }

    // 停止视频编码器
    if (videoEncoder_) {
        int32_t ret = videoEncoder_->Stop();
        if (ret != AVCODEC_SAMPLE_ERR_OK) {
            return ret;
        }
    }

    StartRelease();
    return AVCODEC_SAMPLE_ERR_OK;
}

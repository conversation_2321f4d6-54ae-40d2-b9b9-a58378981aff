

#ifndef VIDEOENCODER_H
#define VIDEOENCODER_H

#include "common/SampleInfo.h"
#include "multimedia/player_framework/native_avcodec_videoencoder.h"
#include "multimedia/player_framework/native_avbuffer_info.h"

class VideoEncoder {
public:
    VideoEncoder() = default;
    ~VideoEncoder();

    int32_t Create(const std::string &videoCodecMime);
    int32_t Config(SampleInfo &sampleInfo, CodecUserData *codecUserData);
    int32_t Start();
    int32_t FreeOutputBuffer(uint32_t bufferIndex);
    int32_t NotifyEndOfStream();
    int32_t Stop();
    int32_t Release();

private:
    int32_t SetCallback(CodecUserData *codecUserData);
    int32_t Configure(const SampleInfo &sampleInfo);
    int32_t GetSurface(SampleInfo &sampleInfo);
    bool isAVBufferMode_ = false;
    OH_AVCodec *encoder_ = nullptr;
};
#endif // VIDEOENCODER_H
import { webview } from "@kit.ArkWeb";
import { promptAction } from "@kit.ArkUI";

// webview.once("webInited", () => {
//   console.log("configCookieSync");
//   webview.WebCookieManager.configCookieSync("https://www.example.com", "a=b");
// })

@Component
export struct ShareWebViewComponent {
  @Prop shareUrl: string = ''
  @State isLoaded: boolean = false
  @State hasError: boolean = false
  CloseClick: () => void = () => {

  }
  controller: webview.WebviewController = new webview.WebviewController();

  build() {
    Stack({alignContent:Alignment.Center}){
      if (!this.isLoaded && !this.hasError) {
        // 加载中提示
        //Progress().width(50).height(50)
        Text('页面加载中...')
          .fontColor(Color.Gray)
      }
      if (this.hasError) {
        // 错误提示
        Text('页面加载失败，请检查网络连接')
          .fontColor(Color.Red)
      }
      Stack({ alignContent: Alignment.TopEnd }) {
        Button({ type: ButtonType.Normal }) {
          Text('关闭')
        }
        .borderRadius(5)
        .backgroundColor(Color.White)
        .onClick(() => {
          this.CloseClick()
        })
        .zIndex(1)
        .padding(5)
        .margin(10)
        Web({ src: this.shareUrl, controller: this.controller })
          .onPageEnd(()=>{
            this.isLoaded = true
            this.hasError = false
          })
          .onErrorReceive((event)=>{
            if(event){
              this.hasError = true
              this.isLoaded = false
              promptAction.showToast({
                message: `加载失败，错误码：$${event.error.getErrorCode()}`
              })
            }
          })
      }
      .visibility(this.isLoaded ? Visibility.Visible : Visibility.Hidden)
    }
  }
}
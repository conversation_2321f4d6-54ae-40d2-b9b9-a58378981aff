/*
 * 基础相机预览组件
 */

import { camera } from '@kit.CameraKit';
import { BusinessError, emitter } from '@kit.BasicServicesKit';
import { videoProfileCheck } from '../common/utils/CameraCheck';
import { CameraDataModel } from '../model/CameraDateModel';
import encoder from 'libencoder.so'
import abilityAccessCtrl from '@ohos.abilityAccessCtrl'; // 导入权限管理模块
import { g_nChannelId } from '../common/infowarelabsdk/video/VideoCallBack';

const TAG: string = '[VideoView]';
const cameraData: CameraDataModel = new CameraDataModel();
let encoderVideoOutput: camera.VideoOutput;

// let g_nChannelId : number = 0;


@Component
export struct VideoView {
  @Prop cameraIndex: number // Add this prop to receive camera index
  @Prop rotation: number
  private XComponentController: XComponentController = new XComponentController();
  private XComponentSurfaceId: string = '';
  // 相机相关实例
  private cameraManager: camera.CameraManager | null = null;
  private cameraInput: camera.CameraInput | null = null;
  private previewOutput: camera.PreviewOutput | null = null;
  private videoSession: camera.VideoSession | null = null;
  @StorageLink('hss_video_storage') @Watch('resolutionRatioChange') resolutionRatio: VideoBit = {
    bitRate: 512000,
    width: 1280,
    height: 720
  }

  resolutionRatioChange() {
    //todo: 分辨率变化通知
    this.isShowXComponent = false
    console.log('eeeeeeeee , resolutionRatioChange', JSON.stringify(this.resolutionRatio))
  }

  /**
   * 请求相机权限
   */
  private async requestCameraPermission(): Promise<void> {
    try {
      // 创建权限管理对象
      let atManager = abilityAccessCtrl.createAtManager();
      let context = getContext(this);
      // 动态申请权限
      const result = await atManager.requestPermissionsFromUser(getContext(this), ['ohos.permission.CAMERA']);
      console.log('eeee', JSON.stringify(result.authResults[0]))
      if (result.authResults[0] === 0) { // 0 表示授权成功
        console.info(TAG, 'Camera permission granted, initializing camera');
        this.initCameraManager();
      } else {
        console.error(TAG, 'Camera permission denied');
        // 这里可以添加提示用户开启权限的逻辑
        atManager.requestPermissionOnSetting(context, ['ohos.permission.CAMERA']);
      }
    } catch (error) {
      console.error(TAG, `Failed to request camera permission: ${error.message}`);
    }
  }

  /**
   * 初始化相机管理器
   */
  private initCameraManager(): void {
    try {
      this.cameraManager = camera.getCameraManager(getContext(this));
      console.info(TAG, 'Camera manager initialized successfully');
      if (!this.cameraManager) {
        console.error(TAG, 'Failed to get camera manager instance');
        return;
      }
    } catch (error) {
      console.error(TAG, `Failed to initialize camera manager: ${error.message}`);
    }
  }

  /**
   * 创建相机预览
   */
  private async createPreview(): Promise<void> {
    if (!this.cameraManager) {
      console.error(TAG, 'Camera manager is not initialized');
      return;
    }


    if (!cameraData.surfaceId) {
      console.error(TAG, 'Surface ID is not available');
      return;
    }

    try {
      // 1. 获取相机设备列表
      const cameraDevices = this.cameraManager.getSupportedCameras();
      console.info(TAG, `Found ${cameraDevices?.length || 0} camera devices`);
      if (!cameraDevices || cameraDevices.length === 0) {
        console.error(TAG, 'No camera devices found');
        return;
      }

      let videoProfile: camera.VideoProfile | undefined = videoProfileCheck(this.cameraManager, cameraData);
      if (!videoProfile) {
        console.error(TAG, 'Failed to get video profile');
        return;
      }


      // 3. 创建相机输入
      this.cameraInput = this.cameraManager.createCameraInput(cameraDevices[this.cameraIndex]);
      await this.cameraInput.open();

      // 4. 创建编码器输出
      encoderVideoOutput = this.cameraManager.createVideoOutput(videoProfile, cameraData.surfaceId);

      if (!encoderVideoOutput) {
        console.error(TAG, 'Failed to create video output');
        return;
      }
      // 5. 创建预览输出
      this.previewOutput = this.cameraManager.createPreviewOutput(videoProfile, this.XComponentSurfaceId);
      console.log('eeepreviewOutpute')
      if (!this.previewOutput) {
        console.error(TAG, 'Failed to create preview output');
        return;
      }

      // 6. 创建并配置视频会话
      this.videoSession = this.cameraManager.createSession(camera.SceneMode.NORMAL_VIDEO) as camera.VideoSession;
      if (!this.videoSession) {
        console.error(TAG, 'Failed to create video session');
        return;
      }

      this.videoSession.beginConfig();
      this.videoSession.addInput(this.cameraInput);
      this.videoSession.addOutput(encoderVideoOutput);
      this.videoSession.addOutput(this.previewOutput);
      await this.videoSession.commitConfig();

      // 7. 启动预览
      await this.videoSession.start();

      // 8. 启动编码器输出
      encoderVideoOutput.start((err: BusinessError) => {
        if (err) {
          console.error(TAG, `Failed to start encoder output: ${JSON.stringify(err)}`);
          return;
        }
        console.info(TAG, 'Encoder output started successfully');
      });

      console.info(TAG, 'Camera preview started successfully');

    } catch (error) {
      console.error(TAG, `Failed to create preview: ${error.message}`);
    }
  }

  /**
   * 释放相机资源
   */
  private async releaseCamera(): Promise<void> {
    try {
      console.info(TAG, 'Releasing camera resources');

      // 停止编码器输出
      if (encoderVideoOutput) {
        encoderVideoOutput.stop();
        encoderVideoOutput.release();
      }

      // 停止并释放会话
      if (this.videoSession) {
        await this.videoSession.stop();
        await this.videoSession.release();
      }

      // 释放预览输出
      if (this.previewOutput) {
        await this.previewOutput.release();
      }

      // 关闭相机输入
      if (this.cameraInput) {
        await this.cameraInput.close();
      }

      // 清空引用
      this.cameraManager = null;
      this.cameraInput = null;
      this.previewOutput = null;
      this.videoSession = null;

      console.info(TAG, 'Camera resources released successfully');
    } catch (error) {
      console.error(TAG, `Failed to release camera resources: ${error.message}`);
    }
  }

  @State isShowXComponent: boolean = true;

  build() {
    Column() {
      // 相机预览界面
      if (this.isShowXComponent) {
        XComponent({
          id: 'previewComponent',
          type: XComponentType.SURFACE,
          controller: this.XComponentController
        })
          .width('100%')
          .height('100%')
          .onLoad(async () => { // 添加 async
            try {
              // cameraData.bitRate = 1024000 / 2;
              // cameraData.bitRate = 1024000;
              this.requestCameraPermission();
              console.log('eeee onLoad', this.resolutionRatio.width, this.resolutionRatio.height,
                this.resolutionRatio.bitRate)
              // 1. 等待编码器初始化完成
              const result = await encoder.initNative(
                cameraData.outputfd,
                cameraData.videoCodecMime,
                this.resolutionRatio.width,
                this.resolutionRatio.height,
                cameraData.frameRate,
                cameraData.isHDRVivid,
                this.resolutionRatio.bitRate
              );

              console.info(TAG, `Encoder init result: ${JSON.stringify(result)}`);

              if (result && result.surfaceId) {
                // 2. 保存 surface ID
                cameraData.surfaceId = result.surfaceId;
                console.info(TAG, `Got surface ID: ${cameraData.surfaceId}`);

                // 3. 获取预览 Surface ID
                this.XComponentSurfaceId = this.XComponentController.getXComponentSurfaceId();
                console.info(TAG, `Got preview surface ID: ${this.XComponentSurfaceId}`);

                this.XComponentController.setXComponentSurfaceRotation({ lock: true });

                // 4. 创建预览
                this.createPreview();

                // 5. 启动编码器
                await encoder.startNative();

                console.info(TAG, 'Encoder started successfully');
              } else {
                console.error(TAG, 'Failed to get valid surface ID from encoder');
              }
            } catch (error) {
              console.error(TAG, `Failed to initialize camera preview: ${error}`);
            }
          })
          .onDestroy(()=>{
            // 释放相机资源
            this.releaseCamera();

            // 停止编码器
            encoder.stopNative();
          })
      } else {
        Text('正在初始化')
          .fontSize(20)
          .fontColor(Color.White)
          .width('100%')
          .height('100%')
          .textAlign(TextAlign.Center)
          .margin({ top: 200 })
          .onAppear(() => {
            setTimeout(() => {
              this.isShowXComponent = true
            }, 1000)
          })
      }

    }
    // .width('100%')
    // .height('80%')
    .constraintSize({ maxWidth: '100%', maxHeight: '100%' })
    .aspectRatio((this.rotation === 0 || this.rotation === 2) ? (11 / 16) : (16 / 11))
  }
}

export interface VideoBit {
  bitRate: number
  width: number
  height: number
  title?: string
}
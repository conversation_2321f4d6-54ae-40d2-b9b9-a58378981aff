{
  "apiType": "stageMode",
  "buildOption": {
    "nativeLib": {
      "filter": {
        "select": [
          {
            "package": "lib_hstinterface",
            "include": ["libhstinterface.so"]
          }
        ]
      }
    },
    "externalNativeOptions": {
      "abiFilters": ["arm64-v8a"]
    }
  },
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": false,
            "files": [
              "./obfuscation-rules.txt"
            ]
          },
          "consumerFiles": [
            "./consumer-rules.txt"
          ]
        }
      },
    },
  ],
  "targets": [
    {
      "name": "default"
    },
    {
      "name": "ohosTest"
    }
  ]
}

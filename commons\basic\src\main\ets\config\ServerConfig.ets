import { preferences } from '@kit.ArkData';
import { Context } from '@kit.AbilityKit';

/**
 * 服务器配置管理类
 * 统一管理服务器地址配置，支持动态更新和持久化存储
 */
export class ServerConfig {
  private static instance: ServerConfig = new ServerConfig();

  // 默认服务器地址
  private static readonly DEFAULT_SERVER_URL = '';

  // 当前服务器地址
  private currentServerUrl: string = ServerConfig.DEFAULT_SERVER_URL;

  // Preferences存储实例
  private preferencesStore: preferences.Preferences | null = null;

  // Preferences存储名称
  private static readonly PREFERENCES_NAME = 'server_config';
  private static readonly SERVER_URL_KEY = 'serverUrl';

  private constructor() {
    // 私有构造函数，确保单例模式
    this.initPreferences();
  }

  public static getInstance(): ServerConfig {
    return ServerConfig.instance;
  }

  /**
   * 初始化Preferences存储
   */
  private async initPreferences(): Promise<void> {
    try {
      // 获取应用上下文
      const context = AppStorage.Get<Context>('context') as Context;
      if (!context) {
        console.error('[ServerConfig] 无法获取应用上下文，使用AppStorage作为备选方案');
        this.loadServerUrlFromAppStorage();
        return;
      }

      // 获取Preferences实例
      this.preferencesStore = await preferences.getPreferences(context, ServerConfig.PREFERENCES_NAME);
      console.info('[ServerConfig] Preferences初始化成功');

      // 加载服务器地址
      await this.loadServerUrl();
    } catch (error) {
      console.error('[ServerConfig] Preferences初始化失败:', error);
      // 降级到AppStorage
      this.loadServerUrlFromAppStorage();
    }
  }

  /**
   * 从Preferences加载服务器地址
   */
  private async loadServerUrl(): Promise<void> {
    try {
      if (!this.preferencesStore) {
        console.warn('[ServerConfig] Preferences未初始化，使用AppStorage');
        this.loadServerUrlFromAppStorage();
        return;
      }

      // 先设置默认值
      this.currentServerUrl = ServerConfig.DEFAULT_SERVER_URL;

      // 从Preferences获取保存的地址
      const savedUrl = await this.preferencesStore.get(ServerConfig.SERVER_URL_KEY, '') as string;
      if (savedUrl && savedUrl.trim() !== '') {
        this.currentServerUrl = savedUrl.trim();
        console.info('[ServerConfig] 从Preferences加载服务器地址:', this.currentServerUrl);
      } else {
        // 如果没有保存的地址，保存默认地址
        await this.saveServerUrlToPreferences(this.currentServerUrl);
        console.info('[ServerConfig] 使用默认服务器地址:', this.currentServerUrl);
      }

      // 同步到AppStorage以便UI组件使用
      AppStorage.SetOrCreate<string>('serverUrl', this.currentServerUrl);

    } catch (error) {
      console.error('[ServerConfig] 从Preferences加载服务器地址失败:', error);
      // 降级到AppStorage
      this.loadServerUrlFromAppStorage();
    }
  }

  /**
   * 从AppStorage加载服务器地址（备选方案）
   */
  private loadServerUrlFromAppStorage(): void {
    try {
      // 先设置默认值，确保currentServerUrl不为空
      this.currentServerUrl = ServerConfig.DEFAULT_SERVER_URL;

      // 尝试从AppStorage获取保存的地址
      const savedUrl = AppStorage.Get<string>('serverUrl');
      if (savedUrl && savedUrl.trim() !== '') {
        this.currentServerUrl = savedUrl.trim();
        console.info('[ServerConfig] 从AppStorage加载服务器地址:', this.currentServerUrl);
      } else {
        // 如果没有保存的地址，保存默认地址
        AppStorage.SetOrCreate<string>('serverUrl', this.currentServerUrl);
        console.info('[ServerConfig] 使用默认服务器地址:', this.currentServerUrl);
      }
    } catch (error) {
      console.error('[ServerConfig] 从AppStorage加载服务器地址失败:', error);
      // 确保即使出错也有默认值
      this.currentServerUrl = ServerConfig.DEFAULT_SERVER_URL;
      try {
        AppStorage.SetOrCreate<string>('serverUrl', this.currentServerUrl);
      } catch (storageError) {
        console.error('[ServerConfig] 保存默认地址到AppStorage失败:', storageError);
      }
    }
  }
  
  /**
   * 保存服务器地址到Preferences
   */
  private async saveServerUrlToPreferences(url: string): Promise<void> {
    try {
      if (!this.preferencesStore) {
        console.warn('[ServerConfig] Preferences未初始化，无法保存');
        return;
      }

      await this.preferencesStore.put(ServerConfig.SERVER_URL_KEY, url);
      await this.preferencesStore.flush();
      console.info('[ServerConfig] 服务器地址已保存到Preferences:', url);
    } catch (error) {
      console.error('[ServerConfig] 保存服务器地址到Preferences失败:', error);
    }
  }

  /**
   * 获取当前服务器地址
   */
  public getServerUrl(): string {
    // 确保返回值不为空
    if (!this.currentServerUrl) {
      console.warn('[ServerConfig] currentServerUrl为空，使用默认地址');
      this.currentServerUrl = ServerConfig.DEFAULT_SERVER_URL;
      AppStorage.SetOrCreate<string>('serverUrl', this.currentServerUrl);
    }
    return this.currentServerUrl;
  }

  /**
   * 设置服务器地址
   * @param url 新的服务器地址
   */
  public async setServerUrl(url: string): Promise<void> {
    if (url && url.trim() !== '') {
      this.currentServerUrl = url.trim();

      // 保存到Preferences（持久化存储）
      await this.saveServerUrlToPreferences(this.currentServerUrl);

      // 同步到AppStorage（供UI组件使用）
      AppStorage.SetOrCreate<string>('serverUrl', this.currentServerUrl);

      console.info('[ServerConfig] 更新服务器地址:', this.currentServerUrl);
    }
  }
  
  /**
   * 获取API基础URL
   */
  public getApiBaseUrl(): string {
    return this.getServerUrl(); // 使用getServerUrl确保不为空
  }
  
  /**
   * 获取会议服务URL
   */
  public getMeetingServiceUrl(): string {
    return `${this.getServerUrl()}/meeting/remoteServlet?funcName=joinConf&`;
  }
  
  /**
   * 获取登录API URL
   */
  public getLoginApiUrl(): string {
    return `${this.getServerUrl()}/cas/auth2`;
  }
  
  /**
   * 获取会议列表API URL
   */
  public getConfListApiUrl(): string {
    return this.getServerUrl(); // 使用getServerUrl确保不为空
  }
  
  /**
   * 重置为默认服务器地址
   */
  public async resetToDefault(): Promise<void> {
    await this.setServerUrl(ServerConfig.DEFAULT_SERVER_URL);
  }

  /**
   * 确保Preferences已初始化（供外部调用）
   */
  public async ensureInitialized(): Promise<void> {
    if (!this.preferencesStore) {
      await this.initPreferences();
    }
  }
  
  /**
   * 验证URL格式
   * @param url 要验证的URL
   */
  public static validateUrl(url: string): boolean {
    if (!url || url.trim() === '') {
      return false;
    }
    
    // 简单的URL格式验证
    const urlPattern = /^https?:\/\/.+/;
    return urlPattern.test(url.trim());
  }
}

// 导出单例实例
export const serverConfig = ServerConfig.getInstance();

// 导出兼容性常量（保持向后兼容）
export const API_BASE_URL = serverConfig.getApiBaseUrl();
export const BaseUrl = serverConfig.getMeetingServiceUrl();

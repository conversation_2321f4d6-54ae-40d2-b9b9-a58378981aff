

#ifndef VIDEO_CODEC_SAMPLE_CAPTURE_H
#define VIDEO_CODEC_SAMPLE_CAPTURE_H

#include <thread>
#include <mutex>
#include <memory>
#include <atomic>
#include <condition_variable>

#include "capbilities/include/VideoEncoder.h"
#include "common/SampleInfo.h"

class Capture {
public:
    Capture(){};
    ~Capture();

    static Capture &GetInstance() {
        static Capture recorder;
        return recorder;
    }

    int32_t Init(SampleInfo &sampleInfo);
    int32_t Start();
//    int32_t Stop();
    void StartRelease();
    void Release();

private:
    void EncOutputThread();
   
    
    std::unique_ptr<VideoEncoder> videoEncoder_ = nullptr;
    std::mutex mutex_;
    std::atomic<bool> isStarted_{false};
    std::unique_ptr<std::thread> encOutputThread_ = nullptr;
    std::unique_ptr<std::thread> releaseThread_ = nullptr;
    std::condition_variable doneCond_;
    SampleInfo sampleInfo_;
    CodecUserData *encContext_ = nullptr;
};

#endif // VIDEO_CODEC_SAMPLE_CAPTURE_H
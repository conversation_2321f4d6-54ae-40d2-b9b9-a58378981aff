import { abilityAccessCtrl, Permissions } from '@kit.AbilityKit';

class Permisson {
  async openPermissionSetting(context: Context, permission: Permissions[]) {
    const atManager = abilityAccessCtrl.createAtManager()
    //const context = AppStorage.get<Context>('context')
    if (context) {
      const result = await atManager.requestPermissionOnSetting(context, permission)
      return result.every(item => item === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED)
    }
    return false
  }

  async requestPermissions(context: Context, permission: Permissions[]) {
    const atManager = abilityAccessCtrl.createAtManager()
    //const context = AppStorage.get<Context>('context')
    if (context) {
      const result = await atManager.requestPermissionsFromUser(context, permission)
      return result.authResults.every(item => item === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED)
    }
    return false
  }
}

export const PermissonUtil = new Permisson()
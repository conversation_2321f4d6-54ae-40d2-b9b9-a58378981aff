# 匿名登录设置页面修改说明

## 修改内容

### 1. 账户显示逻辑修改
- **匿名用户**：账户项显示"未登录"
- **正式用户**：账户项显示用户名
- **未登录用户**：账户项显示"未登录"

### 2. 点击跳转逻辑修改
- **匿名用户**：点击账户项跳转到登录页面
- **正式用户**：点击账户项跳转到退出账户页面
- **未登录用户**：点击账户项跳转到登录页面

## 具体修改

### 文件：`features/settings/src/main/ets/components/SettingsPage.ets`

#### 1. 添加导入
```typescript
import { loginApi } from 'basic';
```

#### 2. 修改显示逻辑（updateLoginStatus方法）
```typescript
// 获取当前用户信息
const currentUser = loginApi.getCurrentUser();

// 检查是否为正式登录用户（排除匿名用户）
if (currentUser.isLoggedIn() && this.isLoggedIn && this.userInfoStr) {
  // 正式用户：显示用户名
  const newValue = userInfo.userName || userInfo.realName || '已登录';
  // 设置账户项显示用户名
} else {
  // 匿名用户或未登录用户：显示"未登录"
  // 设置账户项显示"未登录"
}
```

#### 3. 修改点击跳转逻辑
```typescript
if (item.title === '账户') {
  // 获取当前用户信息
  const currentUser = loginApi.getCurrentUser();
  
  // 检查是否为正式登录用户（排除匿名用户）
  if (currentUser.isLoggedIn()) {
    // 正式登录用户，跳转到退出账户页面
    builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.LogoutPage, ...);
  } else {
    // 未登录或匿名用户，跳转到登录页面
    builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.LoginPage, ...);
  }
}
```

## 用户体验流程

### 匿名登录用户：
1. 匿名登录进入应用
2. 进入设置页面
3. 看到账户项显示"未登录"
4. 点击账户项 → 跳转到登录页面
5. 可以进行正式登录

### 正式登录用户：
1. 正式登录进入应用
2. 进入设置页面
3. 看到账户项显示用户名
4. 点击账户项 → 跳转到退出账户页面
5. 可以退出登录

## 测试验证

### 测试步骤：
1. **匿名登录测试**：
   - 启动应用，点击匿名登录
   - 进入设置页面
   - 验证账户项显示"未登录"
   - 点击账户项，验证跳转到登录页面

2. **正式登录测试**：
   - 进行正式登录
   - 进入设置页面
   - 验证账户项显示用户名
   - 点击账户项，验证跳转到退出账户页面

3. **状态切换测试**：
   - 从匿名状态切换到正式登录
   - 验证设置页面显示和跳转逻辑的变化

### 预期结果：
- ✅ 匿名用户看到"未登录"，点击跳转到登录页面
- ✅ 正式用户看到用户名，点击跳转到退出页面
- ✅ 状态切换时显示和跳转逻辑正确更新

## 技术要点

- 使用`currentUser.isLoggedIn()`区分正式用户和匿名用户
- 匿名用户和未登录用户统一处理为"未登录"状态
- 保持原有的UI更新机制和路由跳转逻辑
- 确保与其他页面的用户状态检查保持一致

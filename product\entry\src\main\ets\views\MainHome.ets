
import { BuilderNameConstants, builderRouterModel, RouterNameConstants } from 'routermoudel';
import { MeetingService, ConferenceInfo } from '../service/MeetingService';
import emitter from '@ohos.events.emitter';
import { loginApi } from 'basic/src/main/ets/api/LoginApi';
import { promptAction } from '@kit.ArkUI';

// 定义可观察的会议类
@Observed
class Meeting {
  title: string
  time: string
  meetingId: string
  password?: string
  startTime?: Date | undefined  // 修改为可选类型
  endTime?: Date | undefined    // 修改为可选类型
  status?: string              // 添加会议状态字段

  constructor(title: string, time: string, meetingId: string, password?: string, startTime?: Date | undefined, endTime?: Date | undefined, status?: string) {
    this.title = title
    this.time = time
    this.meetingId = meetingId
    this.password = password
    this.startTime = startTime
    this.endTime = endTime
    this.status = status
  }
}

// 定义路由参数接口
interface RouterParams {
  origin: string;
}

// 定义会议数据接口（用于传递到详情页）
interface MeetingDataForDetail {
  meetingId: string;
  title: string;
  time: string;
  password: string;
  startTime?: Date;
  endTime?: Date;
  status: string;
}

// 定义详情页路由参数接口
interface MeetingDetailRouteParams {
  origin: string;
  meetingData: MeetingDataForDetail;
}

@Component
export struct MainHome{

  @State meetings: Meeting[] = []
  @State isLoading: boolean = false
  @State currentIndex: number = 0;
  @State confListType: number = 1;  // 默认获取未来7天内的会议
  @State isRefreshing: boolean = false;
  @State canLoadMore: boolean = true;
  @State hasInitialized: boolean = false;  // 添加初始化标志
  private scroller: Scroller = new Scroller();

  aboutToAppear(): void {
    // 只在首次初始化时获取会议列表
    if (!this.hasInitialized) {
      this.fetchMeetingList();
      this.hasInitialized = true;
    }

    // 注册登录成功事件监听
    this.registerLoginSuccessListener();

    // 注册导航栏重置事件监听
    this.registerNavigationResetListener();

    // 注册会议预约成功事件监听
    this.registerMeetingScheduledListener();
  }
  
  aboutToDisappear() {
    // 取消事件监听
    emitter.off('loginSuccess');
    emitter.off('resetNavigation');
    emitter.off('meetingScheduled'); // 取消会议预约事件监听
  }

  // 注册导航栏重置事件监听
  registerNavigationResetListener() {
    emitter.on('resetNavigation', () => {
      console.info('收到导航栏重置事件');
      this.currentIndex = 0;
    });
  }
  
  // 注册登录成功事件监听
  registerLoginSuccessListener() {
    // 注册事件监听器，使用简单的字符串标识符
    emitter.on('loginSuccess', () => {
      console.info('收到登录成功事件，刷新会议列表');
      // 防止重复请求，添加延迟和状态检查
      if (!this.isLoading && !this.isRefreshing) {
        setTimeout(() => {
          this.fetchMeetingList();
        }, 300);
      }
    });
  }

  // 注册会议预约成功事件监听
  registerMeetingScheduledListener() {
    emitter.on('meetingScheduled', () => {
      console.info('收到会议预约成功事件，刷新会议列表');
      // 防止重复请求，添加延迟和状态检查
      if (!this.isLoading && !this.isRefreshing) {
        setTimeout(() => {
          console.info('开始刷新会议列表...');
          this.fetchMeetingList();
        }, 500); // 延迟500毫秒
      }
    });
  }

  onShown() {
    console.log('MainPage onShown');
    // 检查是否需要刷新数据
    // 重置导航栏选中状态为主页
    this.currentIndex = 0;
    // 移除自动刷新，只在用户主动下拉时才刷新
    console.log('页面显示，但不自动刷新会议列表');
  }
  
  // 下拉刷新方法
  async onRefresh() {
    console.info('[会议列表] 开始下拉刷新');
    // 防止重复刷新
    if (this.isRefreshing || this.isLoading) {
      console.info('[会议列表] 正在刷新中，跳过重复刷新');
      return;
    }

    this.isRefreshing = true;
    try {
      await this.fetchMeetingList();
    } finally {
      this.isRefreshing = false;
    }
  }

  // 下拉加载更多方法
  async onLoadMore() {
    if (!this.canLoadMore || this.isLoading || this.isRefreshing) {
      return;
    }
    console.info('[会议列表] 开始下拉加载更多');
    // 这里可以实现分页加载逻辑
    // 暂时只是刷新当前数据
    await this.fetchMeetingList();
  }

  // 获取会议列表数据
  async fetchMeetingList() {
    // 防止重复请求
    if (this.isLoading || this.isRefreshing) {
      console.info('[会议列表] 正在加载中，跳过重复请求');
      return;
    }

    try {
      console.info('[会议列表] 开始获取会议列表数据');
      if (!this.isRefreshing) {
        this.isLoading = true;
      }
      // 调用修改后的会议列表接口，传入页码、每页数量和会议列表类型
      const response = await MeetingService.getConfList(1, 20, this.confListType);

      console.info('[会议列表] 获取会议列表响应成功，响应长度:', typeof response === 'string' ? response.length : 'unknown');
      console.info('[会议列表] 响应内容预览:', typeof response === 'string' ? response.substring(0, 200) + '...' : response);

      if (response != null) {
        try {
          // 使用正则表达式解析XML格式的会议列表
          const meetingData = MeetingService.parseXmlMeetingList(response);
          
          // 合并进行中和未开始的会议
          const allMeetings: ConferenceInfo[] = [];
          
          // 添加进行中的会议
          for (let i = 0; i < meetingData.progressConfs.length; i++) {
            allMeetings.push(meetingData.progressConfs[i]);
          }
          
          // 添加未开始的会议
          for (let i = 0; i < meetingData.confs.length; i++) {
            allMeetings.push(meetingData.confs[i]);
          }
          
          if (allMeetings.length > 0) {
            // 将解析后的会议数据转换为Meeting对象
            const tempMeetings: Meeting[] = [];
            
            for (let i = 0; i < allMeetings.length; i++) {
              const item = allMeetings[i];
              
              // 解析日期时间
              let startTime: Date | undefined = undefined;
              let endTime: Date | undefined = undefined;
              let displayTime = '';
              
              if (item.startTime) {
                startTime = new Date(item.startTime);
                // 格式化显示时间
                displayTime = MainHome.formatDisplayTime(startTime);
              }
              
              if (item.endTime) {
                endTime = new Date(item.endTime);
                // 如果有结束时间，添加到显示时间中
                if (displayTime) {
                  displayTime += ' - ' + MainHome.formatTimeOnly(endTime);
                }
              }
              
              // 如果没有时间信息，使用默认显示
              if (!displayTime) {
                displayTime = '时间未指定';
              }
              
              // 解码会议名称中的特殊字符（处理中文乱码问题）
              let meetingName = item.name || '未命名会议';


              let meetingStatus = item.status
              tempMeetings.push(new Meeting(
                meetingName,
                displayTime,
                item.id || '无会议号',
                item.confPassword || item.hostPassword,
                startTime,
                endTime,
                meetingStatus
              ));

            }
            
            this.meetings = tempMeetings;
            console.info('成功解析会议列表数据，共有会议:', this.meetings.length);
          } else {
            console.error('未找到有效的会议数据');
            this.useDefaultMeetings();
          }
        } catch (parseError) {
          console.error('解析会议列表响应失败:', parseError);
          this.useDefaultMeetings();
        }
      } else {
        console.error('获取会议列表失败:', response);
        this.useDefaultMeetings();
      }
    } catch (error) {
      console.error('[会议列表] 获取会议列表异常:', error);
      console.error('[会议列表] 异常详情:', JSON.stringify(error));
      if (error instanceof Error) {
        console.error('[会议列表] 错误消息:', error.message);
        console.error('[会议列表] 错误堆栈:', error.stack);
      }
      this.useDefaultMeetings();
    } finally {
      if (!this.isRefreshing) {
        this.isLoading = false;
      }
      console.info('[会议列表] 会议列表加载完成，共有会议数量:', this.meetings.length);
    }
  }
  
  // 使用默认会议数据
  private useDefaultMeetings() {
    this.meetings = [
      new Meeting('teddy的会议', '2025.05.30 18:31', '78173226', undefined, undefined, undefined, '进行中'),
      new Meeting('接口2集成20250513', '2025.05.22 19:44', '81698388', undefined, undefined, undefined, '进行中'),
      new Meeting('直播接口2集成2025test', '2025.06.03 14:45', '31222568'),
      new Meeting('直播接口3集成2025test', '2025.06.03 14:45', '97751166'),
      new Meeting('直播推流接口', '2025.06.03 13:45', '76605361'),
      new Meeting('直播会议测试1', '2025.05.19 11:30', '92837561'),
      new Meeting('administrator的会议', '2025.05.19 11:30', '12345678')
    ];
    console.info('使用默认会议列表数据');
  }

  // 检查用户登录状态
  private checkUserLoginStatus(): boolean {
    try {
      // 通过basic里的方法获得用户信息
      const currentUser = loginApi.getCurrentUser();


      // 检查用户信息是否存在
      if (!currentUser) {
        console.info('用户信息为空，判定为匿名用户');
        return false;
      }

      // 检查是否所有字段都是默认值（判定为匿名用户）
      const isDefaultUser = (
        currentUser.userId === 0 &&
        currentUser.userName === '' &&
        currentUser.realName === '' &&
        currentUser.nickName === '' &&
        currentUser.roles === '' &&
        currentUser.token === '' &&
        currentUser.email === ''
      );

      if (isDefaultUser) {
        console.info('用户信息为默认值，判定为匿名用户');
        return false;
      }

      // 检查关键字段来判断是否为有效用户（包括匿名用户）
      const hasValidToken = currentUser.token && currentUser.token.trim() !== '';
      const hasValidUserId = currentUser.userId && (currentUser.userId > 0 || currentUser.userId === -1); // 支持匿名用户ID(-1)
      const hasValidUserName = currentUser.userName && currentUser.userName.trim() !== '';

      // 检查是否为匿名用户
      const isAnonymousUser = currentUser.userId === -1 && currentUser.userName === 'anonymous_user';

      console.info('用户登录状态检查:', {
        hasValidToken,
        hasValidUserId,
        hasValidUserName,
        isAnonymousUser,
        userId: currentUser.userId,
        userName: currentUser.userName,
        token: currentUser.token ? '***' : ''
      });

      // 如果有有效的token、用户ID、用户名，或者是匿名用户，则认为已登录
      return hasValidToken || hasValidUserId || hasValidUserName || isAnonymousUser;
    } catch (error) {
      console.error('检查用户登录状态时发生错误:', error);
      return false;
    }
  }

  // 处理需要登录的操作
  private handleLoginRequiredAction(actionName: string, callback: () => void) {
    const currentUser = loginApi.getCurrentUser();

    if (currentUser.isLoggedIn()) {
      // 用户已正式登录，执行操作
      console.info(`用户已登录，执行${actionName}操作`);
      callback();
    } else if (currentUser.isAnonymousUser()) {
      // 匿名用户，显示提示
      console.info(`匿名用户尝试执行${actionName}操作，显示提示`);
      promptAction.showToast({
        message: '匿名用户不能操作此功能'
      });
    } else {
      // 未登录用户，显示提示
      console.info(`未登录用户尝试执行${actionName}操作，显示提示`);
      promptAction.showToast({
        message: '请先登录'
      });
    }
  }
  
  // 格式化会议号（四个数字一组）
  private static formatMeetingId(meetingId: string): string {
    if (!meetingId) return '';
    // 移除所有非数字字符
    const numbers = meetingId.replace(/\D/g, '');
    // 每四个数字一组，用空格分隔
    return numbers.replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  // 格式化显示时间（年.月.日 时:分）
  private static formatDisplayTime(date: Date | undefined): string {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}.${month}.${day} ${hours}:${minutes}`;
  }
  
  // 仅格式化时间部分（时:分）
  private static formatTimeOnly(date: Date | undefined): string {
    if (!date) return '';
    
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${hours}:${minutes}`;
  }

  // 页面即将显示时调用
  onAboutToAppear() {
    console.log('Page is about to appear');
  }

  // 页面即将消失时调用
  onAboutToDisappear() {
    console.log('Page is about to disappear');
  }

  // 页面完全显示时调用
  onPageShow() {
    console.log('Page is fully visible');
  }

  // 页面完全隐藏时调用
  onPageHide() {
    console.log('Page is fully hidden');
  }

  // 拦截返回键事件
  onBackPress() {
    console.log('Back button pressed');
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Text('红杉会议')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)  // 添加居中对齐
      .padding({ left: 20, right: 20, top: 16, bottom: 16 })
      .backgroundColor(Color.White)

      // 功能按钮区域
      Row() {
        // 加入会议
        Column() {
          Image($r('app.media.hs_join_conf_2'))
            .width(60)
            .height(60)
            .objectFit(ImageFit.Contain)
          
          Text('加入会议')
            .fontSize(16)
            .fontColor('#333333')
            .margin({ top: 8 })
        }
        .layoutWeight(1)
        .padding(16)
        .justifyContent(FlexAlign.End)
        .onClick(() => {
          // 加入会议点击事件 - 导航到加入会议页面
          const routeParams: RouterParams = {
            origin: 'home'
          };
          builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.JoinMeetingPage, routeParams);
        })
        
        // 创建会议
        Column() {
          Image($r('app.media.hs_create_conf_2'))
            .width(60)
            .height(60)
            .objectFit(ImageFit.Contain)

          Text('发起会议')
            .fontSize(16)
            .fontColor('#333333')
            .margin({ top: 8 })
        }
        .layoutWeight(1)
        .padding(16)
        .justifyContent(FlexAlign.End)
        .onClick(() => {
          this.handleLoginRequiredAction('发起会议', () => {
            const routeParams: RouterParams = {
              origin: 'www'
            };
            builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.CreateMeetingPage, routeParams);
          });
        })
        
        // 预约会议
        Column() {
          Image($r('app.media.hs_schedule_conf_2'))
            .width(60)
            .height(60)
            .objectFit(ImageFit.Contain)

          Text('预约会议')
            .fontSize(16)
            .fontColor('#333333')
            .margin({ top: 8 })
        }
        .layoutWeight(1)
        .padding(16)
        .justifyContent(FlexAlign.End)
        .onClick(() => {
          this.handleLoginRequiredAction('预约会议', () => {
            const routeParams: RouterParams = {
              origin: 'www'
            };
            builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.ScheduleMeetingPage, routeParams);
          });
        })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 16 })
      .backgroundColor(Color.White)
      
      // 会议列表 - 添加下拉刷新功能
      Refresh({ refreshing: this.isRefreshing, offset: 120, friction: 100 }) {
        List({ scroller: this.scroller }) {
          if (this.isLoading && !this.isRefreshing) {
            ListItem() {
              Row() {
                LoadingProgress()
                  .width(24)
                  .height(24)
                Text('加载中...')
                  .fontSize(16)
                  .fontColor('#666666')
                  .margin({ left: 8 })
              }
              .width('100%')
              .justifyContent(FlexAlign.Center)
              .padding(16)
            }
          } else if (this.meetings.length === 0 && !this.isLoading) {
            ListItem() {
              Row() {
                Text('暂无会议')
                  .fontSize(16)
                  .fontColor('#999999')
              }
              .width('100%')
              .justifyContent(FlexAlign.Center)
              .padding(16)
            }
          } else {
            ForEach(this.meetings, (meeting: Meeting) => {
              ListItem() {
                Row() {
                  Column() {
                    Row() {
                      Text(meeting.title)
                        .fontSize(16)
                        .fontWeight(FontWeight.Medium)
                        .fontColor('#333333')
                        .maxLines(1)
                        .textOverflow({ overflow: TextOverflow.Ellipsis })

                      Blank()
                    }
                    .width('100%')

                    Row() {
                      Text(meeting.time)
                        .fontSize(15)
                        .fontColor('#999999')

                      Text(' · ')
                        .fontSize(15)
                        .fontColor('#999999')

                      Text(MainHome.formatMeetingId(meeting.meetingId))
                        .fontSize(15)
                        .fontColor('#999999')

                      // 进行中状态标签移到这里，与会议号平齐
                      if (meeting.status === '1') {
                        Text(' · 进行中')
                          .fontSize(15)
                          .fontColor('#0A59F7')
                      }
                    }
                    .margin({ top: 4 })
                  }
                  .alignItems(HorizontalAlign.Start)
                  .layoutWeight(1)

                  // 右侧箭头图标
                  Text('>')
                    .fontSize(16)
                    .fontColor('#999999')
                    .margin({ left: 8 })
                }
                .width('100%')
                .padding({ left: 16, right: 16, top: 12, bottom: 12 })
              }
              .backgroundColor(Color.White)
              .margin({ top: 1 })
              .onClick(() => {
                // 传递会议数据到详情页
                console.info('[会议列表] 点击会议项，传递数据:', meeting.title);

                const meetingData: MeetingDataForDetail = {
                  meetingId: meeting.meetingId,
                  title: meeting.title,
                  time: meeting.time,
                  password: meeting.password || '',
                  startTime: meeting.startTime,
                  endTime: meeting.endTime,
                  status: meeting.status || ''
                };

                const routeParams: MeetingDetailRouteParams = {
                  origin: 'www',
                  meetingData: meetingData
                };

                builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.MeetingDetail, routeParams);
              })
            })

            // 底部加载更多提示
            if (this.canLoadMore && this.meetings.length > 0) {
              ListItem() {
                Row() {
                  Text('上滑加载更多')
                    .fontSize(14)
                    .fontColor('#999999')
                }
                .width('100%')
                .justifyContent(FlexAlign.Center)
                .padding(16)
              }
            }
          }
        }
        .padding({ left: 0, right: 0 })
        .backgroundColor('#F8F8F8')
        .onReachStart(() => {
          // 滚动到顶部时触发加载更多
          console.info('[会议列表] 滚动到顶部，触发加载更多');
          this.onLoadMore();
        })
      }
      .onStateChange((refreshStatus: RefreshStatus) => {
        console.info('[会议列表] 刷新状态变化:', refreshStatus);
      })
      .onRefreshing(() => {
        // 下拉刷新触发
        console.info('[会议列表] 触发下拉刷新');
        this.onRefresh();
      })
      .margin({ top: 8 })
      .layoutWeight(1)

      // 底部导航栏
      Row() {
        Column() {
          Image(this.currentIndex === 0 ? $r('app.media.hs_list_2') : $r('app.media.hs_list'))
            .width(24)
            .height(24)
            .objectFit(ImageFit.Contain)
          Text('主页')
            .fontSize(10)
            .fontColor(this.currentIndex === 0 ? '#0A59F7' : '#999999')
            .margin({ top: 2 })
        }
        .justifyContent(FlexAlign.Center)
        .layoutWeight(1)
        .onClick(() => {
          this.currentIndex = 0;
        })

        Column() {
          Image(this.currentIndex === 1 ? $r('app.media.hs_setting_2') : $r('app.media.hs_setting'))
            .width(24)
            .height(24)
            .objectFit(ImageFit.Contain)
          Text('设置')
            .fontSize(10)
            .fontColor(this.currentIndex === 1 ? '#0A59F7' : '#999999')
            .margin({ top: 2 })
        }
        .justifyContent(FlexAlign.Center)
        .layoutWeight(1)
        .onClick(() => {
          this.currentIndex = 1;
          const routeParams: RouterParams = {
            origin: 'www'
          };
          builderRouterModel(RouterNameConstants.HomeIndex, BuilderNameConstants.SettingsPage, routeParams);
        })
      }
      .width('100%')
      .height(60)
      .backgroundColor(Color.White)
      .border({ width: { top: 0.5 }, color: '#E5E5E5' })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F8F8')
  }
}


import { promptAction } from '@kit.ArkUI';
import { serverConfig } from '../config/ServerConfig';
import { Request } from '../http/request';
import { LoginReqModel } from '../model/LoginReqModel';
import common from '@ohos.app.ability.common';
import preferences from '@ohos.data.preferences';
import { Context } from '@kit.AbilityKit';

// 登录响应接口
export interface LoginResponse {
  userId: number;
  userName: string;
  realName: string;
  nickName: string;
  role: string;
  token: string;
  email: string;
  // 根据实际返回数据添加其他字段
}

// 用户信息接口
export interface UserInfoData {
  userId: number;
  userName: string;
  realName: string;
  nickName: string;
  roles: string;
  token: string;
  email: string;
}

/**
 * 用户信息Preferences管理类
 * 负责用户信息的持久化存储，参考ServerConfig的实现
 */
export class UserPreferences {
  private static instance: UserPreferences = new UserPreferences();

  // Preferences存储实例
  private preferencesStore: preferences.Preferences | null = null;

  // Preferences存储名称和键值
  private static readonly PREFERENCES_NAME = 'user_info';
  private static readonly USER_INFO_KEY = 'userInfo';
  private static readonly IS_LOGGED_IN_KEY = 'isLoggedIn';
  private static readonly CACHED_USERNAME_KEY = 'cachedUsername';

  private constructor() {
    // 私有构造函数，确保单例模式
    this.initPreferences();
  }

  public static getInstance(): UserPreferences {
    return UserPreferences.instance;
  }

  /**
   * 初始化Preferences存储
   */
  private async initPreferences(): Promise<void> {
    try {
      // 获取应用上下文
      const context = AppStorage.Get<Context>('context') as Context;
      if (!context) {
        console.error('[UserPreferences] 无法获取应用上下文，使用AppStorage作为备选方案');
        return;
      }

      // 获取Preferences实例
      this.preferencesStore = await preferences.getPreferences(context, UserPreferences.PREFERENCES_NAME);
      console.info('[UserPreferences] Preferences初始化成功');

    } catch (error) {
      console.error('[UserPreferences] Preferences初始化失败:', error);
    }
  }

  /**
   * 保存用户信息到Preferences
   */
  public async saveUserInfo(userInfo: UserInfoData): Promise<void> {
    try {
      if (!this.preferencesStore) {
        console.warn('[UserPreferences] Preferences未初始化，使用AppStorage作为备选');
        // 降级到AppStorage
        AppStorage.SetOrCreate<boolean>('isLoggedIn', true);
        AppStorage.SetOrCreate<string>('userInfo', JSON.stringify(userInfo));
        return;
      }

      // 保存到Preferences
      await this.preferencesStore.put(UserPreferences.USER_INFO_KEY, JSON.stringify(userInfo));
      await this.preferencesStore.put(UserPreferences.IS_LOGGED_IN_KEY, true);
      await this.preferencesStore.flush();

      // 同步到AppStorage以便UI组件使用
      AppStorage.SetOrCreate<boolean>('isLoggedIn', true);
      AppStorage.SetOrCreate<string>('userInfo', JSON.stringify(userInfo));

      console.info('[UserPreferences] 用户信息已保存到Preferences');
    } catch (error) {
      console.error('[UserPreferences] 保存用户信息到Preferences失败:', error);
      // 降级到AppStorage
      AppStorage.SetOrCreate<boolean>('isLoggedIn', true);
      AppStorage.SetOrCreate<string>('userInfo', JSON.stringify(userInfo));
    }
  }

  /**
   * 从Preferences加载用户信息
   */
  public async loadUserInfo(): Promise<UserInfoData | null> {
    try {
      if (!this.preferencesStore) {
        console.warn('[UserPreferences] Preferences未初始化，从AppStorage加载');
        // 降级到AppStorage
        const userInfoStr = AppStorage.Get<string>('userInfo');
        if (userInfoStr) {
          return JSON.parse(userInfoStr) as UserInfoData;
        }
        return null;
      }

      // 从Preferences获取用户信息
      const userInfoStr = await this.preferencesStore.get(UserPreferences.USER_INFO_KEY, '') as string;
      const isLoggedIn = await this.preferencesStore.get(UserPreferences.IS_LOGGED_IN_KEY, false) as boolean;

      if (userInfoStr && userInfoStr.trim() !== '') {
        const userInfo = JSON.parse(userInfoStr) as UserInfoData;

        // 同步到AppStorage以便UI组件使用
        AppStorage.SetOrCreate<boolean>('isLoggedIn', isLoggedIn);
        AppStorage.SetOrCreate<string>('userInfo', userInfoStr);

        console.info('[UserPreferences] 从Preferences加载用户信息成功');
        return userInfo;
      }

      return null;
    } catch (error) {
      console.error('[UserPreferences] 从Preferences加载用户信息失败:', error);
      // 降级到AppStorage
      const userInfoStr = AppStorage.Get<string>('userInfo');
      if (userInfoStr) {
        try {
          return JSON.parse(userInfoStr) as UserInfoData;
        } catch (parseError) {
          console.error('[UserPreferences] 解析AppStorage用户信息失败:', parseError);
        }
      }
      return null;
    }
  }

  /**
   * 清除用户信息
   */
  public async clearUserInfo(): Promise<void> {
    try {
      if (!this.preferencesStore) {
        console.warn('[UserPreferences] Preferences未初始化，清除AppStorage');
        // 降级到AppStorage
        AppStorage.SetOrCreate<boolean>('isLoggedIn', false);
        AppStorage.SetOrCreate<string>('userInfo', '');
        return;
      }

      // 清除Preferences
      await this.preferencesStore.delete(UserPreferences.USER_INFO_KEY);
      await this.preferencesStore.delete(UserPreferences.IS_LOGGED_IN_KEY);
      await this.preferencesStore.flush();

      // 同步清除AppStorage
      AppStorage.SetOrCreate<boolean>('isLoggedIn', false);
      AppStorage.SetOrCreate<string>('userInfo', '');

      console.info('[UserPreferences] 用户信息已从Preferences清除');
    } catch (error) {
      console.error('[UserPreferences] 清除Preferences用户信息失败:', error);
      // 降级到AppStorage
      AppStorage.SetOrCreate<boolean>('isLoggedIn', false);
      AppStorage.SetOrCreate<string>('userInfo', '');
    }
  }

  /**
   * 保存缓存的用户名
   */
  public async saveCachedUsername(username: string): Promise<void> {
    try {
      if (!this.preferencesStore) {
        console.warn('[UserPreferences] Preferences未初始化，使用AppStorage保存用户名');
        AppStorage.SetOrCreate<string>('cachedUsername', username);
        return;
      }

      await this.preferencesStore.put(UserPreferences.CACHED_USERNAME_KEY, username);
      await this.preferencesStore.flush();

      // 同步到AppStorage
      AppStorage.SetOrCreate<string>('cachedUsername', username);

      console.info('[UserPreferences] 缓存用户名已保存到Preferences:', username);
    } catch (error) {
      console.error('[UserPreferences] 保存缓存用户名到Preferences失败:', error);
      AppStorage.SetOrCreate<string>('cachedUsername', username);
    }
  }

  /**
   * 获取缓存的用户名
   */
  public async getCachedUsername(): Promise<string> {
    try {
      if (!this.preferencesStore) {
        console.warn('[UserPreferences] Preferences未初始化，从AppStorage获取用户名');
        return AppStorage.Get<string>('cachedUsername') || '';
      }

      const cachedUsername = await this.preferencesStore.get(UserPreferences.CACHED_USERNAME_KEY, '') as string;

      // 同步到AppStorage
      AppStorage.SetOrCreate<string>('cachedUsername', cachedUsername);

      return cachedUsername;
    } catch (error) {
      console.error('[UserPreferences] 从Preferences获取缓存用户名失败:', error);
      return AppStorage.Get<string>('cachedUsername') || '';
    }
  }

  /**
   * 确保Preferences已初始化（供外部调用）
   */
  public async ensureInitialized(): Promise<void> {
    if (!this.preferencesStore) {
      await this.initPreferences();
    }
  }
}

// 用户信息存储类
export class UserInfo {
  private static instance: UserInfo = new UserInfo();

  userId: number = 0;
  userName: string = '';
  realName: string = '';
  nickName: string = '';
  roles: string = '';
  token: string = '';
  email: string = '';

  // UserPreferences实例
  private userPreferences: UserPreferences;

  private constructor() {
    // 私有构造函数，确保单例模式
    this.userPreferences = UserPreferences.getInstance();
    // 注意：不在构造函数中加载用户信息，因为它是异步的
    // 用户信息将在ensureInitialized()方法中加载
  }

  public static getInstance(): UserInfo {
    return UserInfo.instance;
  }

  /**
   * 从Preferences加载用户信息
   */
  private async loadUserInfoFromPreferences(): Promise<void> {
    try {
      const userInfo = await this.userPreferences.loadUserInfo();
      if (userInfo) {
        this.userId = userInfo.userId || 0;
        this.userName = userInfo.userName || '';
        this.realName = userInfo.realName || '';
        this.nickName = userInfo.nickName || '';
        this.roles = userInfo.roles || '';
        this.token = userInfo.token || '';
        this.email = userInfo.email || '';
        console.info('[UserInfo] 从Preferences加载用户信息成功');
      }
    } catch (error) {
      console.error('[UserInfo] 从Preferences加载用户信息失败:', error);
    }
  }
  
  // 清除用户信息
  public async clear(): Promise<void> {
    this.userId = 0;
    this.userName = '';
    this.realName = '';
    this.nickName = '';
    this.roles = '';
    this.token = '';
    this.email = '';

    // 清除Preferences存储
    await this.userPreferences.clearUserInfo();
    console.info('[UserInfo] 用户信息已清除');
  }
  
  // 保存用户信息
  public async saveUserInfo(info: UserInfoData): Promise<void> {
    this.userId = info.userId || 0;
    this.userName = info.userName || '';
    this.realName = info.realName || '';
    this.nickName = info.nickName || '';
    this.roles = info.roles || '';
    this.token = info.token || '';
    this.email = info.email || '';

    // 保存到Preferences
    await this.userPreferences.saveUserInfo(info);
    console.info('[UserInfo] 用户信息已保存');
  }
  
  // 判断用户是否已登录（不包括匿名用户）
  public isLoggedIn(): boolean {
    // 排除匿名用户：匿名用户有token但userId为-1
    return this.token !== '' && this.userId !== -1;
  }

  // 判断是否为匿名用户
  public isAnonymousUser(): boolean {
    return this.userId === -1 && this.userName === 'anonymous_user';
  }

  // 判断是否为有效用户（包括匿名用户）
  public isValidUser(): boolean {
    return this.token !== '';
  }

  /**
   * 确保UserPreferences已初始化并加载用户信息
   */
  public async ensureInitialized(): Promise<void> {
    await this.userPreferences.ensureInitialized();
    // 加载用户信息
    await this.loadUserInfoFromPreferences();
  }

  /**
   * 保存缓存的用户名
   */
  public async saveCachedUsername(username: string): Promise<void> {
    await this.userPreferences.saveCachedUsername(username);
  }

  /**
   * 获取缓存的用户名
   */
  public async getCachedUsername(): Promise<string> {
    return await this.userPreferences.getCachedUsername();
  }
}

export class loginApi {
  /**
   * 用户登录
   * @param params 登录参数
   * @returns 登录结果Promise
   */
  static async login(params: LoginReqModel): Promise<boolean> {
    try {
      // 构建登录URL - 使用动态服务器地址
      const serverUrl = serverConfig.getServerUrl();
      console.info('[登录] 服务器地址:', serverUrl);

      const loginUrl = serverConfig.getLoginApiUrl();
      console.info('[登录] 完整登录URL:', loginUrl);
      console.info('[登录] 请求参数:', JSON.stringify(params));

      // 验证URL是否有效
      if (!loginUrl || loginUrl.includes('undefined')) {
        throw new Error('服务器地址配置无效，请检查服务器设置');
      }

      // 发送登录请求，指定返回类型为文本，因为返回的是XML
      const response = await Request.post<string>(loginUrl, params);
      
      // 解析XML响应
      if (response) {
        try {
          // 使用正则表达式检查是否包含认证成功标签
          const successMatch = response.match(/<cas:authenticationSuccess>([\s\S]*?)<\/cas:authenticationSuccess>/i);
          
          if (successMatch) {
            // 登录成功，解析用户信息
            const userInfo: UserInfoData = {
              userId: 0,
              userName: '',
              realName: '',
              nickName: '',
              roles: '',
              token: '',
              email: ''
            };
            
            // 提取用户ID
            const userIdMatch = response.match(/<cas:userid>\s*([^<]+)\s*<\/cas:userid>/i);
            if (userIdMatch) {
              userInfo.userId = parseInt(userIdMatch[1].trim());
            }
            
            // 提取用户名
            const userNameMatch = response.match(/<cas:userName>\s*([^<]+)\s*<\/cas:userName>/i);
            if (userNameMatch) {
              userInfo.userName = userNameMatch[1].trim();
            }
            
            // 提取真实姓名
            const firstNameMatch = response.match(/<cas:firstName>\s*([^<]+)\s*<\/cas:firstName>/i);
            if (firstNameMatch) {
              userInfo.realName = firstNameMatch[1].trim();
            }
            
            // 提取昵称
            const nickNameMatch = response.match(/<cas:nickName>\s*([^<]+)\s*<\/cas:nickName>/i);
            if (nickNameMatch) {
              userInfo.nickName = nickNameMatch[1].trim();
            }
            
            // 提取邮箱
            const emailMatch = response.match(/<cas:email>\s*([^<]+)\s*<\/cas:email>/i);
            if (emailMatch) {
              userInfo.email = emailMatch[1].trim();
            }
            
            // 提取角色
            const rolesMatch = response.match(/<cas:roles>\s*([^<]+)\s*<\/cas:roles>/i);
            if (rolesMatch) {
              userInfo.roles = rolesMatch[1].trim();
            }
            
            // 提取票据
            const ticketMatch = response.match(/<cas:ticket>\s*([^<]+)\s*<\/cas:ticket>/i);
            if (ticketMatch) {
              userInfo.token = ticketMatch[1].trim();
            }
            
            // 保存用户信息
            await UserInfo.getInstance().saveUserInfo(userInfo);
            
            console.info('登录成功，用户信息:', JSON.stringify(userInfo));
            promptAction.showToast({ message: '登录成功' });
            return true;
          } else {
            console.error('登录失败：认证未通过');
            promptAction.showToast({ message: '登录失败：认证未通过' });
            return false;
          }
        } catch (parseError) {
          console.error('XML解析错误:', parseError);
          promptAction.showToast({ message: '登录失败：响应格式错误' });
          return false;
        }
      } else {
        promptAction.showToast({ message: '登录失败' });
        return false;
      }
    } catch (error) {
      console.error('登录错误:', error);
      promptAction.showToast({ message: '登录失败，请检查网络连接' });
      return false;
    }
  }
  
  /**
   * 获取当前登录用户信息
   * @returns 用户信息
   */
  static getCurrentUser(): UserInfo {
    return UserInfo.getInstance();
  }
  
  /**
   * 退出登录
   */
  static async logout(): Promise<void> {
    await UserInfo.getInstance().clear();
    promptAction.showToast({ message: '已退出登录' });
  }
}
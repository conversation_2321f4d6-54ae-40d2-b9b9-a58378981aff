// 聊天消息接口
export interface ChatMessage {
  /** 消息 ID */
  id:number

  /** 用户 ID */
  uid: number;

  /** 用户名 */
  username: string;

  /** 时间 */
  date: string;

  /** 消息内容 */
  message: string;

  /** 是否公聊 */
  isPublic: boolean;

  /** 是否是收到的消息 */
  isComeMsg?: boolean;

  /** 是否显示时间 */
  isShowTime?: boolean;

  /** 是否已读消息 */
  isReaded?: boolean;
}

//共享白板
export interface ANTINFO {
  type: number;
  docId: number;
  pageId: number;
  id: number;
  ptCount: number;
  pt_x: number[];  // 动态数组
  pt_y: number[];  // 动态数组
  strText: string;
  rgb: number;
  fontSize: number;
  lineType: number;
  lineWidth: number;
  ownerId: number;
  left: number;
  right: number;
  top: number;
  bottom: number;
  ratio:boolean
}


#ifndef _SDK_DEFINE_H_H_H
#define _SDK_DEFINE_H_H_H


#ifndef WIN32 
	#define API_EXPORT
	#define AS_API_EXPORT
	#define DS_API_EXPORT
	#define CHAT_API_EXPORT
	#define CONTRLOOER_API_EXPORT
	#define ADAPTER_API_EXPORT
	#define ANNOTATION_API_EXPORT
	#define AUDIO_API_EXPORT
	#define VIDEO_API_EXPORT
	#define WINAUDIO_API_EXPORT
	#define WINVIDEO_API_EXPORT
	#define USER_API_EXPORT
	#define IM_API_EXPORT

#else
	#ifdef API_EXPORTS
	#define API_EXPORT __declspec(dllexport)
	#else 
	#define	API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef ANNOTATION_API_EXPORTS
	#define ANNOTATION_API_EXPORT __declspec(dllexport)
	#else 
	#define	ANNOTATION_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef AS_API_EXPORTS
	#define AS_API_EXPORT __declspec(dllexport)
	#else 
	#define	AS_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef DS_API_EXPORTS
	#define DS_API_EXPORT __declspec(dllexport)
	#else 
	#define	DS_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef	CHAT_API_EXPORTS
	#define CHAT_API_EXPORT __declspec(dllexport)
	#else 
	#define	CHAT_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef	CONTRLOOER_API_EXPORTS
	#define CONTRLOOER_API_EXPORT __declspec(dllexport)
	#else 
	#define	CONTRLOOER_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef ADAPTER_API_EXPORTS
	#define ADAPTER_API_EXPORT __declspec(dllexport)
	#else 
	#define ADAPTER_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef AUDIO_API_EXPORTS
	#define AUDIO_API_EXPORT __declspec(dllexport)
	#else 
	#define AUDIO_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef VIDEO_API_EXPORTS
	#define VIDEO_API_EXPORT __declspec(dllexport)
	#else 
	#define VIDEO_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef WINAUDIO_API_EXPORTS
	#define WINAUDIO_API_EXPORT __declspec(dllexport)
	#else 
	#define WINAUDIO_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef WINVIDEO_API_EXPORTS
	#define WINVIDEO_API_EXPORT __declspec(dllexport)
	#else 
	#define WINVIDEO_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef USER_API_EXPORTS
	#define USER_API_EXPORT __declspec(dllexport)
	#else 
	#define USER_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef RECORDPLAYER_API_EXPORTS
	#define RECORDPLAYER_API_EXPORT __declspec(dllexport)
	#else 
	#define RECORDPLAYER_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef WB_API_EXPORTS
	#define WB_API_EXPORT __declspec(dllexport)
	#else 
	#define WB_API_EXPORT __declspec(dllimport) 
	#endif

	#ifdef IM_API_EXPORTS
	#define IM_API_EXPORT __declspec(dllexport)
	#else 
	#define IM_API_EXPORT __declspec(dllimport) 
	#endif

#endif

#ifndef NULL
#ifdef __cplusplus
#define NULL	0
#else
#define NULL	((void *)0)
#endif
#endif

enum UpdateRosterInfoType
{
	UpdateRosterInfoType_UNKNOWN = -1,
	UpdateRosterInfoType_Add = 0,
	UpdateRosterInfoType_Remove =1,
	UpdateRosterInfoType_Modify = 2
};

/////////////ICONFCLIENT.H
enum CONF_ROLE
{
	CONF_ROLE_OTHERS			= 0,
	CONF_ROLE_HOST				= 1,       // host(1)
	CONF_ROLE_SPEAKER			= 2,       // presenter(1)
	CONF_ROLE_ASSISTANT			= 4,       // assistant(4)
	CONF_ROLE_ATTENDEE			= 8,       // attendee(n)
	CONF_ROLE_ANDROID			= 16,       // android(n)

	CONF_ROLE_MOBILE_DEVICES	= 1 << 8,        //mobile devices
	CONF_ROLE_PC				= 1 << 9,        //PC 
	CONF_ROLE_SUPERVISOR		= 1 << 10,		   //supervisor
	CONF_ROLE_AUDIT				= 1 << 11,	   //sitter-in
	CONF_ROLE_FLASH				= 1 << 12,	   //flash

	CONF_ROLE_AUDIENCE			= 1 << 13,        // audience
	CONF_ROLE_H323				= 1 << 14,        // H323 gateway
	CONF_ROLE_TELEPHONE			= 1 << 15,        // pstn phone
	CONF_ROLE_DVR				= 1 << 16,        // dvr user
	CONF_ROLE_SIP				= 1 << 17,        // sip user
	CONF_ROLE_HIDEATTENDEE		= 1 << 18,		   // hide attendee
	CONF_ROLE_TELE_CONFERENCE	= 1 << 19,       // Tele-conference
	CONF_ROLE_MEETINGBOX		= 1 << 20,		   // MeetingBox

	CONF_ROLE_SUPERVISOR_MOBILEPC = 1 << 21,			//supervisor mobilepc
	CONF_ROLE_CLOUDRECORD		= (1 << 22),       // Record Server
	CONF_ROLE_APPLET			= (1 << 23),       // APPLET
	CONF_ROLE_CONFCONTROL		= (1 << 24),       // Conf Control
};

typedef bool STATE;

enum  USER_PRIVILEDGE_TYPE
{
	USER_PRIVILEDGE_TYPE_UNKNOWN = 0,
	USER_PRIVILEDGE_TYPE_PUBLIC_CHAT = 1,
	USER_PRIVILEDGE_TYPE_PRIVATE_CHAT = 2,
	USER_PRIVILEDGE_TYPE_AUDIO = 3,
	USER_PRIVILEDGE_TYPE_VIDEO = 4,
	USER_PRIVILEDGE_TYPE_DS_ANNOTATION = 5,
	USER_PRIVILEDGE_TYPE_DS_DOCUMENT,
	USER_PRIVILEDGE_TYPE_DS_PAGE,
	USER_PRIVILEDGE_TYPE_DS_SAVE,
	USER_PRIVILEDGE_TYPE_FT_UPLOAD,
	USER_PRIVILEDGE_TYPE_FT_DOWNLOAD,
	USER_PRIVILEDGE_TYPE_RENAME,
	USER_PRIVILEDGE_TYPE_AS,
	USER_PRIVILEDGE_TYPE_RECORD
};
/////////////ICONFCLIENT.H
//#include "IComponentBase.h"

/////////////ICONFDATA.H
typedef unsigned long IL_NODE_ID;
/////////////ICONFDATA.H

/////////////ICONFAS.H
enum SendDataType
{
	SendDataType_UnKnown = 0,
	SendDataType_JPG,
	SendDataType_H264,
	SendDataType_MouseEvent,
	SendDataType_KeyboardEvent,
};
/////////////
/////////////ICONFAS.H
typedef enum
{
	MAIN_PROCESS = 0,
	OTHER_PROCESSS
}WndHandle_Type;

typedef enum
{
	LIVE_STATE_START = 0,
	LIVE_STATE_PAUSE,
	LIVE_STATE_STOP
}Live_State;

#endif


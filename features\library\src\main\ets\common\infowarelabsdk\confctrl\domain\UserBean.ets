// // UserBean.ets
// import { userCommonConstants } from '../../confctrl/UserCommon';
// import { MessageBean } from './MessageBean'
//
// @Observed
// export class UserBean {
//   // 属性定义
//   channelId: number = 0;
//   isSelected: boolean = false;
//   isFouse: boolean = false;
//   uid: number = 0;
//   username: string = "";
//   password: string = "";
//   nickname: string = "";
//   realname: string = "";
//   messages: MessageBean[] = [];
//   role: number = 0;
//   device: number = 0;
//   audioOpen: boolean = false;
//   isHaveMic: boolean = true;
//   videoOpen: boolean = false;
//   isReadedMsg: boolean = true;
//   isShareVideo: boolean = false;
//   isHaveVideo: boolean = false;
//   createConfRole: string = "";
//   pointerColor: number = -1;
//   handUp: boolean = false;
//   channelIds: number[] = [];
//
//   // 方法定义
//
//   // Clone 方法
//   clone(): UserBean {
//     const bean = new UserBean();
//     bean.uid = this.uid;
//     bean.isReadedMsg = this.isReadedMsg;
//     bean.username = this.username;
//     bean.password = this.password;
//     bean.audioOpen = this.audioOpen;
//     bean.videoOpen = this.videoOpen;
//     bean.channelId = this.channelId;
//     bean.createConfRole = this.createConfRole;
//     bean.device = this.device;
//     bean.isFouse = this.isFouse;
//     bean.isHaveMic = this.isHaveMic;
//     bean.isHaveVideo = this.isHaveVideo;
//     bean.nickname = this.nickname;
//     bean.realname = this.realname;
//     bean.pointerColor = this.pointerColor;
//     bean.role = this.role;
//     bean.isSelected = this.isSelected;
//     bean.isShareVideo = this.isShareVideo;
//     return bean;
//   }
//
//   // Channel 操作方法
//   addChannel(channelId: number): void {
//     this.channelIds.push(channelId);
//   }
//
//   removeChannel(channelId: number): void {
//     this.channelIds = this.channelIds.filter(id => id !== channelId);
//   }
//
//   // 比较方法（静态方法）
//   static compare(lhs: UserBean, rhs: UserBean): number {
//     if (lhs.uid === userCommonConstants.ALL_USER_ID) return -1;
//     if (rhs.uid === userCommonConstants.ALL_USER_ID) return 1;
//
//     if (lhs.role === 6) return -1;
//     if (rhs.role === 6) return 1;
//
//     if (lhs.role === 0 && rhs.role !== 0) return 1;
//     if (rhs.role === 0 && lhs.role !== 0) return -1;
//
//     if (lhs.role < rhs.role) return -1;
//     if (lhs.role > rhs.role) return 1;
//
//     return 0;
//   }
//
//   setUid(uid : number) {
//     this.uid = uid;
//   }
//
//   setUsername(username : string) {
//     this.username = username;
//   }
//
//   setRole(role : number) {
//     this.role = role;
//   }
//
//   setDevice(device : number) {
//     this.device = device;
//   }
//
//   isAudioOpen() : boolean {
//     return this.audioOpen;
//   }
//
//   // 设置 videoOpen 状态
//   setVideoOpen(videoOpen: boolean): void {
//     this.videoOpen = videoOpen;
//   }
//
//   // 设置 isReadedMsg 状态
//   setReadedMsg(isReadedMsg: boolean): void {
//     this.isReadedMsg = isReadedMsg;
//   }
//
//   // 获取 isReadedMsg 状态
//   isReadedMsg(): boolean {
//     return this.isReadedMsg;
//   }
//
//   // 获取 pointerColor
//   getPointerColor(): number {
//     return this.pointerColor;
//   }
//
//   // 设置 pointerColor
//   setPointerColor(pointerColor: number): void {
//     this.pointerColor = pointerColor;
//   }
//
//   // 获取 channelIds
//   getChannelIds(): number[] {
//     return this.channelIds;
//   }
//
//   // 设置 channelIds
//   setChannelIds(channelIds: number[]): void {
//     this.channelIds = channelIds;
//   }
//
//   // 添加 channelId
//   addChannel(channelId: number): void {
//     this.channelIds.push(channelId);
//   }
//
//   // 移除 channelId
//   removeChannel(channelId: number): void {
//     this.channelIds = this.channelIds.filter(id => id !== channelId);
//   }
//
//
//
//   // 设置 isHaveVideo 状态
//   setHaveVideo(isHaveVideo: boolean): void {
//     this.isHaveVideo = isHaveVideo;
//   }
//
//   // 获取 createConfRole
//   getCreateConfRole(): string {
//     return this.createConfRole;
//   }
//
//   // 设置 createConfRole
//   setCreateConfRole(createConfRole: string): void {
//     this.createConfRole = createConfRole;
//   }
//
//   // 设置 isSelected 状态
//   setSelected(selected: boolean): void {
//     this.isSelected = selected;
//   }
//
//   // 设置 isFouse 状态
//   setFouse(fouse: boolean): void {
//     this.isFouse = fouse;
//   }
//
//   // 获取 channelId
//   getChannelId(): number {
//     return this.channelId;
//   }
//
//   // 设置 channelId
//   setChannelId(channelId: number): void {
//     this.channelId = channelId;
//   }
//
//   // 获取 handUp 状态
//   isHandUp(): boolean {
//     return this.handUp;
//   }
//
//   // 设置 handUp 状态
//   setHandUp(handUp: boolean): void {
//     this.handUp = handUp;
//   }
//
//   isVideoOpen(): boolean {
//     return this.videoOpen;
//   }
//
// }
//

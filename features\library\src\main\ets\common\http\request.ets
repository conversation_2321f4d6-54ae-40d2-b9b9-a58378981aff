import { http } from '@kit.NetworkKit'
import { BaseUrl } from './BasicUrl';
import { promptAction } from '@kit.ArkUI';
import { ResponsesData } from '../model/ResponsesModel';
import { callbackManager, CallBackManager } from '../infowarelabsdk/callback/CallBackManager';

function encodeChinese(str: string, encoding: 'url' | 'unicode'): string {
  let result = '';

  for (const char of str) {
    if (/[\u4e00-\u9fff]/.test(char)) { // 判断是否为中文字符
      if (encoding === 'url') {
        result += encodeURIComponent(char); // URL 编码
      } else if (encoding === 'unicode') {
        result += `\\u${char.charCodeAt(0).toString(16).padStart(4, '0')}`; // Unicode 编码
      }
    } else {
      result += char; // 非中文字符直接拼接
    }
  }

  return result;
}

function containsChinese(str: string): boolean {
  const chineseRegex = /[\u4e00-\u9fff]/;
  return chineseRegex.test(str);
}

function processString(str: string, encoding: 'url' | 'unicode'): string {
  if (!containsChinese(str)) {
    return str; // 如果没有中文，直接返回原字符串
  }

  return encodeChinese(str, encoding); // 转换并拼接
}


async function requestHttp<T>(url: string, method: http.RequestMethod = http.RequestMethod.GET, data ?: object) {
  let urlStr = url;
  const httpRequest = http.createHttp();
  let extraData = ''
  if (method === http.RequestMethod.GET || method === http.RequestMethod.POST) {
    if (data && Object.keys(data).length) {
      if (method === http.RequestMethod.GET) {
        urlStr += Object.keys(data).map(key => {
          if (data[key] === '') {
            return `${key}=`
          } else {
            return `${key}=${data[key]}`
          }
        }).join('&')
      }
    }
  }
  console.log('eeeUrl', urlStr)
  urlStr = processString(urlStr, 'url')
  let config: http.HttpRequestOptions = {
    method,
    readTimeout: 10000,
    extraData: method === http.RequestMethod.GET ? '' : extraData,
    header: {
      'Content-Type': method === http.RequestMethod.GET ? 'application/json' :
        'application/x-www-form-urlencoded',
    }
  }
  console.log('eeeError++:::' + JSON.stringify(config))
  try {
    const res = await httpRequest.request(urlStr, config)
    if (res.responseCode !== 200) {
      callbackManager.httpReqIsFailed = false
      promptAction.showToast({ message: `HTTP请求失败，状态码：${res.responseCode}` })
      throw new Error(`HTTP请求失败，状态码：${res.responseCode}`);
    } else {
      callbackManager.httpReqIsFailed = true
      return res.result as T
    }

  } catch (error) {
    return Promise.reject(error)
  } finally {
    httpRequest.destroy()
  }
}

export class Request {
  static get<T>(url: string, data?: object): Promise<T> {
    return requestHttp<T>(url, http.RequestMethod.GET, data)
  }

  static post<T>(url: string, data?: object): Promise<T> {
    return requestHttp<T>(url, http.RequestMethod.POST, data)
  }

  static put<T>(url: string, data?: object): Promise<T> {
    return requestHttp<T>(url, http.RequestMethod.PUT, data)
  }

  static delete<T>(url: string, data?: object): Promise<T> {
    return requestHttp<T>(url, http.RequestMethod.DELETE, data)
  }
}
# 匿名登录设置页面显示修改

## 修改内容

### 问题描述
之前匿名登录后，设置页面的账户项会显示匿名用户名（如"anonymous_user"），这不符合用户体验要求。用户希望匿名登录时账户项显示"未登录"。

### 解决方案
修改了`features/settings/src/main/ets/components/SettingsPage.ets`中的`updateLoginStatus()`方法，添加了对匿名用户的特殊处理。

### 具体修改

#### 1. 添加导入
```typescript
import { loginApi } from 'basic';
```

#### 2. 修改登录状态检查逻辑
```typescript
// 原来的逻辑
if (this.isLoggedIn && this.userInfoStr) {
  // 显示用户名
}

// 修改后的逻辑
const currentUser = loginApi.getCurrentUser();
if (currentUser.isLoggedIn() && this.isLoggedIn && this.userInfoStr) {
  // 只有正式登录用户才显示用户名
} else {
  // 未登录或匿名用户都显示"未登录"
}
```

### 逻辑说明

#### 用户状态分类：
1. **正式登录用户**：
   - `currentUser.isLoggedIn()` 返回 `true`
   - `this.isLoggedIn` 为 `true`
   - `this.userInfoStr` 有值
   - **显示**：用户名或真实姓名

2. **匿名用户**：
   - `currentUser.isLoggedIn()` 返回 `false`（因为排除了匿名用户）
   - `currentUser.isAnonymousUser()` 返回 `true`
   - **显示**："未登录"

3. **未登录用户**：
   - `currentUser.isLoggedIn()` 返回 `false`
   - `this.isLoggedIn` 为 `false`
   - **显示**："未登录"

### 用户体验改进

#### 修改前：
- 正式登录：显示用户名
- 匿名登录：显示"anonymous_user"
- 未登录：显示"未登录"

#### 修改后：
- 正式登录：显示用户名
- 匿名登录：显示"未登录"
- 未登录：显示"未登录"

### 测试验证

#### 测试步骤：
1. **匿名登录测试**：
   - 启动应用
   - 点击"匿名登录"
   - 进入设置页面
   - 验证账户项显示"未登录"

2. **正式登录测试**：
   - 从匿名状态退出
   - 进行正式登录
   - 进入设置页面
   - 验证账户项显示用户名

3. **状态切换测试**：
   - 从正式登录切换到匿名登录
   - 验证设置页面账户显示的变化

#### 预期结果：
- ✅ 匿名登录时账户显示"未登录"
- ✅ 正式登录时账户显示用户名
- ✅ 状态切换时显示正确更新
- ✅ 点击账户项的导航逻辑保持不变

### 技术细节

#### 关键方法：
- `currentUser.isLoggedIn()`：检查是否为正式登录用户（排除匿名用户）
- `currentUser.isAnonymousUser()`：检查是否为匿名用户
- `currentUser.isValidUser()`：检查是否为有效用户（包括匿名用户）

#### 状态判断优先级：
1. 首先检查是否为正式登录用户
2. 如果不是正式用户，统一显示"未登录"
3. 保持原有的UI更新机制

### 兼容性说明

- **向后兼容**：不影响现有的正式登录流程
- **功能保持**：账户项的点击导航功能保持不变
- **状态同步**：与其他页面的登录状态检查保持一致

### 总结

通过这个修改，匿名用户在设置页面看到的账户状态更加直观和友好，符合"匿名登录"的概念。用户不会看到技术性的用户名（如"anonymous_user"），而是看到更自然的"未登录"状态，同时保持了与正式登录用户的清晰区分。

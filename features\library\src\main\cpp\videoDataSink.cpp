// 修改 ClearAllDecoders() 函数,添加日志
void CVideoDataSink::ClearAllDecoders() {
    std::unique_lock<std::mutex> lock(decoderMutex_);    
    OH_LOG_Print(LOG_APP, LOG_INFO, 0xFF00, "VideoDataSink", "Clearing all decoders - count: %d", mapDecoder_.size());
    for (auto& pair : mapDecoder_) {
        if(pair.second) {
            OH_LOG_Print(LOG_APP, LOG_INFO, 0xFF00, "VideoDataSink", "Deleting decoder for channel %d", pair.first);
            delete pair.second;
        }
    }
    mapDecoder_.clear();
    OH_LOG_Print(LOG_APP, LOG_INFO, 0xFF00, "VideoDataSink", "All decoders cleared successfully");
}

// 修改 RemoveDecoder() 函数,添加日志
void CVideoDataSink::RemoveDecoder(int nChannelId) {
    std::unique_lock<std::mutex> lock(decoderMutex_);    
    OH_LOG_Print(LOG_APP, LOG_INFO, 0xFF00, "VideoDataSink", "Removing decoder for channel %d", nChannelId);
    if (mapDecoder_.count(nChannelId)) {
        delete mapDecoder_[nChannelId];
        mapDecoder_.erase(nChannelId);
        OH_LOG_Print(LOG_APP, LOG_INFO, 0xFF00, "VideoDataSink", "Decoder removed for channel %d", nChannelId);
    }
} 
export interface MeetingInfo {
  return:string;
  MeetingName: string;
  confId: string;
  MeetingCreatorID: string;
  MeetingPassword: string;
  InviteWebApi: string;
}

export function parseNameValuePairs(xmlStr: string): MeetingInfo {
  const result: MeetingInfo = {
    MeetingName: "",
    confId: "",
    MeetingCreatorID: "",
    MeetingPassword: "",
    InviteWebApi: "",
    return: ""
  }

  // 使用正则表达式提取 <ps> 内容
  const psMatch = xmlStr.match(/<ps>([\s\S]*?)<\/ps>/i);
  if (!psMatch) return result as MeetingInfo;

  // 提取所有 <p> 块
  const pBlocks = psMatch[1].match(/<p>[\s\S]*?<\/p>/gi) || [];
  // 提取confId 块
  const confIdBlocks = xmlStr.match(/<confId>\s*([^<]+)\s*<\/confId>/i)
  if(confIdBlocks){
    result.confId = confIdBlocks[1].trim()
  }
  const returnBlocks = xmlStr.match(/<return>\s*([^<]+)\s*<\/return>/i)
  if(returnBlocks){
    result.return = returnBlocks[1].trim()
  }
  pBlocks.forEach(pBlock => {
    // 提取 name 和 value
    const nameMatch = pBlock.match(/<n>([\s\S]*?)<\/n>/i);
    const valueMatch = pBlock.match(/<v>([\s\S]*?)<\/v>/i);

    switch (nameMatch?.[1]?.trim()){
      case 'MeetingName':
        result.MeetingName = valueMatch?.[1]?.trim() as string
      break;
      case 'confId':
        result.confId = valueMatch?.[1]?.trim() as string
      break;
      case 'MeetingCreatorID':
        result.MeetingCreatorID = valueMatch?.[1]?.trim() as string
      break;
      case 'MeetingPassword':
        result.MeetingPassword = valueMatch?.[1]?.trim() as string
      break;
      case 'InviteWebApi':
        result.InviteWebApi = valueMatch?.[1]?.trim() as string
      break
    }

  });
  console.log('xmlData',JSON.stringify(result))
  return result;
}


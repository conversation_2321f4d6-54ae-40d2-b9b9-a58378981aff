import testNapi from 'libhstinterface.so';
import { CallBackManager } from '../callback/CallBackManager';
import { Participant, ParticipantClass } from '../../../models'
import { BusinessType, RoleTypeEnum } from '../../Constant';
import { callbackManager } from '../callback/CallBackManager';

export type onRosterInfoUpdateCallbackFun = (params: Participant) => void

type onUserHandUpCallbackFun = (nUserId: number, bHandUp: boolean) => void
//
type onLeaveConferenceCallbackFun = (type: boolean) => void

type onUserRoleUpdateCallbackFun = (nUserId: number, role: number) => void

export let RosterInfoUpdateCallbacks: onRosterInfoUpdateCallbackFun [] = []

export let CurrentUserlist: Participant[] = []

function handleLeaveConference(nResult: number) {
  console.log('退会了', nResult)
  let handles = callbackManager.trigger(BusinessType.CONFERENCE_ON_LEAVE_CONFERENCE)
  if (handles) {
    handles(true)
  }
}


function handleRosterInfoUpdate(action: number, uid: number, name: string, role: number) {
  // 1. 添加参数验证
  if (typeof action !== 'number' || typeof uid !== 'number' || typeof role !== 'number' || uid === 0 || uid === 1) {
    console.error('Invalid parameters:', { action, uid, role });
    return;
  }

  if (checkUserRole(role)) {
    return
  }
  // 2. 安全地创建用户角色对象
  try {
    let userRole: Participant = {
      uid: uid,
      username: name ? name.toString() : '', // 确保name是字符串
      role: role,
      action: action
    }

    console.log('getRosterList__ before:', JSON.stringify(userRole), action);

    // 3. 确保checkUserRole函数存在且返回有效值
    //const updatedUserRole = checkUserRole(role, userRole);
    if (!userRole) {
      console.error('checkUserRole returned invalid result');
      return;
    }
    //userRole = updatedUserRole;
    let handlers = callbackManager.trigger(BusinessType.CONFERENCE_ON_ROSTER_INFO_UPDATE)
    if (handlers) {
      handlers(userRole)
    }
    // RosterInfoUpdateCallbacks.forEach((CallbackFn) => {
    //   if (typeof CallbackFn === 'function') {
    //     try {
    //       CallbackFn(userRole);
    //     } catch (error) {
    //       console.error('Callback execution error:', error);
    //     }
    //   }
    // });

    // 4. 使用严格相等运算符
    switch (action) {
      case 0:
        handleRoleJoin(userRole);
        break;
      case 1:
        handleRoleExit(userRole);
        break
    }

    // 5. 安全地调用回调函数
    // console.log('audio++++++222',RosterInfoUpdateCallbacks.length)
    // if (Array.isArray(RosterInfoUpdateCallbacks)) {
    //   RosterInfoUpdateCallbacks.forEach((CallbackFn) => {
    //     if (typeof CallbackFn === 'function') {
    //       try {
    //         CallbackFn(CurrentUserlist);
    //       } catch (error) {
    //         console.error('Callback execution error:', error);
    //       }
    //     }
    //   });
    // }
  } catch (error) {
    console.error('handleRosterInfoUpdate error:', error);
  }
}

const handleRoleJoin = (role: Participant) => {
  if (!CurrentUserlist.some(item => item.uid === role.uid)) {
    CurrentUserlist.push(role)
  }
}

const handleRoleExit = (role: Participant) => {
  CurrentUserlist = CurrentUserlist.filter(i => i.uid !== role.uid)

}

function handleUserHandUp(nUserId: number, bHandUp: boolean) {
  let handles = callbackManager.trigger(BusinessType.CONFERENCE_ON_HAND_UP);
  if (handles) {
    handles(nUserId, bHandUp)
  }
}

function handleUserRoleUpdate(nUserId: number, role: number) {
  let handles = callbackManager.trigger(BusinessType.CONFERENCE_ON_USER_ROLE_UPDATE)
  if (handles) {
    handles(nUserId, role)
  }
}

export const addRosterInfoUpdateCallback = (callback: onRosterInfoUpdateCallbackFun) => {
  callbackManager.register(BusinessType.CONFERENCE_ON_ROSTER_INFO_UPDATE, callback)
}

export const addUserHandUpCallback = (callback: onUserHandUpCallbackFun) => {
  callbackManager.register(BusinessType.CONFERENCE_ON_HAND_UP, callback)
}

export const addLeaveConferenceCallback = (callback: onLeaveConferenceCallbackFun) => {
  callbackManager.register(BusinessType.CONFERENCE_ON_LEAVE_CONFERENCE, callback)
}

export const addUserRoleUpdateCallback = (callback: onUserRoleUpdateCallbackFun) => {
  callbackManager.register(BusinessType.CONFERENCE_ON_USER_ROLE_UPDATE, callback)
}

export const removeRosterInfoUpdateCallback = (callback: onRosterInfoUpdateCallbackFun) => {
  callbackManager.unregister(BusinessType.CONFERENCE_ON_ROSTER_INFO_UPDATE)
}

export const removeUserHandUpCallback = () => {
  callbackManager.unregister(BusinessType.CONFERENCE_ON_HAND_UP)
}

export const removeLeaveConferenceCallback = () => {
  callbackManager.unregister(BusinessType.CONFERENCE_ON_LEAVE_CONFERENCE)
}

export const removeUserRoleUpdateCallback = () => {
  callbackManager.unregister(BusinessType.CONFERENCE_ON_USER_ROLE_UPDATE)
}

/**
 * 加入会议回调
 *
 * @param errCode 0:成功; -1:超时; -2:密码错误; -3:登录信息错误 ; -4:会议号错误; -5：未初始化; -6:站点错误; -7:加会失败; -8:未知错误
 */
export function JoinConference(xmlData: string): number {
  let type: number = testNapi.conference_JoinConference(xmlData)
  if (type) {
    callbackManager.joinFailed = true
  }
  return type
}

export function LeaveConference(nResult: number): number {
  let t: number = testNapi.conference_LeaveConference(nResult);
  return t
}

function CloseConference() {
  testNapi.conference_CloseConference()
}

function onRosterUpdate() {

}

//加入会议回调代码块------------start--------------------
export function handleJoinConference(nResult: number) {
  JoinConferenceCallback(nResult)
}

let JoinConferenceCallback: (nResult: number) => void = () => {

}

export function onJoinConference(callback: (nResult: number) => void) {
  JoinConferenceCallback = callback
}


function checkUserRole(role: number) {
  let type: boolean = false
  if ((role & RoleTypeEnum.CONF_ROLE_H323) == RoleTypeEnum.CONF_ROLE_H323) {
    type = true
  } else if ((role & RoleTypeEnum.CONF_ROLE_HIDEATTENDEE) == RoleTypeEnum.CONF_ROLE_HIDEATTENDEE) {
    type = true
  } else if ((role & RoleTypeEnum.CONF_ROLE_CONFCONTROL) == RoleTypeEnum.CONF_ROLE_CONFCONTROL) {
    type = true
  } else if ((role & RoleTypeEnum.CONF_ROLE_CLOUDRECORD) == RoleTypeEnum.CONF_ROLE_CLOUDRECORD) {
    type = true
  }
  return type
}

//加入会议回调代码块--------------end--------------------
export function conference_registerCallback() {
  testNapi.registerCallback('conference_onRosterInfoUpdate', handleRosterInfoUpdate)
  testNapi.registerCallback('conference_onJoinConference', handleJoinConference)
  testNapi.registerCallback('conference_onHandUp', handleUserHandUp)
  testNapi.registerCallback("conference_onLeaveConference", handleLeaveConference);
  testNapi.registerCallback("conference_onUserRoleUpdate", handleUserRoleUpdate)
  callbackManager.initIsFailed = true
}

export function conference_unregisterCallback() {
  RosterInfoUpdateCallbacks = []
  CurrentUserlist = []
  testNapi.unregisterCallback('conference_onRosterInfoUpdate', handleRosterInfoUpdate)
  testNapi.unregisterCallback('conference_onJoinConference', handleJoinConference)
  testNapi.unregisterCallback('conference_onHandUp', handleUserHandUp)
}
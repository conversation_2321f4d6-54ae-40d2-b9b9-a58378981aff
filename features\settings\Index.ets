
import { BuilderNameConstants } from 'routermoudel'

export function harInit(builderName:string){
  switch (builderName){
    case BuilderNameConstants.SettingsPage:
      import('./src/main/ets/components/SettingsPage')
      break
    case BuilderNameConstants.LoginPage:
      import('./src/main/ets/components/LoginPage')
      break
    case BuilderNameConstants.LogoutPage:
      import('./src/main/ets/components/LogoutPage')
      break
    case BuilderNameConstants.ServerSettingsPage:
      import('./src/main/ets/components/ServerSettingsPage')
      break
  }
}



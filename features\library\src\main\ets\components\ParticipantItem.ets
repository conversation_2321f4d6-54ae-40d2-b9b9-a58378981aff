import { UserCommon } from '../common/infowarelabsdk/confctrl/UserCommon'
import { Participant, ParticipantClass } from '../models'

// 参会者列表项组件
@Component
export struct ParticipantItem {
  @Prop userInfo: Participant
  @ObjectLink participant: ParticipantClass
  onChatClick: () => void = () => {
  }
  onKick: () => void = () => {
  }

  build() {
    Column() {
      Row({ space: 10 }) {
        //左侧图标
        Row() {
          if (UserCommon.getDeviceType(this.participant.device!) === UserCommon.RT_DEVICE_MOBILE) {
            this.deviceIcon($r('app.media.ic_att_device_mobile'))
          } else if (UserCommon.getDeviceType(this.participant.device!) === UserCommon.RT_DEVICE_PC) {
            this.deviceIcon($r('app.media.ic_att_device_pc'))
          } else if (UserCommon.getDeviceType(this.participant.device!) === UserCommon.RT_DEVICE_TELEPHONE) {
            this.deviceIcon($r('app.media.ic_att_device_tel'))
          } else if (UserCommon.getDeviceType(this.participant.device!) === UserCommon.RT_DEVICE_MEETINGBOX) {
            this.deviceIcon($r('app.media.ic_att_device_meetingbox'))
          } else if (UserCommon.getDeviceType(this.participant.device!) === UserCommon.RT_DEVICE_H323) {
            this.deviceIcon($r('app.media.ic_att_device_h323'))
          } else if (UserCommon.getDeviceType(this.participant.device!) === UserCommon.CONF_ROLE_APPLET) {
            this.deviceIcon($r('app.media.ic_att_device_web'))
          }
        }
        .width(40)
        .height(40)
        .padding(9)
        .justifyContent(FlexAlign.Center)

        //用户入会名称
        Row() {
          Text(this.participant.nickname)
            .fontSize(14)
            .fontWeight(500)
            .constraintSize({ maxWidth: 100 })
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
          if (this.participant.role! === 1) {
            Text('(主持人)')
              .fontSize(14)
              .fontWeight(500)
              .fontColor(Color.Blue)
          }
          if (this.participant.uid == this.userInfo.uid) {
            Text('(我)')
              .fontSize(14)
              .fontWeight(500)
              .fontColor(Color.Blue)
          }
        }


        Blank()
        //如果是公聊的话就不显示音视频图标

        Row({ space: 20 }) {
          if (this.participant.handUp) {
            Image($r('app.media.hst_list_handup'))
              .width(13)
          }

          Image(this.participant.videoOpen ? $r('app.media.ic_att_video_on') : $r('app.media.ic_att_video_off'))
            .width(12)

          Image(this.participant.audioOpen ? $r('app.media.ic_att_audio_on') : $r('app.media.ic_att_audio_off'))
            .width(12)

          //占位
          Image('').width(12)
          // //不是主持人显示踢出图标
          // if (!this.userData.isHost) {
          //   Image($r('app.media.ic_att_kick'))
          //     .width(13)
          //     .onClick(() => {
          //       this.kick()
          //     })
          // } else {
          //   //占位
          //   Image('').width(13)
          // }
        }

      }
      .width('100%')
      .margin({ bottom: 15 })
      .onClick(() => {
        this.onChatClick()
      })

      Divider()
    }
    .padding({ left: 15, right: 15, bottom: 15 })
    .width('100%')

  }

  @Builder
  deviceIcon(img: ResourceStr) {
    Stack({ alignContent: Alignment.TopEnd }) {
      Image(img).objectFit(ImageFit.Contain)
      if (!this.participant.isReadedMsg) {
        Text()
          .width(8)
          .aspectRatio(1)
          .borderRadius(4)
          .backgroundColor(Color.Red)
      }
    }
  }

  kick() {
    AlertDialog.show({
      message: "\n" + `确认请出${this.participant.nickname}？` + "\n",
      buttons: [
        {
          value: '取消',
          action: () => {

          }
        },
        {
          value: '确定',
          action: () => {
            this.onKick()
          }
        }
      ]
    })
  }
}